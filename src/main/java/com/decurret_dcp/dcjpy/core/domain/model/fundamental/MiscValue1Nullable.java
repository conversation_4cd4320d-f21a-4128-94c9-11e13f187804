package com.decurret_dcp.dcjpy.core.domain.model.fundamental;

import org.seasar.doma.Domain;
import org.web3j.abi.datatypes.generated.Bytes32;

import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

import lombok.Value;

@Domain(valueType = String.class)
@Value
public class MiscValue1Nullable {

    String value;

    public MiscValue1Nullable(String value) {
        if (value != null) {
            Assertion.assertNotExceedByteLength(value, Bytes32.MAX_BYTE_LENGTH, "miscValue");
        }
        this.value = value;
    }
}
