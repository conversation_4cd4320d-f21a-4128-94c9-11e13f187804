package com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Bool;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Bytes32;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.Identified;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBoolValue;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class SetAccountIdentifyCommand extends BlockchainContractCommand {

    public SetAccountIdentifyCommand(IssuerId issuerId, AccountId accountId, Identified identified) {
        super(ContractName.ISSUER, "setAccountIdentify",
              Map.of("issuerId", new ContractBytes32Value(issuerId.getValue())
                      , "accountId", new ContractBytes32Value(accountId.getValue())
                      , "identified", new ContractBoolValue(identified.isValue())));
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                new Bytes32(((ContractBytes32Value) getArgs().get("issuerId")).asBytes32()),
                new Bytes32(((ContractBytes32Value) getArgs().get("accountId")).asBytes32()),
                new Bool(((ContractBoolValue) getArgs().get("identified")).getValue())
        );
    }
}
