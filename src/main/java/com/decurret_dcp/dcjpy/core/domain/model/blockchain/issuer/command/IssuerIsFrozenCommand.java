package com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class IssuerIsFrozenCommand extends BlockchainContractCommand {

    public IssuerIsFrozenCommand(IssuerId issuerId, AccountId accountId) {
        super(ContractName.ISSUER, "isFrozen",
              Map.ofEntries(
                      Map.entry("issuerId", new ContractBytes32Value(issuerId.getValue())),
                      Map.entry("accountId", new ContractBytes32Value(accountId.getValue())),
                      Map.entry("traceId", new ContractBytes32Value(TraceIdThreadLocalHolder.getTraceId().getValue()))
              )
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                // 署名不要のため空のまま
        );
    }
}
