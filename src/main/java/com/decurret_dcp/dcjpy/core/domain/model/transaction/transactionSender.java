package com.decurret_dcp.dcjpy.core.domain.model.transaction;

import org.seasar.doma.Entity;
import org.seasar.doma.Table;
import org.seasar.doma.jdbc.entity.NamingType;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionId;

import lombok.Builder;

@Entity(immutable = true, naming = NamingType.SNAKE_LOWER_CASE)
@Table(name = "transaction_sender")
@Builder
public class transactionSender {
    /** トランザクションID */
    public final TransactionId transactionId;

    /** 送金指示者アカウントID */
    public final AccountId sendAccountId;
}
