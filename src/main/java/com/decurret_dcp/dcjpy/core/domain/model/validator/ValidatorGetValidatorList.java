package com.decurret_dcp.dcjpy.core.domain.model.validator;

import java.util.Collections;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainCallErrorException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.GetValidatorListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.error.ContractError;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount;
import com.decurret_dcp.dcjpy.core.domain.model.validator.result.ValidatorGetValidatorListResult;

@Component
public class ValidatorGetValidatorList {

    public ValidatorGetValidatorListResult execute(
            Offset offset, Limit limit, CallBlockchainContractService bcService
    ) {
        GetValidatorListCommand command = new GetValidatorListCommand(offset, limit);
        BlockchainCallResponse response = bcService.executeWithoutSignature(command);

        if (response.isSuccess() == false) {
            String errStr = ContractError.substrErrorCode(response.getError());
            if (ContractError.VALID_OFFSET_OUT_OF_INDEX.getErrorCode().equals(errStr)) {
                return ValidatorGetValidatorListResult.builder()
                        .validators(Collections.emptyList())
                        .totalCount(new TotalCount(0L))
                        .build();
            }

            throw new BlockchainCallErrorException(ContractError.substrErrorCode(response.getError()));
        }

        // 正常時のレスポンス
        try {
            return ValidatorGetValidatorListResult.convert(response.data);
        } catch (Exception exc) {
            throw new BlockchainCallErrorException("response conversion failed", exc);
        }
    }
}
