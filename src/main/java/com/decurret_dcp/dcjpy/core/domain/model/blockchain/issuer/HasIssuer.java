package com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer;

import java.util.function.Consumer;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainCallErrorException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.HasIssuerCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.error.ContractError;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

@Component
public class HasIssuer {

    /**
     * イシュアの存在チェックを行います
     *
     * @param issuerId イシュアID
     * @param consumer コンシューマ
     * @param bcService　callBlockchainContractService
     * @throws BadRequestException イシュアの存在チェックに失敗した場合
     *     E0017 issuer_idが見つかりません
     *     E0016 issuer_idが無効です ※引数のchkEnabledがfalseの場合、このエラーコードが返却されることはない
     * @throws BlockchainCallErrorException 上記に記載以外の例外の場合
     */
    public void execute(IssuerId issuerId, Consumer<ContractError> consumer, CallBlockchainContractService bcService)
            throws BadRequestException, BlockchainCallErrorException {

        HasIssuerCommand command = new HasIssuerCommand(issuerId);
        BlockchainCallResponse response = bcService.executeWithoutSignature(command);

        if (response.isSuccess()) {
            return;
        }

        String errStr = response.getError();
        ContractError contractError = ContractError.getContractError(errStr);
        if (contractError != null) {
            consumer.accept(contractError);
        }

        throw new BlockchainCallErrorException(errStr);
    }

    public static class RequestSwitch implements Consumer<ContractError> {

        @Override
        public void accept(ContractError error) {
            switch (error) {
                case ISSUER_INVALID_VALUE:
                case ISSUER_ID_NOT_EXIST:
                    throw new BadRequestException(MessageCode.ISSUER_ID_NOT_FOUND);
                case ISSUER_DISABLED:
                    throw new BadRequestException(MessageCode.ISSUER_ID_NOT_ENABLED);
                default:
            }
        }
    }

    public static class PathValueSwitch implements Consumer<ContractError> {

        @Override
        public void accept(ContractError error) {
            switch (error) {
                case ISSUER_INVALID_VALUE:
                case ISSUER_ID_NOT_EXIST:
                    throw new NotFoundException(MessageCode.ISSUER_ID_NOT_FOUND);
                case ISSUER_DISABLED:
                    throw new BadRequestException(MessageCode.ISSUER_ID_NOT_ENABLED);
                default:
            }
        }
    }
}
