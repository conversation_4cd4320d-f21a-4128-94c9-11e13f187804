package com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Uint16;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractIntValue;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

public class AddBizZoneToIssuerCommand extends BlockchainContractCommand {

    public AddBizZoneToIssuerCommand(IssuerId issuerId, ZoneId zoneId) {
        super(ContractName.ISSUER, "addBizZoneToIssuer",
              Map.ofEntries(
                      Map.entry("issuerId", new ContractBytes32Value(issuerId.getValue())),
                      Map.entry("zoneId", new ContractIntValue(zoneId.getValue())),
                      Map.entry("traceId", new ContractBytes32Value(TraceIdThreadLocalHolder.getTraceId().getValue()))
              )
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                new Bytes32(((ContractBytes32Value) getArgs().get("issuerId")).asBytes32()),
                new Uint16(((ContractIntValue) getArgs().get("zoneId")).asBigInteger())
        );
    }
}
