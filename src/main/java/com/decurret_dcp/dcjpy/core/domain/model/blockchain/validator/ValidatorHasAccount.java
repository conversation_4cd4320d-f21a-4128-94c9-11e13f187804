package com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator;

import java.util.function.BiConsumer;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainCallErrorException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.ValidatorHasAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.error.ContractError;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class ValidatorHasAccount {

    /**
     * バリデータに紐づくアカウントの存在チェックを行います
     *
     * @param validatorId バリデータID
     * @param accountId アカウントID
     * @param accountIdNotExistMessage アカウントが存在しない場合のエラーコード
     * @param biConsumer biConsumer
     * @param bcService　callBlockchainContractService
     * @throws BadRequestException イシュアに紐づくアカウントの取得に失敗した場合
     *     引数のaccountIdNotExistMessageで指定したエラーコード
     * @throws NotFoundException イシュアに紐づくアカウントの取得に失敗した場合
     *     引数のaccountIdNotExistMessageで指定したエラーコード
     * @throws BlockchainCallErrorException 上記の例外以外の場合
     */
    public void execute(
            ValidatorId validatorId,
            AccountId accountId,
            MessageCode accountIdNotExistMessage,
            BiConsumer<ContractError, MessageCode> biConsumer, CallBlockchainContractService bcService
    ) throws BadRequestException, NotFoundException, BlockchainCallErrorException {

        ValidatorHasAccountCommand command = new ValidatorHasAccountCommand(validatorId, accountId);
        BlockchainCallResponse response = bcService.executeWithoutSignature(command);

        if (response.isSuccess()) {
            return;
        }

        String errStr = response.getError();
        ContractError contractError = ContractError.getContractError(errStr);
        if (contractError != null) {
            biConsumer.accept(contractError, accountIdNotExistMessage);
        }

        throw new BlockchainCallErrorException(errStr);
    }

    public static class PathValueSwitch implements BiConsumer<ContractError, MessageCode> {
        @Override
        public void accept(ContractError t, MessageCode accountIdNotExistMessage) {
            if (t == ContractError.ACCOUNT_ID_NOT_EXIST) {
                throw new NotFoundException(accountIdNotExistMessage);
            }
        }
    }
}
