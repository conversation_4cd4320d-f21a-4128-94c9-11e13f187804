package com.decurret_dcp.dcjpy.core.domain.model.validator;

import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerIdNullable;
import com.decurret_dcp.dcjpy.core.domain.model.validator.result.ValidatorGetValidatorListResult;

import lombok.Builder;

@Builder
public class Validator {

    public final ValidatorId validatorId;

    public final ValidatorName name;

    public final IssuerIdNullable issuerId;

    public static Validator create(ValidatorGetValidatorListResult.Validator validator) {
        return Validator.builder()
                .validatorId(validator.validatorId.validatorId())
                .name(new ValidatorName(validator.name.getValue()))
                .issuerId(validator.issuerId.issuerId())
                .build();
    }
}
