package com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractIntValue;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractStringValue;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.fundamental.SortOrder;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class RenewableEnergyTokenGetTokenListCommand extends BlockchainContractCommand {

    public RenewableEnergyTokenGetTokenListCommand(
            ValidatorId validatorId, AccountId accountId, Offset offset, Limit limit, SortOrder sortOrder
    ) {
        super(ContractName.RENEWABLE_ENERGY_TOKEN, "getTokenList",
              Map.ofEntries(
                      Map.entry("validatorId", new ContractBytes32Value(validatorId.getValue())),
                      Map.entry("accountId", new ContractBytes32Value(accountId.getValue())),
                      Map.entry("offset", new ContractIntValue(offset.asBigInteger())),
                      Map.entry("limit", new ContractIntValue(limit.asBigInteger())),
                      Map.entry("sortOrder", new ContractStringValue(sortOrder.name().toLowerCase()))
              )
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                // 署名不要のため空のまま
        );
    }
}
