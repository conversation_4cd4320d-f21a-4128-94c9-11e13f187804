package com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Uint16;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractIntValue;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractStringValue;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class CheckSyncAccountCommand extends BlockchainContractCommand {

    public CheckSyncAccountCommand(
            ValidatorId validatorId, AccountId accountId, ZoneId zoneId, AccountStatus accountStatus,
            Signature accountSignature, Info info
    ) {
        super(ContractName.FINANCIAL_CHECK, "checkSyncAccount",
              Map.ofEntries(
                      Map.entry("validatorId", new ContractBytes32Value(validatorId.getValue())),
                      Map.entry("accountId", new ContractBytes32Value(accountId.getValue())),
                      Map.entry("zoneId", new ContractIntValue(BigInteger.valueOf(Long.valueOf(zoneId.getValue())))),
                      Map.entry("accountStatus", new ContractBytes32Value(accountStatus.getValue())),
                      Map.entry("accountSignature", new ContractStringValue(accountSignature.getValue())),
                      Map.entry("info", new ContractStringValue(info.getValue()))
              )
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                new Bytes32(new ContractBytes32Value("checkSyncAccount").asBytes32())
        );
    }
}
