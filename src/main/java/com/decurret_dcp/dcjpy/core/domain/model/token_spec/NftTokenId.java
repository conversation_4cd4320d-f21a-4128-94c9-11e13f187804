package com.decurret_dcp.dcjpy.core.domain.model.token_spec;

import org.seasar.doma.Domain;

import com.decurret_dcp.dcjpy.core.domain.model.fundamental.IdGenerator;

import lombok.Value;

@Domain(valueType = String.class)
@Value
public class NftTokenId {

    private static final String PREFIX = "31";

    public final String value;

    public NftTokenId(String value) {
        this.value = value;
    }

    public static NftTokenId of(String nftTokenId) {
        return new NftTokenId(nftTokenId);
    }

    public String getValue() {
        return this.value;
    }

    public static NftTokenId generate() {
        return new NftTokenId(IdGenerator.generate(PREFIX));
    }
}
