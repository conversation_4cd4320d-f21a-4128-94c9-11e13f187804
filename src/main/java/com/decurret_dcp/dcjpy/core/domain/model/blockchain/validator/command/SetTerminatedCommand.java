package com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractStringValue;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class SetTerminatedCommand extends BlockchainContractCommand {

    public SetTerminatedCommand(ValidatorId validatorId, AccountId accountId, ReasonCode reasonCode) {
        super(ContractName.VALIDATOR, "setTerminated",
              Map.ofEntries(
                      Map.entry("validatorId", new ContractBytes32Value(validatorId.getValue())),
                      Map.entry("accountId", new ContractBytes32Value(accountId.getValue())),
                      Map.entry("accountStatus", new ContractBytes32Value(AccountStatus.TERMINATED.getValue())),
                      Map.entry("reasonCode", new ContractBytes32Value(reasonCode.getValue())),
                      Map.entry("traceId", new ContractBytes32Value(TraceIdThreadLocalHolder.getTraceId().getValue()))
              )
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                // 署名不要のため空のまま
        );
    }
}
