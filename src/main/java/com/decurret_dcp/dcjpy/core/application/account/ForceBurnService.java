package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.command.ForceBurnCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.ForceBurnResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerIsFrozen;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.PartialForceBurnCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerIsFrozenResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.GetBalanceList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.result.TokenGetBalanceListResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.Builder;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ForceBurnService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final IssuerIsFrozen issuerIsFrozen;
    private final IssuerGetAccount getAccount;
    private final GetBalanceList getBalanceList;
    private final BalanceCacheRepository balanceCacheRepository;

    private static enum PartialForceBurnCommandOption {
        WITHIN_FIN_ZONE_BALANCE {
            @Override
            Balance getForceBurnedBalance(Balance totalBalance, Balance finZoneBalance, Amount burnAmount) {
                Balance burned = new Balance(burnAmount.value);
                return finZoneBalance.subtract(burned);
            }

            @Override
            Amount getDelta(Balance finZoneBalance, Balance burnedBalance) {
                Balance delta = finZoneBalance.subtract(burnedBalance);
                return new Amount(delta.value);
            }
        },
        OVER_FIN_ZONE_BALANCE {
            @Override
            Balance getForceBurnedBalance(Balance totalBalance, Balance finZoneBalance, Amount burnAmount) {
                Balance burned = new Balance(burnAmount.value);
                return totalBalance.subtract(burned);
            }

            @Override
            Amount getDelta(Balance finZoneBalance, Balance burnedBalance) {
                Balance delta = burnedBalance.subtract(finZoneBalance);
                return new Amount(delta.value);
            }
        };

        abstract Balance getForceBurnedBalance(Balance totalBalance, Balance finZoneBalance, Amount burnAmount);

        abstract Amount getDelta(Balance bizZoneTotalBalance, Balance burnedBalance);
    }

    @Builder
    private static class PartialForceBurnBalance {

        final ZoneId zoneId;

        final Balance totalBalance;

        final Balance finZoneBalance;
    }

    /**
     * 強制償却のチェックを行います
     * @param command 強制償却コマンド
     * @return チェック結果
     */
    public ForceBurnResult check(ForceBurnCommand command) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // FinZone ではない場合はチャージを実行できない
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // アカウントのステータス確認
        String accountName = this.checkAccountStatus(command.issuerId, command.accountId);

        // 残高確認
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.accountId, zoneId);
        Balance finZoneBalance = balanceCache.balance;

        this.checkBalance(command, finZoneBalance);

        return ForceBurnResult.builder()
                .accountId(command.accountId)
                .accountName(accountName)
                .burnAmount(command.burnAmount)
                .build();
    }

    /**
     * 強制償却を実行します
     * @param command 強制償却コマンド
     * @return 実行結果
     */
    public ForceBurnResult execute(ForceBurnCommand command) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // FinZone ではない場合は償却を実行できない
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // ステータスチェック
        String accountName = this.checkAccountStatus(command.issuerId, command.accountId);

        // 残高確認
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.accountId, zoneId);
        Balance finZoneBalance = balanceCache.balance;

        Balance totalBalance = this.checkBalance(command, finZoneBalance);

        // 強制償却実行
        EntityId entityId = new EntityId(command.issuerId);
        com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.ForceBurnCommand burnCommand =
                new com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.ForceBurnCommand(
                        command.issuerId, command.accountId);
        sendBlockchainContractService.execute(burnCommand, entityId);

        // 残高キャッシュ減算
        BalanceCache afterBalanceCache
                = this.balanceCacheRepository.subtractBalanceCache(command.accountId,
                                                                   zoneId, new Amount(finZoneBalance.value));

        return ForceBurnResult.builder()
                .accountId(command.accountId)
                .accountName(accountName)
                .balance(afterBalanceCache.balance)
                .burnAmount(new Amount(totalBalance.value))
                .build();
    }

    public ForceBurnResult executePartialBurn(ForceBurnCommand command) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // FinZone ではない場合は償却を実行できない
        if (!zoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // ステータスチェック
        String accountName = this.checkAccountStatus(command.issuerId, command.accountId);

        // 残高確認
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.accountId, zoneId);
        Balance finZoneBalance = balanceCache.balance;

        Balance totalBalance = this.checkBalance(command, finZoneBalance);

        // 一部強制償却実行
        PartialForceBurnBalance partialBurnBalance = PartialForceBurnBalance.builder()
                .zoneId(zoneId)
                .totalBalance(totalBalance)
                .finZoneBalance(finZoneBalance)
                .build();

        Balance burnedBalance = this.doExecutePartialBurn(command, partialBurnBalance);
        return ForceBurnResult.builder()
                .accountId(command.accountId)
                .accountName(accountName)
                .balance(burnedBalance)
                .burnAmount(command.burnAmount)
                .build();
    }

    private Balance doExecutePartialBurn(ForceBurnCommand command, PartialForceBurnBalance partialBurnBalance) {
        Balance burn = new Balance(command.burnAmount.value);
        if (burn.isLessThanEqual(partialBurnBalance.finZoneBalance)) {
            return this.doExecutePartialBurnWithOption(
                    command, partialBurnBalance, PartialForceBurnCommandOption.WITHIN_FIN_ZONE_BALANCE);
        }

        return this.doExecutePartialBurnWithOption(
                command, partialBurnBalance, PartialForceBurnCommandOption.OVER_FIN_ZONE_BALANCE);
    }

    private Balance doExecutePartialBurnWithOption(ForceBurnCommand command, PartialForceBurnBalance partialBurnBalance,
                                                   PartialForceBurnCommandOption option) {
        // 一部強制償却実施
        EntityId entityId = new EntityId(command.issuerId);
        Balance burnedBalance = option.getForceBurnedBalance(
                partialBurnBalance.totalBalance, partialBurnBalance.finZoneBalance, command.burnAmount);
        PartialForceBurnCommand partialBurnCommand = new PartialForceBurnCommand(command, burnedBalance);
        sendBlockchainContractService.execute(partialBurnCommand, entityId);

        switch (option) {
            case WITHIN_FIN_ZONE_BALANCE -> {
                Amount subtractAmount = option.getDelta(partialBurnBalance.finZoneBalance, burnedBalance);
                this.balanceCacheRepository.subtractBalanceCache(
                        command.accountId, partialBurnBalance.zoneId, subtractAmount);
            }
            case OVER_FIN_ZONE_BALANCE -> {
                Amount addAmount = option.getDelta(partialBurnBalance.finZoneBalance, burnedBalance);
                this.balanceCacheRepository.addBalanceCache(command.accountId, partialBurnBalance.zoneId, addAmount);
            }
        }
        return burnedBalance;
    }

    private String checkAccountStatus(IssuerId issuerId, AccountId accountId) {
        // ステータスチェック
        IssuerIsFrozenResult isFrozenResult
                = issuerIsFrozen.execute(issuerId, accountId, callBlockchainContractService);
        if (isFrozenResult.frozen == false) {
            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
        }

        // アカウント名取得
        IssuerGetAccountResult getAccountResult
                = getAccount.execute(issuerId, accountId, callBlockchainContractService);

        return getAccountResult.accountName;
    }

    private Balance checkBalance(ForceBurnCommand command, Balance finZoneBalance) {

        // BizZoneの残高取得
        TokenGetBalanceListResult tokenGetBalanceListResult
                = getBalanceList.execute(command.accountId, callBlockchainContractService);

        Balance bizZoneBalance = tokenGetBalanceListResult.totalBalance;
        Balance allBalance = finZoneBalance.add(bizZoneBalance);

        if (command.isPartialBurn) {
            Balance burn = new Balance(command.burnAmount.value);
            doCheckBalance(allBalance.isLessThanEqual(burn));
            return allBalance;
        }

        doCheckBalance(allBalance.isZero());
        return allBalance;
    }

    private void doCheckBalance(boolean lacking) {
        if (lacking) {
            throw new BadRequestException(MessageCode.BALANCE_NOT_ENOUGH);
        }
    }
}
