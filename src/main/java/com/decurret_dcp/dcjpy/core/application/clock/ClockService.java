package com.decurret_dcp.dcjpy.core.application.clock;

import java.time.Clock;
import java.time.Instant;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Service;
import org.springframework.web.context.annotation.RequestScope;

@Service
@RequestScope
public class ClockService {

    private Clock clock;

    @PostConstruct
    public void init() {
        clock = Clock.systemDefaultZone();
    }

    public Instant instant() {
        return Instant.now(clock);
    }

    public void setClock(Clock clock) {
        this.clock = clock;
    }
}
