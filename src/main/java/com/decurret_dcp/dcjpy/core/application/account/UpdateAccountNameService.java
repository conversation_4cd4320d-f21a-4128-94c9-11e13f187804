package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.UpdateAccountNameServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.UpdateAccountNameResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command.SetAccountNameCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class UpdateAccountNameService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final ValidatorGetAccount getAccount;

    /**
     * アカウント名の更新を行います。
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException　アカウント名の更新に失敗。
     *     E0001 account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public UpdateAccountNameResult execute(UpdateAccountNameServiceCommand command) throws BadRequestException {

        //1.アカウントの存在チェック
        getAccount.execute(command.validatorId, command.accountId, callBlockchainContractService);

        //2.アカウント名の更新
        SetAccountNameCommand setAccountNameCommand = command.toSetAccountNameCommand();
        sendBlockchainContractService.execute(setAccountNameCommand, new EntityId(command.validatorId));

        return UpdateAccountNameResult.builder()
                .accountId(command.accountId)
                .accountName(command.accountName)
                .build();
    }
}
