package com.decurret_dcp.dcjpy.core.application.identity.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.AddAccountRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySigner;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKeyAndEoa;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class CreateAccountIdentityServiceCommand {
    public final IssuerId issuerId;
    public final AccountId accountId;

    public CreateAccountIdentityServiceCommand(IssuerId issuerId, AccountId accountId) {
        Assertion.assertNotNull(issuerId, "issuerId");
        Assertion.assertNotNull(accountId, "accountId");
        this.issuerId = issuerId;
        this.accountId = accountId;
    }

    public AddAccountRoleCommand toAddAccountRoleCommand(EntitySigner entitySigner) {
        return new AddAccountRoleCommand(
                this.issuerId,
                this.accountId,
                entitySigner.getEoa()
        );
    }
}
