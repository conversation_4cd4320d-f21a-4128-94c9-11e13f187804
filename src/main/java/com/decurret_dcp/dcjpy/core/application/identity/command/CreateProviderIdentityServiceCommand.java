package com.decurret_dcp.dcjpy.core.application.identity.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.AddProviderRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.Eoa;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class CreateProviderIdentityServiceCommand {

    public final AdminId adminId;

    public final ProviderId providerId;

    public AddProviderRoleCommand toAddProviderRoleCommand(Eoa eoa) {
        return new AddProviderRoleCommand(this.providerId, eoa);
    }
}
