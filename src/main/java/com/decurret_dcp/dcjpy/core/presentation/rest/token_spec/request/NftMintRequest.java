package com.decurret_dcp.dcjpy.core.presentation.rest.token_spec.request;

import java.util.Map;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring.annotation.IdConstraint;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.NftMintServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.RequestId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.user.CoreUserDetails;
import com.decurret_dcp.dcjpy.core.presentation.rest.fundamental.validation.groups.FirstValidation;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.Builder;

@Builder
public class NftMintRequest {

    @NotEmpty(groups = FirstValidation.class)
    @com.decurret_dcp.dcjpy.core.presentation.rest.fundamental.validation.annotation.RequestId
    public final String requestId;

    @NotEmpty(groups = FirstValidation.class)
    @IdConstraint
    public final String mintedAccountId;

    @NotEmpty(groups = FirstValidation.class)
    @IdConstraint
    public final String ownerAccountId;

    @NotNull
    public final Boolean locked;

    @NotNull
    public final Map<String, Object> metadataDetail;

    public NftMintServiceCommand initCommand(CoreUserDetails user, String nftId) {
        return NftMintServiceCommand.builder()
                .validatorId(user.getValidatorId())
                .nftId(nftId)
                .requestId(RequestId.of(this.requestId))
                .mintedAccountId(new AccountId(this.mintedAccountId))
                .ownerAccountId(new AccountId(this.ownerAccountId))
                .locked(this.locked)
                .metadataDetail(this.metadataDetail)
                .build();
    }
}
