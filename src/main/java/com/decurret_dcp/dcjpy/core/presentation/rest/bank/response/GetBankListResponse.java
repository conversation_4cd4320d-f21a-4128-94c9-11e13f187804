package com.decurret_dcp.dcjpy.core.presentation.rest.bank.response;

import java.util.List;

import com.decurret_dcp.dcjpy.core.application.bank.result.GetBankListResult;

import lombok.Builder;

@Builder
public class GetBankListResponse {

    public final List<GetBankResponse> banks;

    public final long offset;

    public final int limit;

    public final long total;

    public static GetBankListResponse initResponse(GetBankListResult getBankListResult, long offset, int limit) {
        List<GetBankResponse> banks = getBankListResult.bankList.stream()
                .map(GetBankListResponse::assembleGetBankResponse)
                .toList();

        return GetBankListResponse.builder()
                .banks(banks)
                .offset(offset)
                .limit(limit)
                .total(getBankListResult.totalCount.getValue())
                .build();
    }

    private static GetBankResponse assembleGetBankResponse(GetBankListResult.Bank bank) {
        return new GetBankResponse(
                bank.issuerId.getValue(),
                bank.bankCode.zeroPadding(),
                bank.bankName
        );
    }
}
