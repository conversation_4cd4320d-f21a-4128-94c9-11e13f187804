package com.decurret_dcp.dcjpy.core.presentation.rest.fundamental.validation.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

import org.hibernate.validator.constraints.Length;

import com.decurret_dcp.dcjpy.core.presentation.rest.fundamental.validation.groups.SecondValidation;

@Documented
@Constraint(validatedBy = {})
@Target({ ElementType.TYPE_USE, ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Length(min = 1, max = 128, groups = SecondValidation.class)
public @interface ZoneName {

    String message() default "Invalid format.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
