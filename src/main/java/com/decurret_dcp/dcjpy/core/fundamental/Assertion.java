package com.decurret_dcp.dcjpy.core.fundamental;

import java.nio.charset.StandardCharsets;

import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;
import com.google.common.base.Strings;

public class Assertion {

    private Assertion() {
    }

    /**
     * assert argument is not null
     * @param anObject
     *      assertion target
     * @param nameOfAnObject
     *      target's object name
     * @throws IllegalArgumentException
     *      if anObject is null
     */
    public static void assertNotNull(Object anObject, String nameOfAnObject) {
        if (anObject == null) {
            throw new BadRequestException(MessageCode.INVALID_FORMAT, nameOfAnObject);
        }
    }

    /**
     * assert argument is not null and empty
     * @param anObject
     *      assertion target
     * @param nameOfAnObject
     *      target's object name
     * @throws IllegalArgumentException
     *      if anObject is null or empty
     */
    public static void assertNotEmpty(String anObject, String nameOfAnObject) {
        if (Strings.isNullOrEmpty(anObject)) {
            throw new BadRequestException(MessageCode.INVALID_FORMAT, nameOfAnObject);
        }
    }

    /**
     * assert argument is not null and empty
     * @param anObject
     *      assertion target
     * @param byteLength
     *      assertion evaluation value
     * @param nameOfAnObject
     *      target's object name
     * @throws BadRequestException
     *      if length of anObject exceed byteLength
     */
    public static void assertNotExceedByteLength(String anObject, int byteLength, String nameOfAnObject) {

        assertNotNull(anObject, nameOfAnObject);
        if (anObject.getBytes(StandardCharsets.UTF_8).length > byteLength) {
            throw new BadRequestException(MessageCode.INVALID_FORMAT, nameOfAnObject);
        }
    }

    /**
     * assert argument value between min and max
     * @param value
     *      assertion target
     * @param min
     *      assertion evaluation min value
     * @param max
     *      assertion evaluation max value
     * @param nameOfValue
     *      target's object name
     * @throws BadRequestException
     *      if value not in between min and max
     */
    public static void assertBetween(long value, long min, long max, String nameOfValue) {
        if (value < min || value > max) {
            throw new BadRequestException(MessageCode.INVALID_FORMAT, nameOfValue);
        }
    }
}
