package com.decurret_dcp.dcjpy.bpm.server.domain.exception;

import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;

public class SignInForbiddenForUserStatusException extends BpmServerException {

    public SignInForbiddenForUserStatusException(String message) {
        super(message);
    }

    public SignInForbiddenForUserStatusException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public MessageCode getMessageCode() {
        return MessageCode.SIGN_IN_FAILURE_USER_STATUS_INVALID;
    }
}
