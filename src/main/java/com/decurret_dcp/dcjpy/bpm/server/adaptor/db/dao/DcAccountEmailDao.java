package com.decurret_dcp.dcjpy.bpm.server.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Insert;
import org.seasar.doma.Select;
import org.seasar.doma.Update;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

import com.decurret_dcp.dcjpy.bpm.server.domain.entity.DcAccountEmailEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.ExistsDcAccountCondition;

@ConfigAutowireable
@Dao
public interface DcAccountEmailDao {

    @Select
    public boolean exists(ExistsDcAccountCondition condition);

    @Insert
    public Result<DcAccountEmailEntity> insert(DcAccountEmailEntity entity);

    @Update
    public Result<DcAccountEmailEntity> update(DcAccountEmailEntity entity);
}
