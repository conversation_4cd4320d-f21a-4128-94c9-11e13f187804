package com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.command;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.Signature;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.RequestId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ValidatorId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class CoreApiBizTerminatingCommand {

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final RequestId requestId;

    public final Signature signature;

    public final String reasonCode;
}
