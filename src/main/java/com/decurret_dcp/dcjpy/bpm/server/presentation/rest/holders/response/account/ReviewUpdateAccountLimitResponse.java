package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.response.account;

import com.decurret_dcp.dcjpy.bpm.server.application.holders.result.ReviewDcUserOrderResult;
import com.decurret_dcp.dcjpy.bpm.server.application.sharers.result.UpdateAccountLimitResult;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.response.ReviewResultResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.LimitMaximumResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.accounts.UpdateAccountResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmDcUserDetail;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ReviewUpdateAccountLimitResponse {

    public final ReviewResultResponse reviewResult;

    public final String qrToken;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public final UpdateAccountResponse<LimitMaximumResponse> accountLimitUpdatedDetail;

    public static ReviewUpdateAccountLimitResponse create(
            BpmDcUserDetail userDetail, ReviewDcUserOrderResult<UpdateAccountLimitResult> result
    ) {
        ReviewResultResponse reviewResult = ReviewResultResponse.create(userDetail, result);
        String qrToken = (result.qrKey != null) ? result.qrKey.withPrefix() : null;
        UpdateAccountResponse<LimitMaximumResponse> detail = UpdateAccountResponse.create(result.detailResult);

        return new ReviewUpdateAccountLimitResponse(reviewResult, qrToken, detail);
    }
}
