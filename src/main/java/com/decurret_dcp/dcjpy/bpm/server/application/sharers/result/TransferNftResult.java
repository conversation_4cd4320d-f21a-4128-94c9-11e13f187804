package com.decurret_dcp.dcjpy.bpm.server.application.sharers.result;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.NftTokenId;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@EqualsAndHashCode
@Builder
public class TransferNftResult {

    public final NftTokenId nftTokenId;

    public final DcBankNumber fromDcBankNumber;

    public final String fromAccountName;

    public final DcBankNumber toDcBankNumber;

    public final String toAccountName;

}
