package com.decurret_dcp.dcjpy.bpm.server.application.holders;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.bpm.server.application.holders.command.TransferCommand;
import com.decurret_dcp.dcjpy.bpm.server.application.holders.exception.TransferFailureException;
import com.decurret_dcp.dcjpy.bpm.server.application.holders.result.OrderTransferResult;
import com.decurret_dcp.dcjpy.bpm.server.application.holders.result.ReviewDcUserOrderResult;
import com.decurret_dcp.dcjpy.bpm.server.application.holders.result.TransferResult;
import com.decurret_dcp.dcjpy.bpm.server.application.services.exception.GetValidatorAccountException;
import com.decurret_dcp.dcjpy.bpm.server.application.sharers.command.AcceptQrOperationCommand;
import com.decurret_dcp.dcjpy.bpm.server.config.BpmAuthenticatorAppProperty;
import com.decurret_dcp.dcjpy.bpm.server.domain.IdResolver;
import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.CoreAccountsApi;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.CoreActorInfo;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.CoreActorSupport;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.CoreTransactionsApi;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.CoreValidatorsApi;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.command.CoreApiTransferCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiCheckTransferResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiFindAccountDetailResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiGetValidatorAccountResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiTransferResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.DcUserOrderEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.QrOperationEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ServiceOwnerEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.DcUserRepository;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.QrOperationRepository;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.TransactionSupport;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.DcUserOrderCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.FindDcAccountCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.RegisterQrConfirmationCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.ReviewDcUserOrderCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.AccountSigner;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.DcUserOrder;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.MessageNotifier;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.command.CreateAccountSignatureCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.command.PublishDcUserCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.result.PrepareReviewOrder;
import com.decurret_dcp.dcjpy.bpm.server.domain.signer.result.SignerSignResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.AuthenticatorMode;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.DcAccountSummary;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.DcUserWithAccount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Either;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Tuple;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OrderStatus;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OrderType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OwnerType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.QrConfirmationType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.order.TransferOrderDetail;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.qr.TransferOperationDetail;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.sign.TransferSignatureDetail;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * DCJPY 送金に関するアプリケーションクラス。
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TransferApplication {

    private final BpmAuthenticatorAppProperty property;

    private final DcUserOrder dcUserOrder;

    private final MessageNotifier messageNotifier;

    private final AccountSigner accountSigner;

    /** トランザクションのヘルパークラス */
    private final TransactionSupport transactionSupport;

    private final DcUserReviewSupport dcUserReviewSupport;

    private final CoreActorSupport coreActorSupport;

    private final CoreAccountsApi coreAccountsApi;

    /** コアの送金関係のAPI実行 */
    private final CoreTransactionsApi coreTransactionsApi;

    private final CoreValidatorsApi coreValidatorsApi;

    private final QrOperationRepository qrOperationRepository;

    private final DcUserRepository dcUserRepository;

    /**
     * 移転のチェックを行う。
     *
     * @param command 移転コマンド
     *
     * @return 移転結果
     */
    public CoreApiCheckTransferResult checkTransfer(TransferCommand command) {
        // 1. 移転元DC口座番号と移転先のDC口座番号が同じ場合はエラーとする。
        // 2. DC口座テーブルから送金元のアカウントIDを取得する
        // 3. サービスオーナーテーブルからバリデータIDを取得する
        // 4. 送金先のDC口座番号より、送金先のアカウントIDを生成する
        // 5. CoreのAPI「FinZoneコイン送金／BizZoneコイン移転前確認」を呼び出す。
        Tuple<CoreApiCheckTransferResult, CoreActorInfo> result = this.doCheckTransfer(command);
        return result.first;
    }

    private Tuple<CoreApiCheckTransferResult, CoreActorInfo> doCheckTransfer(TransferCommand command) {

        // 1. 移転元DC口座番号と移転先のDC口座番号が同じ場合はエラーとする。
        if (command.fromDcBankNumber.equals(command.toDcBankNumber)) {
            throw TransferFailureException.fromBpm(MessageCode.SAME_ACCOUNT_TRANSFER);
        }

        // バリデータIDを取得する。
        // 2. DC口座テーブルから送金元のアカウントIDを取得する
        // 3. サービスオーナーテーブルからバリデータIDを取得する
        FindDcAccountCommand findCommand = command.initFindDcAccountCommand();
        CoreActorInfo ownerInfo = this.transactionSupport.transaction(
                () -> this.coreActorSupport.findActor(findCommand)
        );

        // 4. 送金先のDC口座番号より、送金先のアカウントIDを生成する (TransferCommand コマンド内で作成済み)
        // 5. アカウント署名を作成する
        // 6. CoreのAPI「FinZoneコイン送金／BizZoneコイン移転前確認」を呼び出す。
        CoreApiTransferCommand coreCommand = this.buildTransferCommand(ownerInfo, command);
        Either<CoreApiCheckTransferResult> checkResult = this.coreTransactionsApi.checkTransfer(coreCommand);

        if (checkResult.isSuccess() == false) {
            throw TransferFailureException.fromCoreApiCheckTransfer(checkResult);
        }

        return Tuple.of(checkResult.success(), ownerInfo);
    }

    private CoreApiTransferCommand buildTransferCommand(CoreActorInfo ownerInfo, TransferCommand command) {
        if (command.sendDcBankNumber != null) {
            CreateAccountSignatureCommand<TransferSignatureDetail> signatureCommand = command.initSignerCommand();
            SignerSignResult signerSign = this.accountSigner.createAccountSignature(signatureCommand);

            return command.initCoreAPiCommand(ownerInfo, signerSign.signature);
        }

        // sendDcBankNumber が未指定の場合は、バリデータに紐づくアカウントを指定する
        Either<CoreApiGetValidatorAccountResult> either =
                this.coreValidatorsApi.getValidatorAccount(ownerInfo.validatorId());
        if (either.isSuccess() == false) {
            throw GetValidatorAccountException.fromCoreApi(either);
        }

        CoreApiGetValidatorAccountResult valildatorAccount = either.success();
        return command.initCoreAPiCommand(ownerInfo, valildatorAccount);
    }

    /**
     * 承認フローなしの DCJPY 移転手続きを行う。
     * 認証アプリが有効な場合は、手続き用のQRコード情報を返却し、認証アプリが無効の場合は、DCJPY 移転を実施する。
     *
     * @param command 移転コマンド
     *
     * @return 実施結果
     */
    public ReviewDcUserOrderResult<TransferResult> transferDirect(TransferCommand command) {
        AuthenticatorMode authenticatorMode = this.property.company;
        if (authenticatorMode != AuthenticatorMode.MUST_USE) {
            TransferResult result = this.transfer(command);

            return ReviewDcUserOrderResult.<TransferResult>builder()
                    .detailResult(result)
                    .build();
        }

        Tuple<CoreApiCheckTransferResult, CoreActorInfo> checkResult = this.doCheckTransfer(command);
        CoreApiCheckTransferResult checkTransferResult = checkResult.first;

        // QR操作テーブルへデータを登録する
        DcAccountSummary toAccount = DcAccountSummary.builder()
                .dcBankNumber(command.toDcBankNumber)
                .accountName(checkTransferResult.toAccountName)
                .build();
        TransferOperationDetail operationDetail = TransferOperationDetail.builder()
                .confirmationType(QrConfirmationType.TRANSFER)
                .transferAmount(command.transferAmount)
                .sendDcBankNumber(command.sendDcBankNumber)
                .fromDcBankNumber(command.fromDcBankNumber)
                .toAccount(toAccount)
                .memo(command.memo)
                .miscValue(command.miscValue)
                .requestId(command.requestId)
                .build();

        RegisterQrConfirmationCommand<TransferOperationDetail> qrCommand =
                RegisterQrConfirmationCommand.<TransferOperationDetail>builder()
                        .serviceId(command.dcUser.serviceId)
                        .ownerType(OwnerType.USER)
                        .signInId(command.dcUser.signInId)
                        .operationDetail(operationDetail)
                        .build();

        return this.transactionSupport.transaction(
                () -> {
                    QrOperationEntity qrOperation = this.qrOperationRepository.registerConfirmation(qrCommand);

                    return ReviewDcUserOrderResult.<TransferResult>builder()
                            .qrKey(qrOperation.qrKey)
                            .build();
                }
        );
    }

    public TransferResult transfer(TransferCommand command) {
        // 1. 移転元DC口座番号と移転先のDC口座番号が同じ場合はエラーとする。
        if (command.fromDcBankNumber.equals(command.toDcBankNumber)) {
            throw TransferFailureException.fromBpm(MessageCode.SAME_ACCOUNT_TRANSFER);
        }

        // バリデータIDを取得する。
        // 2. DC口座テーブルから送金元のアカウントIDを取得する
        // 3. サービスオーナーテーブルからバリデータIDを取得する
        FindDcAccountCommand findCommand = command.initFindDcAccountCommand();
        CoreActorInfo ownerInfo = this.transactionSupport.transaction(
                () -> this.coreActorSupport.findActor(findCommand)
        );

        // 送金元のアカウント名を取得する為に「口座詳細取得」を呼び出す
        CoreApiFindAccountDetailResult fromAccount =
                this.doFindAccountFromCore(ownerInfo.accountId(), ownerInfo.serviceOwner());

        // 4. 送金先のDC口座番号より、送金先のアカウントIDを生成する (TransferCommand コマンド内で作成済み)
        // 5. アカウント署名を作成する
        // 6. CoreのAPI「FinZoneコイン送金／BizZoneコイン移転」を呼び出す。
        CoreApiTransferCommand coreCommand = this.buildTransferCommand(ownerInfo, command);
        Either<CoreApiTransferResult> either = this.coreTransactionsApi.transfer(command.requestId, coreCommand);

        //エラーが発生した場合は内容に応じてエラーを返却する
        if (either.isSuccess() == false) {
            throw TransferFailureException.fromCoreApiTransfer(either);
        }

        CoreApiTransferResult transferResult = either.success();

        // 6. 送金先アカウントIDをDC口座番号に変換する
        DcBankNumber toDcBankNumber = IdResolver.toDcBankNumber(transferResult.toAccountId);

        return TransferResult.initResult(fromAccount, command.fromDcBankNumber, either.success(), toDcBankNumber);
    }

    public OrderTransferResult orderTransfer(DcUserWithAccount dcUser, TransferCommand command) {
        // 1. 移転元DC口座番号と移転先のDC口座番号が同じ場合はエラーとする。
        // 2. DC口座テーブルから送金元のアカウントIDを取得する
        // 3. サービスオーナーテーブルからバリデータIDを取得する
        // 4. 送金先のDC口座番号より、送金先のアカウントIDを生成する
        // 5. CoreのAPI「FinZoneコイン送金／BizZoneコイン移転前確認」を呼び出す。
        Tuple<CoreApiCheckTransferResult, CoreActorInfo> result = this.doCheckTransfer(command);

        CoreApiCheckTransferResult checkResult = result.first;
        ValidatorId validatorId = result.second.validatorId();
        AccountId accountId = result.second.accountId();

        // 申請時は自身のアカウント名が必要なため、Core より取得する
        Either<CoreApiFindAccountDetailResult> either = this.coreAccountsApi.findAccount(validatorId, accountId);
        // 自身のアカウントが取得できない場合はシステムエラーとする。
        if (either.isSuccess() == false) {
            throw either.toUnexpectedException();
        }

        // 6. DCユーザ申請テーブルへ登録する。
        DcUserOrderCommand<TransferOrderDetail> orderCommand =
                command.initOrderCommand(dcUser, either.success(), checkResult);
        DcUserOrderEntity order = this.transactionSupport.transaction(
                () -> this.dcUserOrder.order(orderCommand)
        );

        // 7. 依頼通知を行う
        PublishDcUserCommand publishCommand = command.initPublishCommand(order);
        this.messageNotifier.publishMessage(publishCommand);

        return OrderTransferResult.create(order.orderId, command.toDcBankNumber, checkResult);
    }

    /**
     * 送金の承認受付を行う。
     *
     * @param command 申請承認/否認コマンド
     *
     * @return 手続き結果
     */
    public ReviewDcUserOrderResult<TransferResult> reviewTransfer(ReviewDcUserOrderCommand command) {
        PrepareReviewOrder<DcUserOrderEntity> prepare = this.transactionSupport.transaction(
                () -> this.dcUserOrder.prepareReview(command, OrderType.TRANSFER)
        );

        DcUserOrderEntity order = prepare.order;
        TransferOrderDetail transferOrderDetail = TransferOrderDetail.of(order.orderDetail);

        // リクエストの review_status が rejected の場合は終了
        if (prepare.orderStatus == OrderStatus.REJECTED) {

            // 認証アプリが有効の場合、否認通知を行う
            PublishDcUserCommand publishCommand = command.initPublishCommandForRejection(order, prepare.reasonCode);
            this.messageNotifier.publishMessage(publishCommand);

            return ReviewDcUserOrderResult.<TransferResult>builder()
                    .order(prepare.order)
                    .reasonCode(prepare.reasonCode)
                    .build();
        }

        // 認証アプリが有効か無効か確認
        AuthenticatorMode authenticatorMode = property.company;

        // 認証アプリが有効な場合、承認受付を実行
        if (authenticatorMode == AuthenticatorMode.MUST_USE) {
            return this.prepareAuthenticator(command, transferOrderDetail);
        }

        return this.doApproveTransfer(command, transferOrderDetail);
    }

    private ReviewDcUserOrderResult<TransferResult> prepareAuthenticator(
            ReviewDcUserOrderCommand command, TransferOrderDetail detail
    ) {
        return this.dcUserReviewSupport.prepareAuthenticator(command, () -> {
            // 3-2. CoreAPI にて送金可能かチェックする
            TransferCommand transferCommand = TransferCommand.fromOrder(command, detail);
            this.doCheckTransfer(transferCommand);

            return null;
        });
    }

    /**
     * 認証アプリの読み込み手続きによる、承認手続きを実施する。
     *
     * @param command 申請コマンド*
     *
     * @return 手続き結果
     */
    public TransferOperationDetail approveTransfer(
            ReviewDcUserOrderCommand command
    ) {
        PrepareReviewOrder<DcUserOrderEntity> prepare = this.transactionSupport.transaction(
                () -> this.dcUserOrder.prepareReviewForAuthenticator(command, OrderType.TRANSFER)
        );

        DcUserOrderEntity order = prepare.order;
        TransferOrderDetail transferOrderDetail = TransferOrderDetail.of(order.orderDetail);

        ReviewDcUserOrderResult<TransferResult> result = this.doApproveTransfer(command, transferOrderDetail);
        TransferOrderDetail orderDetail = TransferOrderDetail.of(order.orderDetail);

        return result.detailResult.toOperationDetail(orderDetail);
    }

    /**
     * 認証アプリの読み込み手続きによる、確定手続きを実施する。
     *
     * @param command 確定コマンド
     * @param qrOperation QRオペレーションテーブル
     *
     * @return 手続き結果
     */
    public TransferOperationDetail confirmTransfer(
            AcceptQrOperationCommand command, QrOperationEntity qrOperation
    ) {
        // QR操作テーブルから詳細情報を取得
        TransferOperationDetail transferDetail = TransferOperationDetail.of(qrOperation.operationDetail);

        // 実行者の情報を取得
        DcUserWithAccount dcUser = this.transactionSupport.transaction(
                () -> this.dcUserRepository.findUserWithAccount(command.signInId)
        );

        // 送金処理を実行
        TransferCommand transferCommand = TransferCommand.builder()
                .dcUser(dcUser)
                .serviceId(command.serviceId)
                .requestId(transferDetail.requestId)
                .sendDcBankNumber(transferDetail.sendDcBankNumber)
                .fromDcBankNumber(transferDetail.fromDcBankNumber)
                .toDcBankNumber(transferDetail.toAccount.dcBankNumber)
                .transferAmount(transferDetail.transferAmount)
                .memo(transferDetail.memo)
                .miscValue(transferDetail.miscValue)
                .build();

        TransferResult result = this.transfer(transferCommand);

        // QR操作テーブルから詳細情報に、残高を追加し返却
        return TransferOperationDetail.builder()
                .confirmationType(QrConfirmationType.TRANSFER)
                .transferAmount(transferDetail.transferAmount)
                .sendDcBankNumber(transferDetail.sendDcBankNumber)
                .fromDcBankNumber(transferDetail.fromDcBankNumber)
                .toAccount(transferDetail.toAccount)
                .memo(transferDetail.memo)
                .miscValue(transferDetail.miscValue)
                .balance(result.balance)
                .requestId(transferDetail.requestId)
                .build();
    }

    private ReviewDcUserOrderResult<TransferResult> doApproveTransfer(
            ReviewDcUserOrderCommand command, TransferOrderDetail orderDetail
    ) {
        return this.dcUserReviewSupport.approve(command, () -> {
            // 移転実行
            TransferCommand transferCommand = TransferCommand.fromOrder(command, orderDetail);
            return this.transfer(transferCommand);
        });
    }

    private CoreApiFindAccountDetailResult doFindAccountFromCore(AccountId accountId, ServiceOwnerEntity serviceOwner) {
        Either<CoreApiFindAccountDetailResult> either =
                this.coreAccountsApi.findAccount(serviceOwner.validatorId, accountId);
        if (either.isSuccess() == false) {
            throw either.toUnexpectedException();
        }

        return either.success();
    }
}
