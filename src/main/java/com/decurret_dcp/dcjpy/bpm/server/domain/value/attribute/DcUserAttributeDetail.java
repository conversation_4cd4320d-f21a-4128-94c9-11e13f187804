package com.decurret_dcp.dcjpy.bpm.server.domain.value.attribute;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AttributeDetail;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = DcUserAttributeDetail.Builder.class)
public class DcUserAttributeDetail implements AttributeDetailContent {

    private static final TypeReference<DcUserAttributeDetail> TYPE_REFERENCE = new TypeReference<>() {
    };

    @JsonProperty("email_address")
    public final String emailAddress;

    @Override
    public AttributeDetail toAttributeDetail() {
        return AttributeDetail.of(this);
    }

    public static DcUserAttributeDetail of(AttributeDetail attributeDetail) {
        return attributeDetail.getContent(TYPE_REFERENCE);
    }
}
