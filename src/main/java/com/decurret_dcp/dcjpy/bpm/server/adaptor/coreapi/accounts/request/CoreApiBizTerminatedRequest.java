package com.decurret_dcp.dcjpy.bpm.server.adaptor.coreapi.accounts.request;

import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.command.CoreApiBizTerminatedCommand;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class CoreApiBizTerminatedRequest {
    public final String requestId;

    public final Integer zoneId;

    public static CoreApiBizTerminatedRequest createBody(CoreApiBizTerminatedCommand command) {
        return CoreApiBizTerminatedRequest.builder()
                .requestId(command.requestId.getValue())
                .zoneId(command.zoneId.getValue())
                .build();
    }
}
