package com.decurret_dcp.dcjpy.bpm.server.adaptor.coreapi.transactions.response;

import java.math.BigInteger;
import java.time.ZonedDateTime;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiFetchTransactionResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.MiscValue;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ZoneId;

public class CoreApiFetchTransactionResponse {

    /** トランザクションID */
    public String transactionId;

    /** トランザクションハッシュ */
    public String transactionHash;

    /** アカウントID */
    public String accountId;

    /** アカウント名 */
    public String accountName;

    /** ゾーンID */
    public Integer zoneId;

    /** ゾーン名 */
    public String zoneName;

    /** 取引日時 */
    public ZonedDateTime transactedAt;

    /** 取引種別 */
    public String transactionType;

    /** 取引額 */
    public Long amount;

    /** 取引後残高 */
    public Long postBalance;

    /** 送金指示者アカウントID */
    public String sendAccountId;

    /** 取引先アカウントID */
    public String otherAccountId;

    /** 取引先アカウント名 */
    public String otherAccountName;

    /** 取引先ゾーンID */
    public Integer otherZoneId;

    /** 取引先ゾーン名 */
    public String otherZoneName;

    /** 摘要 */
    public String memo;

    /** misc_value1 */
    public String miscValue1;

    /** misc_value2 */
    public String miscValue2;

    public CoreApiFetchTransactionResult toResult() {
        var transactedAt = Objects.isNull(this.transactedAt) ? null : AppTimeStamp.of(this.transactedAt);
        var miscValue = StringUtils.isEmpty(this.miscValue1) && StringUtils.isEmpty(this.miscValue2) ? null :
                MiscValue.of(this.miscValue1, this.miscValue2);

        return CoreApiFetchTransactionResult.builder()
                .transactionId(this.transactionId)
                .transactionHash(this.transactionHash)
                .accountId(AccountId.of(this.accountId))
                .accountName(this.accountName)
                .zoneId(ZoneId.of(this.zoneId))
                .zoneName(this.zoneName)
                .transactedAt(transactedAt)
                .transactionType(TransactionType.of(this.transactionType))
                .amount(Amount.of(new BigInteger(this.amount.toString())))
                .postBalance(Balance.of(new BigInteger(this.postBalance.toString())))
                .sendAccountId((this.sendAccountId == null) ? null : AccountId.of(this.sendAccountId))
                .otherAccountId((this.otherAccountId == null) ? null : AccountId.of(this.otherAccountId))
                .otherAccountName(this.otherAccountName)
                .otherZoneId((this.otherZoneId == null) ? null : ZoneId.of(this.otherZoneId))
                .otherZoneName(this.otherZoneName)
                .memo(this.memo)
                .miscValue(miscValue)
                .build();
    }

}
