package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services.response.accounts;

import com.decurret_dcp.dcjpy.bpm.server.application.services.result.OrderChangeAccountResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ReasonCodeEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ServiceUserOrderEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.CharacterConverter;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.order.AccountStatusOrderDetail;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderForceTerminatedAccountResponse {

    /* 依頼番号 */
    public String orderId;

    /* 依頼内容 */
    public String orderType;

    /* アカウント 凍結詳細 */
    public OrderChangeAccountDetailResponse accountForceTerminatedDetail;

    public static OrderForceTerminatedAccountResponse create(OrderChangeAccountResult<?> result) {
        ServiceUserOrderEntity serviceUserOrder = result.serviceUserOrder;
        AccountStatusOrderDetail orderDetail = AccountStatusOrderDetail.of(serviceUserOrder.orderDetail);
        ReasonCodeEntity reasonCode = result.reasonCode;

        OrderChangeAccountDetailResponse detail = OrderChangeAccountDetailResponse.builder()
                .dcBankNumber(result.dcBankNumber.getValue())
                .accountName(CharacterConverter.toFullWidth(result.coreAccount.getDcAccountName()))
                .operatedReasonType(reasonCode.operationType.getValue())
                .reasonCode(reasonCode.reasonCode)
                .reasonTitle(reasonCode.reasonTitle)
                .reasonDetail(orderDetail.reasonDetail)
                .build();

        return OrderForceTerminatedAccountResponse.builder()
                .orderId(serviceUserOrder.orderId.format())
                .orderType(serviceUserOrder.serviceOrderType.getValue())
                .accountForceTerminatedDetail(detail)
                .build();
    }
}
