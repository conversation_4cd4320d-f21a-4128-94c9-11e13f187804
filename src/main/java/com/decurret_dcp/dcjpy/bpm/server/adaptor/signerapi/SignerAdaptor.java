package com.decurret_dcp.dcjpy.bpm.server.adaptor.signerapi;

import org.springframework.http.RequestEntity;
import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bpm.server.adaptor.signerapi.request.SignerAuthorizationRequest;
import com.decurret_dcp.dcjpy.bpm.server.adaptor.signerapi.request.SignerSignRequest;
import com.decurret_dcp.dcjpy.bpm.server.adaptor.signerapi.response.SignerAuthorizationResponse;
import com.decurret_dcp.dcjpy.bpm.server.adaptor.signerapi.response.SignerSignResponse;
import com.decurret_dcp.dcjpy.bpm.server.config.BpmServerApplicationProperty;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.BankAccountEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.exception.ResourceNotFoundException;
import com.decurret_dcp.dcjpy.bpm.server.domain.exception.SignerTokenNotFoundException;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.DcAccountRepository;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.TransactionSupport;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.FindDcAccountCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.CredentialStorage;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.JwtHolder;
import com.decurret_dcp.dcjpy.bpm.server.domain.signer.SignerApi;
import com.decurret_dcp.dcjpy.bpm.server.domain.signer.command.SignerAuthorizationCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.signer.command.SignerSignCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.signer.result.SignerAuthorizationResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.signer.result.SignerSignResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Either;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Tuple;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Jwt;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.credential.SignerToken;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class SignerAdaptor implements SignerApi {

    private final BpmServerApplicationProperty property;

    private final SignerAdaptorSupport support;

    private final CredentialStorage credentialStorage;

    private final TransactionSupport transactionSupport;

    private final DcAccountRepository dcAccountRepository;

    @Override
    public Either<SignerAuthorizationResult> authorization(SignerAuthorizationCommand command) {
        String path = "/auth/token";
        SignerAuthorizationRequest body = SignerAuthorizationRequest.createBody(command);
        RequestEntity<SignerAuthorizationRequest> request = this.support.initPost(path).body(body);

        Either<SignerAuthorizationResponse> either =
                this.support.execute(command.bankCode, request, SignerAuthorizationResponse.class);
        return either.map(success -> success.toResult());
    }

    @Override
    public Either<SignerSignResult> sign(SignerSignCommand command) {
        var path = "/sign";

        Tuple<Jwt, String> tuple = switch (this.property.zoneType) {
            case FINANCIAL_ZONE -> this.findFromBankInfo(command);
            case BUSINESS_ZONE -> this.findFromCredentialStorage(command.signInId);
        };

        Jwt jwt = tuple.first;
        String bankCode = tuple.second;

        SignerSignRequest body = SignerSignRequest.createBody(command);
        RequestEntity<SignerSignRequest> request = this.support.initPostWithIdToken(jwt, path).body(body);

        Either<SignerSignResponse> response = this.support.execute(bankCode, request, SignerSignResponse.class);
        return response.map(success -> success.toResult());
    }

    private Tuple<Jwt, String> findFromBankInfo(SignerSignCommand command) {
        FindDcAccountCommand findCommand = FindDcAccountCommand.create(command.dcBankNumber, command.serviceId);
        BankAccountEntity bankAccount = this.transactionSupport.transaction(
                () -> this.dcAccountRepository.findBankAccount(findCommand)
        );

        return Tuple.of(JwtHolder.get(), bankAccount.bankCode);
    }

    private Tuple<Jwt, String> findFromCredentialStorage(SignInId signInId) {
        try {
            SignerToken token = this.credentialStorage.findSignerToken(signInId);
            return Tuple.of(new Jwt(token.accessToken), token.bankCode);
        } catch (ResourceNotFoundException notFound) {
            throw new SignerTokenNotFoundException(notFound);
        }
    }
}
