package com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic;

public enum SortOrder {

    /** 昇順。 */
    ASC,

    /** 降順。 */
    DESC;

    public String getValue() {
        return this.name().toLowerCase();
    }

    public static SortOrder of(String order) {
        for (SortOrder sortOrder : SortOrder.values()) {
            if (sortOrder.name().equalsIgnoreCase(order)) {
                return sortOrder;
            }
        }

        return null;
    }
}
