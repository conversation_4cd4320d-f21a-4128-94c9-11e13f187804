package com.decurret_dcp.dcjpy.bpm.server.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Insert;
import org.seasar.doma.Select;
import org.seasar.doma.Update;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

import com.decurret_dcp.dcjpy.bpm.server.domain.entity.DcAccountEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.DcUserReasonEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.SignInId;

@ConfigAutowireable
@Dao
public interface DcUserReasonDao {

    @Select
    public DcUserReasonEntity selectById(SignInId signInId);

    @Insert
    public Result<DcUserReasonEntity> insert(DcUserReasonEntity entity);

    @Update
    public Result<DcUserReasonEntity> update(DcUserReasonEntity entity);

    @Update(sqlFile = true)
    public Result<DcAccountEntity> mergeBulk(DcAccountEntity dcAccount, DcUserReasonEntity entity);
}
