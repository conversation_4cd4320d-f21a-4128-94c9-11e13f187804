package com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result;

import java.util.List;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class CoreApiFetchBanksResult {

    public List<CoreApiFindBankResult> banks;

    public final CoreApiPagingResult paging;

    @lombok.Builder
    public static class CoreApiPagingResult {

        /** オフセット */
        public Long offset;

        /** 取得件数 */
        public Integer limit;

        /** 全件数 */
        public Long total;
    }
}
