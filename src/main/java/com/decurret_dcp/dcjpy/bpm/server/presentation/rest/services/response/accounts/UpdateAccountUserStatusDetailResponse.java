package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services.response.accounts;

import com.decurret_dcp.dcjpy.bpm.server.application.services.result.OrderChangeUserStatusResult;
import com.decurret_dcp.dcjpy.bpm.server.application.services.result.ReviewChangeUserStatusOrderResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.CharacterConverter;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OperationType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.order.UserStatusOrderDetail;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateAccountUserStatusDetailResponse {

    /* ユーザのサインイン ID */
    public final String signInId;

    /* 法人ユーザ表示名 */
    public final String userName;

    /* 操作理由種別 */
    public final String operatedReasonType;

    /* 理由分類 */
    public final String reasonCode;

    /* 理由名 */
    public final String reasonTitle;

    /* 理由詳細 */
    public final String reasonDetail;

    public static UpdateAccountUserStatusDetailResponse create(OrderChangeUserStatusResult result) {
        UserStatusOrderDetail orderDetail = UserStatusOrderDetail.of(result.serviceUserOrder.orderDetail);

        return UpdateAccountUserStatusDetailResponse.builder()
                .signInId(orderDetail.signInId.getValue())
                .userName(CharacterConverter.toFullWidth(result.dcUser.userName))
                .operatedReasonType(orderDetail.operationType.getValue())
                .reasonCode(orderDetail.reasonCode)
                .reasonDetail(orderDetail.reasonDetail)
                .reasonTitle(orderDetail.reasonTitle)
                .build();
    }

    public static UpdateAccountUserStatusDetailResponse create(ReviewChangeUserStatusOrderResult result) {
        // qrKey が存在する場合は承認手続きが完了していないので、返却しない
        if (result.qrKey != null) {
            return null;
        }

        UserStatusOrderDetail orderDetail = UserStatusOrderDetail.of(result.serviceUserOrder.orderDetail);
        // サービスユーザ理由が存在しない == 否認された場合
        if (result.dcUserReason == null) {
            return null;
        }

        OperationType operatedReasonType = result.dcUserReason.operationType;

        return UpdateAccountUserStatusDetailResponse.builder()
                .signInId(orderDetail.signInId.getValue())
                .userName(CharacterConverter.toFullWidth(result.userName))
                .operatedReasonType(operatedReasonType.getValue())
                .reasonCode(orderDetail.reasonCode)
                .reasonDetail(orderDetail.reasonDetail)
                .reasonTitle(orderDetail.reasonTitle)
                .build();
    }

    public static UpdateAccountUserStatusDetailResponse create(UserStatusOrderDetail orderDetail) {
        return UpdateAccountUserStatusDetailResponse.builder()
                .signInId(orderDetail.signInId.getValue())
                .userName(CharacterConverter.toFullWidth(orderDetail.userName))
                .operatedReasonType(orderDetail.operationType.getValue())
                .reasonCode(orderDetail.reasonCode)
                .reasonTitle(orderDetail.reasonTitle)
                .reasonDetail(orderDetail.reasonDetail)
                .build();
    }
}
