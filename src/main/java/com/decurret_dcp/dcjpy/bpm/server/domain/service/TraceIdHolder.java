package com.decurret_dcp.dcjpy.bpm.server.domain.service;

import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.TraceId;

public class TraceIdHolder {

    private static final ThreadLocal<TraceId> TL_TRACE_ID = new ThreadLocal<>();

    public static TraceId getTraceId() {
        return TL_TRACE_ID.get();
    }

    public static void setTraceId(String value) {
        TraceId traceId = StringUtils.hasText(value) ? TraceId.of(value) : TraceId.createTraceId();

        TL_TRACE_ID.set(traceId);
        MDC.put("traceId", traceId.getValue());
    }

    public static void remove() {
        TL_TRACE_ID.remove();
        MDC.remove("traceId");
    }
}
