package com.decurret_dcp.dcjpy.bpm.server.domain.value.order;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OrderDetail;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = AccountNameUpdateOrderDetail.Builder.class)
public class AccountNameUpdateOrderDetail implements OrderDetailContent {

    private static final TypeReference<AccountNameUpdateOrderDetail> TYPE_REFERENCE = new TypeReference<>() {
    };

    @JsonProperty("dc_bank_number")
    public final DcBankNumber dcBankNumber;

    @JsonProperty("before_account_name")
    public final String beforeAccountName;

    @JsonProperty("after_account_name")
    public final String afterAccountName;

    @Override
    public OrderDetail toOrderDetail() {
        return OrderDetail.of(this);
    }

    public static AccountNameUpdateOrderDetail of(OrderDetail orderDetail) {
        return orderDetail.getContent(TYPE_REFERENCE);
    }
}

