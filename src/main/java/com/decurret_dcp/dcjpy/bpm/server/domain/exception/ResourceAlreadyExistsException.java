package com.decurret_dcp.dcjpy.bpm.server.domain.exception;

import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;

public class ResourceAlreadyExistsException extends BpmServerException {

    private final MessageCode messageCode;

    private ResourceAlreadyExistsException(String message, MessageCode messageCode) {
        super(message);
        this.messageCode = messageCode;
    }

    public static ResourceAlreadyExistsException existsServiceOwner() {
        return new ResourceAlreadyExistsException(
                "service owner user is already exists.", MessageCode.SERVICE_OWNER_USER_EXISTS);
    }

    public static ResourceAlreadyExistsException existsBankAccount() {
        return new ResourceAlreadyExistsException(
                "Active bank account is duplicated.", MessageCode.DUPLICATED_ACTIVE_BANK_ACCOUNT);
    }

    @Override
    public MessageCode getMessageCode() {
        return this.messageCode;
    }
}
