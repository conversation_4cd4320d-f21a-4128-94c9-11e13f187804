package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services.response.accounts;

import javax.annotation.Nullable;

import com.decurret_dcp.dcjpy.bpm.server.application.services.result.ReviewServiceUserOrderResult;
import com.decurret_dcp.dcjpy.bpm.server.application.services.result.UpdateDcAccountResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.order.AccountStatusOrderDetail;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmServiceUserDetail;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReviewAccountTerminateResponse {

    /* 依頼判定結果 */
    public ReviewResultResponse reviewResult;

    /* QR 用のトークン */
    @Nullable
    public String qrToken;

    /* アカウント凍結詳細(認証アプリを利用しない場合設定) */
    @Nullable
    public AccountUpdateDetailResponse accountForceTerminatedDetail;

    public static ReviewAccountTerminateResponse create(
            BpmServiceUserDetail serviceUserDetail,
            ReviewServiceUserOrderResult<UpdateDcAccountResult<AccountStatusOrderDetail>> result
    ) {
        ReviewResultResponse reviewResult = ReviewResultResponse.create(serviceUserDetail, result);
        AccountUpdateDetailResponse detail = AccountUpdateDetailResponse.create(result);

        return ReviewAccountTerminateResponse.builder()
                .reviewResult(reviewResult)
                .qrToken(result.qrKeyValue())
                .accountForceTerminatedDetail(detail)
                .build();
    }
}
