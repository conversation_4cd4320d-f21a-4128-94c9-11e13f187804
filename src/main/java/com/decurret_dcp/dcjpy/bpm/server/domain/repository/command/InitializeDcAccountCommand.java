package com.decurret_dcp.dcjpy.bpm.server.domain.repository.command;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.SignInId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class InitializeDcAccountCommand {

    public final SignInId signInId;

    public final ServiceId serviceId;

    public final DcBankNumber dcBankNumber;

    public final AccountId accountId;
}
