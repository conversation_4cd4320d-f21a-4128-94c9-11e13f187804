package com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;

import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.request.account.UpdateAccountLimitRequest;

@Documented
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidUpdateAccountLimitRequest.ValidUpdateAccountLimitRequestValidator.class)
public @interface ValidUpdateAccountLimitRequest {

    String message() default "target is required";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    static class ValidUpdateAccountLimitRequestValidator
            implements ConstraintValidator<ValidUpdateAccountLimitRequest, UpdateAccountLimitRequest> {

        @Override
        public boolean isValid(UpdateAccountLimitRequest request, ConstraintValidatorContext context) {

            // いずれかのlimitが指定されている場合はOK
            return (request.mintLimit != null)
                    || (request.burnLimit != null)
                    || (request.transferLimit != null)
                    || (request.chargeLimit != null)
                    || (request.dailyCumulativeLimit != null);
        }
    }
}
