package com.decurret_dcp.dcjpy.bpm.server.domain.exception;

import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;

public class RegisterTelephoneFailureException extends BpmServerException {

    public RegisterTelephoneFailureException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public MessageCode getMessageCode() {
        return MessageCode.PHONE_NUMBER_EXISTS;
    }
}
