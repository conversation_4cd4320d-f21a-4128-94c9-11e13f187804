package com.decurret_dcp.dcjpy.bpm.server.application.services.exception;

import java.util.Map;

import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;
import com.decurret_dcp.dcjpy.bpm.server.domain.exception.BpmServerException;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Either;

public class ChangeAccountNameFailureException extends BpmServerException {

    private static final Map<String, MessageCode> CORE_API_ERROR_CODE_MAP = Map.ofEntries(
            Map.entry("E0001", MessageCode.RESOURCE_NOT_FOUND),
            Map.entry("E0012", MessageCode.RESOURCE_NOT_FOUND),
            Map.entry("E0032", MessageCode.UNABLE_TO_UPDATE_ACCOUNT)
    );

    private final MessageCode messageCode;

    private ChangeAccountNameFailureException(MessageCode messageCode, String message, Throwable cause) {
        super(message, cause);
        this.messageCode = messageCode;
    }

    public static <TYPE> ChangeAccountNameFailureException fromCoreApi(Either<TYPE> result) {
        if (result.isSuccess()) {
            throw new IllegalStateException("This Either is success.");
        }

        String errorCode = result.errorCode();
        MessageCode messageCode = CORE_API_ERROR_CODE_MAP.get(errorCode);
        String errorMessage = result.errorMessage();
        Exception cause = result.cause();
        if (messageCode == null) {
            throw new RuntimeException("[" + errorCode + "]" + errorMessage, cause);
        }

        return new ChangeAccountNameFailureException(messageCode, errorMessage, cause);
    }

    @Override
    public MessageCode getMessageCode() {
        return this.messageCode;
    }
}
