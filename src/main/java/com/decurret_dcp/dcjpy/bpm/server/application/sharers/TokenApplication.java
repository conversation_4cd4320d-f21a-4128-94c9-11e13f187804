package com.decurret_dcp.dcjpy.bpm.server.application.sharers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.bpm.server.domain.exception.ResourceNotFoundException;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.BpmIdentityProvider;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.CredentialStorage;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.JwtProcessor;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.command.RefreshTokenCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.command.SignOutCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.result.RefreshTokenResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.JwtUser;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.UserType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.credential.AuthenticationToken;

@RequiredArgsConstructor
@Service
@Slf4j
public class TokenApplication {

    private final BpmIdentityProvider identityProvider;

    private final JwtProcessor jwtProcessor;

    private final CredentialStorage credentialStorage;

    /**
     * ユーザ認証情報のトークン更新を行う。
     *
     * @param command トークン更新用コマンド
     *
     * @return トークン更新結果
     */
    public RefreshTokenResult refreshToken(RefreshTokenCommand command) {
        RefreshTokenResult result = this.identityProvider.refreshToken(command);

        try {
            JwtUser jwtUser = this.jwtProcessor.parse(result.idToken);
            log.info("Succeed to refresh token. userType : {}, signInId : {}",
                     jwtUser.userType(), jwtUser.signInId().getValue());
        } catch (Exception exc) {
            // 対象のユーザは特定できないものの、トークン更新は完了しているので、その旨をログ出力
            log.info("Succeed to refresh token.");
        }

        return result;
    }

    /**
     * Web向けの認証情報も含めて、認証情報の更新を行う。
     *
     * @param command トークン更新用コマンド
     * @param userType ユーザ種別
     *
     * @return トークン更新結果
     */
    public RefreshTokenResult refreshTokenForAuthenticator(RefreshTokenCommand command, UserType userType) {
        RefreshTokenResult resultForAuthenticator = this.identityProvider.refreshToken(command);

        JwtUser jwtUser;
        try {
            jwtUser = this.jwtProcessor.parse(resultForAuthenticator.idToken);
        } catch (Exception exc) {
            log.warn("Failed to parse JWT. Skip to refresh token for web. detail : {}", exc.getMessage());
            return resultForAuthenticator;
        }

        SignInId signInId = jwtUser.signInId();

        AuthenticationToken beforeWebToken;
        try {
            beforeWebToken = this.credentialStorage.findToken(signInId, userType);
        } catch (ResourceNotFoundException notFound) {
            log.warn("Skip to refresh token for web because token not found. userType : {}, signInId : {}, detail : {}",
                     userType, signInId.getValue(), notFound.getMessage());
            return resultForAuthenticator;
        }

        RefreshTokenCommand refreshWebCommand = RefreshTokenCommand.builder()
                .refreshToken(beforeWebToken.refreshToken)
                .build();
        RefreshTokenResult resultForWeb = this.identityProvider.refreshToken(refreshWebCommand);

        AuthenticationToken afterWebToken = resultForWeb.toToken(beforeWebToken.refreshToken);
        try {
            this.credentialStorage.saveToken(signInId, userType, afterWebToken);
        } catch (RuntimeException exc) {
            log.warn("Failed to refresh token for web. userType : {}, signInId : {}, detail : {}",
                     userType, signInId.getValue(), exc.getMessage());
            return resultForAuthenticator;
        }

        log.info("Succeed to refresh token and update token for web. userType : {}, signInId : {}",
                 userType, signInId.getValue());

        return resultForAuthenticator;
    }

    /**
     * 法人/銀行/事業者/認証アプリのサインアウトを実施する。
     *
     * @param command サインアウトコマンド
     */
    public boolean signOut(SignOutCommand command) {
        return this.identityProvider.signOut(command);
    }
}
