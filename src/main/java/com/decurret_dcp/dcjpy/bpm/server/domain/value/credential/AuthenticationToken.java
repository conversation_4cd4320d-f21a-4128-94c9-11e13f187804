package com.decurret_dcp.dcjpy.bpm.server.domain.value.credential;

import java.util.concurrent.TimeUnit;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(exclude = { "idToken", "accessToken", "refreshToken" })
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = AuthenticationToken.Builder.class)
public class AuthenticationToken implements CredentialEntity {

    @JsonProperty("id_token")
    public final String idToken;

    @JsonProperty("access_token")
    public final String accessToken;

    @JsonProperty("refresh_token")
    public final String refreshToken;

    @JsonProperty("expires_unix_time_millis")
    public final Long expiresUnixTimeMillis;

    public Integer expiresIn() {
        if (this.expiresUnixTimeMillis == null) {
            return null;
        }

        long expiresIn = TimeUnit.MILLISECONDS.toSeconds(
                this.expiresUnixTimeMillis.longValue() - System.currentTimeMillis()
        );

        return Integer.valueOf((int) expiresIn);
    }
}
