package com.decurret_dcp.dcjpy.bpm.server.domain.value.order;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OrderDetail;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.SignInId;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = UserUpdatedDetail.Builder.class)
public class UserUpdatedDetail implements OrderDetailContent {

    private static final TypeReference<UserUpdatedDetail> TYPE_REFERENCE = new TypeReference<>() {
    };

    @JsonProperty("sign_in_id")
    public final SignInId signInId;

    @JsonProperty("before_user_name")
    public final String beforeUserName;

    @JsonProperty("after_user_name")
    public final String afterUserName;

    @Override
    public OrderDetail toOrderDetail() {
        return OrderDetail.of(this);
    }

    public static UserUpdatedDetail of(OrderDetail orderDetail) {
        return orderDetail.getContent(TYPE_REFERENCE);
    }
}
