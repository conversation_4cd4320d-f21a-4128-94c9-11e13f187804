package com.decurret_dcp.dcjpy.bpm.server.application.holders.command;

import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.command.CoreApiChargeCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.command.CoreApiCheckChargeCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiCheckChargeResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.DcAccountEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.DcUserOrderEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.DcUserOrderCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.FindDcAccountCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.ReviewDcUserOrderCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.command.PublishDcUserCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.order.ExchangeAccountDetail;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.DcUserWithAccount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.NotificationType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OrderType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.RequestId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.order.ChargeOrderDetail;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class ChargeCommand {

    public final RequestId requestId;

    public final DcUserWithAccount dcUser;

    public final DcBankNumber dcBankNumber;

    public final ServiceId serviceId;

    public final ZoneId toZoneId;

    public final Amount chargeAmount;

    public DcBankNumber getDcBankNumber() {
        return this.dcUser.dcBankNumber;
    }

    public static ChargeCommand fromOrder(ReviewDcUserOrderCommand command, ChargeOrderDetail orderDetail) {
        ZoneId zoneId = orderDetail.toAccount.getZoneId();
        Amount chargeAmount = orderDetail.chargeAmount;

        return ChargeCommand.builder()
                .requestId(command.initRequestId())
                .dcUser(command.dcUser)
                .dcBankNumber(command.getDcBankNumber())
                .serviceId(command.getServiceId())
                .toZoneId(zoneId)
                .chargeAmount(chargeAmount)
                .build();
    }

    public FindDcAccountCommand initFindDcAccountCommand() {
        return FindDcAccountCommand.create(this.dcBankNumber, this.serviceId);
    }

    public CoreApiCheckChargeCommand initCoreApiCheckChargeCommand(ValidatorId validatorId, DcAccountEntity dcAccount) {
        return CoreApiCheckChargeCommand.builder()
                .validatorId(validatorId)
                .accountId(dcAccount.accountId)
                .toZoneId(this.toZoneId)
                .chargeAmount(this.chargeAmount)
                .build();
    }

    public CoreApiChargeCommand initCoreApiChargeCommand(
            RequestId requestId, ValidatorId validatorId, DcAccountEntity dcAccount) {
        return CoreApiChargeCommand.builder()
                .requestId(requestId)
                .validatorId(validatorId)
                .accountId(dcAccount.accountId)
                .toZoneId(this.toZoneId)
                .chargeAmount(this.chargeAmount)
                .build();
    }

    public DcUserOrderCommand<ChargeOrderDetail> initOrderCommand(
            CoreApiCheckChargeResult checkResult, String bizAccountName
    ) {
        // チャージ元のフィナンシャルゾーンのアカウント
        ExchangeAccountDetail fromAccount = ExchangeAccountDetail.builder()
                .dcBankNumber(this.getDcBankNumber())
                .accountName(checkResult.accountName)
                .zone(checkResult.fromZoneDetail())
                .build();

        // チャージ先のビジネスゾーンのアカウント
        ExchangeAccountDetail toAccount = ExchangeAccountDetail.builder()
                .dcBankNumber(this.getDcBankNumber())
                .accountName(bizAccountName)
                .zone(checkResult.toZoneDetail())
                .build();

        // DCユーザ申請テーブルの申請内容詳細
        ChargeOrderDetail orderDetail = ChargeOrderDetail.builder()
                .chargeAmount(checkResult.chargeAmount)
                .fromAccount(fromAccount)
                .toAccount(toAccount)
                .build();

        return DcUserOrderCommand.<ChargeOrderDetail>builder()
                .orderType(OrderType.CHARGE)
                .orderUser(this.dcUser)
                .orderDetail(orderDetail)
                .build();
    }

    public PublishDcUserCommand initPublishCommand(DcUserOrderEntity order) {
        return PublishDcUserCommand.builder()
                .order(order)
                .notificationType(NotificationType.ORDER)
                .dcUser(this.dcUser)
                .build();
    }
}
