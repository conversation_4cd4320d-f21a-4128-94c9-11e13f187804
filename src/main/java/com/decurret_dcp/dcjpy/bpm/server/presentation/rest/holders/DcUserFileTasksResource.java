package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders;

import java.util.List;

import javax.validation.Valid;

import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.decurret_dcp.dcjpy.bpm.server.application.holders.DcUserFileTaskApplication;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.FetchFileTasksCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.result.DcUserFileTaskWithDcUser;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Pager;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.PagingList;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcUserRoleType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.FileTaskId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.TaskType;
import com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.ListLimit;
import com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.ListOffset;
import com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.SortOrder;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.FetchFileTasksResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.DownloadFileResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmDcUserDetail;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.annotation.DcAccountOwnerAuthorize;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.annotation.DcUserAuthorize;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * U81. ユーザ向け ファイルタスク管理。
 */
@AllArgsConstructor
@RestController
@RequestMapping("/holders/file_tasks")
@Validated
@Slf4j
public class DcUserFileTasksResource {

    private final DcUserFileTaskApplication application;

    /**
     * U81-01. ユーザ向け タスク一覧取得。
     *
     * @param userDetail ユーザ情報
     * @param taskType 取得対象のタスク種別
     * @param sortOrder ソート順
     * @param offset オフセット
     * @param limit 取得件数
     *
     * @return タスク一覧
     */
    @GetMapping("")
    @DcUserAuthorize(
            roleType = { DcUserRoleType.USER_OWNER, DcUserRoleType.OPERATOR, DcUserRoleType.REVIEWER,
                    DcUserRoleType.INDIVIDUAL },
            authority = {}
    )
    public FetchFileTasksResponse fetchFileTasks(
            @AuthenticationPrincipal BpmDcUserDetail userDetail,
            @RequestParam(name = "task_type", required = false)
            List<@com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.TaskType String> taskType,
            @Valid @SortOrder @RequestParam(name = "sort_order", required = false) String sortOrder,
            @ListOffset @RequestParam(required = false) Integer offset,
            @ListLimit @RequestParam(required = false) Integer limit
    ) {
        FetchFileTasksCommand command = FetchFileTasksCommand.builder()
                .dcBankNumber(userDetail.getDcBankNumber())
                .serviceId(userDetail.getServiceId())
                .taskType(toTaskLists(taskType))
                .build();
        Pager pager = Pager.defaultDesc(sortOrder, offset, limit);

        PagingList<DcUserFileTaskWithDcUser> pagingList = this.application.fetchFileTasks(command, pager);
        return FetchFileTasksResponse.forDcUser(pagingList);
    }

    private static List<TaskType> toTaskLists(List<String> taskTypes) {
        if ((taskTypes == null) || taskTypes.isEmpty()) {
            return null;
        }

        return taskTypes.stream()
                .map(taskType -> TaskType.of(taskType))
                .toList();
    }

    /**
     * U81-03. ユーザ向け タスク詳細 ダウンロード。
     *
     * @param fileTaskId ファイルタスクID
     * @return ダウンロードファイルURL
     */
    @GetMapping("/{file_task_id}/download")
    @DcAccountOwnerAuthorize
    public DownloadFileResponse fetchDownloadUrl(
            @AuthenticationPrincipal BpmDcUserDetail userDetail,
            @PathVariable(name = "file_task_id", required = true) String fileTaskId
    ) {
        String preSignedUrl = this.application.downloadFileTask(FileTaskId.of(fileTaskId), userDetail.getServiceId());

        return DownloadFileResponse.create(preSignedUrl);
    }
}
