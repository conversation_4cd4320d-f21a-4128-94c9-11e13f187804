package com.decurret_dcp.dcjpy.bpm.server.domain.value.qr;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.MiscValue;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.ToString;

@lombok.Builder
@JsonDeserialize(builder = TransferDetail.Builder.class)
public class TransferDetail {

    @JsonProperty("transfer_amount")
    public final Amount transferAmount;

    @JsonProperty("to_account")
    public final ToAccount toAccount;

    @JsonProperty("memo")
    public final String memo;

    @JsonProperty("extra_info")
    public final MiscValue extraInfo;

    @JsonProperty("balance")
    public final Balance balance;

    @ToString
    @lombok.Builder
    @JsonDeserialize(builder = ToAccount.Builder.class)
    public static class ToAccount {

        @JsonProperty("dc_bank_number")
        public DcBankNumber dcBankNumber;

        @JsonProperty("account_name")
        public String accountName;
    }
}