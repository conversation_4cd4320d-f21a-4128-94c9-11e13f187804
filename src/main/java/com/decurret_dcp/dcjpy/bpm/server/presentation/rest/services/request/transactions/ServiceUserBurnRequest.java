package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services.request.transactions;

import java.math.BigInteger;

import javax.validation.constraints.NotNull;

import com.decurret_dcp.dcjpy.bpm.server.application.sharers.command.BurnCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.RequestId;
import com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.BurnAmount;
import com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.ValidBurnRequest;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmServiceUserDetail;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@ValidBurnRequest
@NotNull
public class ServiceUserBurnRequest {

    @NotNull
    @com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.RequestId
    public String requestId;

    public Boolean fullBurn = Boolean.FALSE;

    @BurnAmount
    public BigInteger burnAmount;

    public BurnCommand initCommand(BpmServiceUserDetail serviceUserDetail, String dcBankNumber) {
        DcBankNumber tmpDcBankNumber = DcBankNumber.of(dcBankNumber);
        boolean ifFullBurn = this.fullBurn();

        return BurnCommand.builder()
                .requestId(RequestId.create(tmpDcBankNumber, this.requestId))
                .fullBurn(ifFullBurn)
                .burnAmount((ifFullBurn == true) ? null : Amount.of(this.burnAmount))
                .dcBankNumber(tmpDcBankNumber)
                .serviceId(serviceUserDetail.getServiceId())
                .build();
    }

    private boolean fullBurn() {
        return (this.fullBurn == null) ? false : this.fullBurn.booleanValue();
    }
}
