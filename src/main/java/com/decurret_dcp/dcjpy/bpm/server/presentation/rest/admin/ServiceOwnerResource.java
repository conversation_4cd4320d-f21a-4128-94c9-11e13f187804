package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.admin;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.decurret_dcp.dcjpy.bpm.server.application.admin.ServiceOwnerApplication;
import com.decurret_dcp.dcjpy.bpm.server.application.admin.command.CreateServiceOwnerCommand;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.admin.request.CreateServiceOwnerRequest;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@RestController
@RequestMapping("/admin/service_owner")
@Validated
public class ServiceOwnerResource {

    private final ServiceOwnerApplication serviceOwnerApplication;

    /**
     * M91-01. サービスオーナー作成。
     *
     * @param request 作成内容
     */
    @PostMapping("")
    public void createServiceOwner(@RequestBody @Validated CreateServiceOwnerRequest request) {
        CreateServiceOwnerCommand command = request.initCommand();
        this.serviceOwnerApplication.createServiceOwner(command);
    }
}
