package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.response.users;

import java.time.ZonedDateTime;

import com.decurret_dcp.dcjpy.bpm.server.domain.repository.result.DcUserDetail;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.result.DcUserWithAuthority;
import com.decurret_dcp.dcjpy.bpm.server.domain.service.CharacterConverter;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.AuthenticatorMode;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.response.OperatedReasonResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.UserStatusResponse;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FindDcUserResponse {

    public final String signInId;

    public final String userName;

    public final String userStatus;

    public final FindDcUserRoleMasterResponse role;

    public final DcUserAttributeResponse userAttribute;

    public final OperatedReasonResponse operatedReason;

    public final ZonedDateTime lastSignedAt;

    public final ZonedDateTime registeredAt;

    public static FindDcUserResponse create(DcUserDetail dcUserDetail, AuthenticatorMode authenticatorMode) {
        FindDcUserRoleMasterResponse userRole = FindDcUserRoleMasterResponse.create(dcUserDetail);
        DcUserAttributeResponse userAttribute = new DcUserAttributeResponse(dcUserDetail, authenticatorMode);
        OperatedReasonResponse operatedReason = OperatedReasonResponse.create(dcUserDetail);

        return FindDcUserResponse.builder()
                .signInId(dcUserDetail.signInId.getValue())
                .userName(CharacterConverter.toFullWidth(dcUserDetail.userName))
                .userStatus(UserStatusResponse.toValue(dcUserDetail.userStatus))
                .role(userRole)
                .userAttribute(userAttribute)
                .operatedReason(operatedReason)
                .lastSignedAt(dcUserDetail.currentSignedInAt.zonedDateTime())
                .registeredAt(dcUserDetail.registeredAt.zonedDateTime())
                .build();
    }

    static FindDcUserResponse create(DcUserWithAuthority dcUser) {
        FindDcUserRoleMasterResponse role = FindDcUserRoleMasterResponse.create(dcUser);

        return FindDcUserResponse.builder()
                .signInId(dcUser.signInId.getValue())
                .userName(CharacterConverter.toFullWidth(dcUser.userName))
                .userStatus(UserStatusResponse.toValue(dcUser.userStatus))
                .role(role)
                .registeredAt(dcUser.registeredAt.zonedDateTime())
                .build();
    }
}
