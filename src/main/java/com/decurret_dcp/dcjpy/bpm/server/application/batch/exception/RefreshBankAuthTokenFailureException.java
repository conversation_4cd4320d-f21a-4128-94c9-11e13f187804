package com.decurret_dcp.dcjpy.bpm.server.application.batch.exception;

import java.util.Map;

import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;
import com.decurret_dcp.dcjpy.bpm.server.domain.bankgw.result.BankGatewayAuthorizationResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.exception.BpmServerException;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Either;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountStatus;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;

public class RefreshBankAuthTokenFailureException extends BpmServerException {

    private static final Map<String, MessageCode> BANKGW_ERROR_CODE_MAP = Map.ofEntries(
            Map.entry("not_found_token", MessageCode.FAILED_OPERATION_WITH_EXTERNAL_SERVICE),
            Map.entry("invalid_grant", MessageCode.FAILED_OPERATION_WITH_EXTERNAL_SERVICE),
            Map.entry("invalid_scope", MessageCode.FAILED_OPERATION_WITH_EXTERNAL_SERVICE),
            Map.entry("out_of_service", MessageCode.FAILED_OPERATION_WITH_EXTERNAL_SERVICE),
            Map.entry("ib_unexpected_error", MessageCode.FAILED_OPERATION_WITH_EXTERNAL_SERVICE)
    );

    private final MessageCode messageCode;

    private RefreshBankAuthTokenFailureException(MessageCode messageCode, String message) {
        super(message);
        this.messageCode = messageCode;
    }

    private RefreshBankAuthTokenFailureException(MessageCode messageCode, String message, Throwable cause) {
        super(message, cause);
        this.messageCode = messageCode;
    }

    public static RefreshBankAuthTokenFailureException needIBAuth(DcBankNumber dcBankNumber) {
        return new RefreshBankAuthTokenFailureException(
                MessageCode.INVALID_DC_ACCOUNT_STATUS,
                "Need IB authentication for bankLinkStatus is invalid. dcBankNumber = " + dcBankNumber.getValue());
    }

    public static RefreshBankAuthTokenFailureException invalidDcAccountStatus(
            DcBankNumber dcBankNumber, AccountStatus accountStatus
    ) {
        return new RefreshBankAuthTokenFailureException(
                MessageCode.INVALID_DC_ACCOUNT_STATUS,
                "Need IB authentication for accountStatus is invalid. dcBankNumber = " + dcBankNumber.getValue() +
                        ", accountStatus = " + accountStatus.getValue());
    }

    public static RefreshBankAuthTokenFailureException fromBankGateway(Either<BankGatewayAuthorizationResult> result) {
        if (result.isSuccess()) {
            throw new IllegalStateException("This Either is success.");
        }

        String errorCode = result.errorCode();
        MessageCode messageCode = BANKGW_ERROR_CODE_MAP.get(errorCode);
        String errorMessage = result.errorMessage();
        Exception cause = result.cause();
        if (messageCode == null) {
            throw new RuntimeException("[" + errorCode + "]" + errorMessage, cause);
        }

        return new RefreshBankAuthTokenFailureException(messageCode, errorMessage, cause);
    }

    @Override
    public MessageCode getMessageCode() {
        return this.messageCode;
    }
}
