package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services.response.transactions;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiCheckMintResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OrderRequiredType;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class ServiceUserCheckMintResponse {

    public final String orderRequired;

    public final String dcBankNumber;

    public final BigInteger mintAmount;

    public static ServiceUserCheckMintResponse create(CoreApiCheckMintResult result, String dcBankNumber) {
        return ServiceUserCheckMintResponse.builder()
                .orderRequired(OrderRequiredType.DIRECT.getValue())
                .dcBankNumber(dcBankNumber)
                .mintAmount(result.mintAmount.getDisplayValue())
                .build();
    }
}
