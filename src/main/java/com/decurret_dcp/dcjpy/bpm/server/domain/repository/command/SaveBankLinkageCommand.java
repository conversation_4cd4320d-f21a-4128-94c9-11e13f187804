package com.decurret_dcp.dcjpy.bpm.server.domain.repository.command;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ServiceId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class SaveBankLinkageCommand {

    public final DcBankNumber dcBankNumber;

    public final ServiceId serviceId;

    public final AppTimeStamp expiresAt;

    public static SaveBankLinkageCommand toNeedAuth(DcBankNumber dcBankNumber, ServiceId serviceId) {
        return SaveBankLinkageCommand.builder()
                .dcBankNumber(dcBankNumber)
                .serviceId(serviceId)
                .build();
    }
}
