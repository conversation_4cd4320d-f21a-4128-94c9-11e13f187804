package com.decurret_dcp.dcjpy.bpm.server.application.admin.result;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ZoneId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class AddValidatorResult {

    public final String validatorId;

    public final ZoneId zoneId;

    public final String validatorName;

    public final String issuerId;

    public final String clientId;

    public final String clientSecret;

}
