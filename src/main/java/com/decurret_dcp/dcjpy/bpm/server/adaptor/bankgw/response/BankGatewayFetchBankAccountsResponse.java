package com.decurret_dcp.dcjpy.bpm.server.adaptor.bankgw.response;

import java.math.BigInteger;
import java.util.List;

import com.decurret_dcp.dcjpy.bpm.server.domain.bankgw.result.BankGatewayBankAccountResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Balance;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class BankGatewayFetchBankAccountsResponse {

    public List<BankGatewayBankAccountResponse> bankAccounts;

    public List<BankGatewayBankAccountResult> toResult() {
        if (this.bankAccounts == null) {
            return List.of();
        }

        return this.bankAccounts.stream()
                .map(bankAccount -> bankAccount.toResult())
                .toList();
    }

    @ToString
    @EqualsAndHashCode
    public static class BankGatewayBankAccountResponse {

        public String bankAccountId;

        public String bankCode;

        public String bankName;

        public String branchCode;

        public String bankAccountType;

        public String bankAccountNumber;

        public String bankAccountName;

        public String bankAccountNameKana;

        public BigInteger realBalance;

        public BigInteger availableBalance;

        public BankGatewayExtraInfoResponse extraInfo;

        public BankGatewayBankAccountResult toResult() {
            return BankGatewayBankAccountResult.builder()
                    .bankAccountId(this.bankAccountId)
                    .bankCode(this.bankCode)
                    .bankName(this.bankName)
                    .branchCode(this.branchCode)
                    .bankAccountType(this.bankAccountType)
                    .bankAccountNumber(this.bankAccountNumber)
                    .bankAccountName(this.bankAccountName)
                    .bankAccountNameKana(this.bankAccountNameKana)
                    .realBalance((this.realBalance != null) ? Balance.of(this.realBalance) : null)
                    .availableBalance((this.availableBalance != null) ? Balance.of(this.availableBalance) : null)
                    .bankGatewayExtraInfo((this.extraInfo != null) ? this.extraInfo.toResult() : null)
                    .build();
        }
    }
}
