package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders;

import org.springframework.context.annotation.Conditional;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.decurret_dcp.dcjpy.bpm.server.application.holders.DcAccountApplication;
import com.decurret_dcp.dcjpy.bpm.server.application.holders.command.ChangeBusinessAccountCommand;
import com.decurret_dcp.dcjpy.bpm.server.application.holders.result.ChangeBusinessAccountResult;
import com.decurret_dcp.dcjpy.bpm.server.config.condition.ZoneCondition;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.request.account.ChangeBusinessAccountRequest;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.response.account.ChangeBusinessAccountResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmDcUserDetail;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.annotation.DcAccountOwnerAuthorize;

import lombok.AllArgsConstructor;

/**
 * U31. ユーザ向け アカウント管理。
 * ただしFinZoneのみかつ、ビジネスゾーン関連
 */
@Conditional(ZoneCondition.FinancialZone.class)
@AllArgsConstructor
@RestController
@RequestMapping("/holders/business")
@Validated
public class MyAccountIndustryResource {

    private final DcAccountApplication dcAccountApplication;

    /**
     * U31-36.ビジネスゾーンアカウント利用確定。
     *
     * @param userDetail ユーザ情報
     * @param request 　ビジネスゾーンアカウント利用確定内容
     */
    @DcAccountOwnerAuthorize
    @PostMapping("/apply")
    public ChangeBusinessAccountResponse applyBusiness(
            @AuthenticationPrincipal BpmDcUserDetail userDetail,
            @RequestBody @Validated ChangeBusinessAccountRequest request
    ) {
        ChangeBusinessAccountCommand command = request.initCommand(userDetail);
        ChangeBusinessAccountResult result = this.dcAccountApplication.applyBusinessAccount(command);

        return ChangeBusinessAccountResponse.create(result);
    }

    /**
     * U31-46.ビジネスゾーンアカウント利用解約確定。
     *
     * @param userDetail ユーザ情報
     * @param request 　ビジネスゾーンアカウント利用解約確定内容
     */
    @DcAccountOwnerAuthorize
    @PostMapping("/terminate")
    public ChangeBusinessAccountResponse terminateBusiness(
            @AuthenticationPrincipal BpmDcUserDetail userDetail,
            @RequestBody @Validated ChangeBusinessAccountRequest request
    ) {
        ChangeBusinessAccountCommand command = request.initCommand(userDetail);
        ChangeBusinessAccountResult result = this.dcAccountApplication.terminateBusinessAccount(command);

        return ChangeBusinessAccountResponse.create(result);
    }
}
