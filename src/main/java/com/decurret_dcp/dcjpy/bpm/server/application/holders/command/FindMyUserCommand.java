package com.decurret_dcp.dcjpy.bpm.server.application.holders.command;

import com.decurret_dcp.dcjpy.bpm.server.domain.service.command.FetchDcUserStatusCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.AuthenticatorMode;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.DcUserWithAccount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.SignInId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class FindMyUserCommand {

    public final SignInId signInId;

    public final DcUserWithAccount dcUserWithAccount;

    public final AuthenticatorMode authenticatorMode;

    public FetchDcUserStatusCommand initGetStatusCommand() {
        return FetchDcUserStatusCommand.builder()
                .dcUser(dcUserWithAccount)
                .authenticatorMode(authenticatorMode)
                .currentTime(AppTimeStamp.now())
                .build();
    }
}
