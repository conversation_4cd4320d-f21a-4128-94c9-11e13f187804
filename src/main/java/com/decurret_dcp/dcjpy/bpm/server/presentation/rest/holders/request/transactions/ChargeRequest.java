package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.request.transactions;

import java.math.BigInteger;

import javax.validation.constraints.NotNull;

import com.decurret_dcp.dcjpy.bpm.server.application.holders.command.ChargeCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.RequestId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.ChargeAmount;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmDcUserDetail;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class ChargeRequest {

    @NotNull
    @com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.RequestId
    public String requestId;

    @NotNull
    @com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.ZoneId
    public Integer zoneId;

    @NotNull
    @ChargeAmount
    public BigInteger chargeAmount;

    public ChargeCommand initCommand(BpmDcUserDetail userDetail) {
        DcBankNumber dcBankNumber = userDetail.getDcBankNumber();
        return ChargeCommand.builder()
                .requestId(RequestId.create(dcBankNumber, this.requestId))
                .dcBankNumber(dcBankNumber)
                .serviceId(userDetail.getServiceId())
                .toZoneId(ZoneId.of(this.zoneId))
                .chargeAmount(Amount.of(this.chargeAmount))
                .build();
    }
}
