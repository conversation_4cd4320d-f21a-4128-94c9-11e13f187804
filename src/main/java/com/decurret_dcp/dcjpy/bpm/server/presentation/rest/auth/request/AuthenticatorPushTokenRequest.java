package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.auth.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.decurret_dcp.dcjpy.bpm.server.application.sharers.command.RegisterPushTokenCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OsType;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmUserDetails;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(exclude = { "pushToken" })
@EqualsAndHashCode
public class AuthenticatorPushTokenRequest {

    @NotBlank
    @Size(min = 1, max = 1024)
    public String pushToken;

    @NotBlank
    @com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation.OsType
    public String osType;

    public RegisterPushTokenCommand initCommand(BpmUserDetails userDetail) {
        return RegisterPushTokenCommand.builder()
                .serviceId(userDetail.getServiceId())
                .signInId(userDetail.getSignInId())
                .userType(userDetail.getUserType())
                .pushToken(this.pushToken)
                .osType(OsType.of(this.osType))
                .build();
    }
}
