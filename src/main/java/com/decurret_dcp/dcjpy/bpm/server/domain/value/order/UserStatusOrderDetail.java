package com.decurret_dcp.dcjpy.bpm.server.domain.value.order;

import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OperationType;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.OrderDetail;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.SignInId;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = UserStatusOrderDetail.Builder.class)
public class UserStatusOrderDetail implements OrderDetailContent {

    private static final TypeReference<UserStatusOrderDetail> TYPE_REFERENCE = new TypeReference<>() {
    };

    @JsonProperty("sign_in_id")
    public final SignInId signInId;

    @JsonProperty("user_name")
    public final String userName;

    @JsonProperty("operation_type")
    public final OperationType operationType;

    @JsonProperty("reason_code")
    public final String reasonCode;

    @JsonProperty("reason_title")
    public final String reasonTitle;

    @JsonProperty("reason_detail")
    public final String reasonDetail;

    @Override
    public OrderDetail toOrderDetail() {
        return OrderDetail.of(this);
    }

    public static UserStatusOrderDetail of(OrderDetail orderDetail) {
        return orderDetail.getContent(TYPE_REFERENCE);
    }
}
