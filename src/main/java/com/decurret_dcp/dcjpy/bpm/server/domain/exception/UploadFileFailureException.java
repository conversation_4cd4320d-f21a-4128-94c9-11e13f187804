package com.decurret_dcp.dcjpy.bpm.server.domain.exception;

import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;

public class UploadFileFailureException extends BpmServerException {

    public UploadFileFailureException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public MessageCode getMessageCode() {
        return MessageCode.FAILED_TO_UPLOAD_FILE;
    }
}
