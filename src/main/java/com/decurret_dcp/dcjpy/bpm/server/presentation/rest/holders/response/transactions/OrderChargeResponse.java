package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.response.transactions;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bpm.server.application.holders.result.OrderChargeResult;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class OrderChargeResponse {

    public final String orderId;

    public final BigInteger chargeAmount;

    public static OrderChargeResponse create(OrderChargeResult result) {
        return OrderChargeResponse.builder()
                .orderId(result.orderId.format())
                .chargeAmount(result.chargeAmount.getDisplayValue())
                .build();
    }
}
