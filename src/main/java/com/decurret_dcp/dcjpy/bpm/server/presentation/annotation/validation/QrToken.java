package com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.constraints.Pattern;

@Documented
@Constraint(validatedBy = {})
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Pattern(regexp = "^DCJPY.{64}$", message = "Invalid format.")
public @interface QrToken {

    String message() default "Invalid format.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
