package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services.response.accounts;

import java.time.ZonedDateTime;

import javax.annotation.Nullable;

import com.decurret_dcp.dcjpy.bpm.server.application.services.result.ReviewChangeUserStatusOrderResult;
import com.decurret_dcp.dcjpy.bpm.server.application.services.result.ReviewServiceUserOrderResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ReasonCodeEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ServiceUserOrderEntity;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.RejectedReasonResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.ReviewerResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmServiceUserDetail;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 依頼判定結果
 */
@lombok.Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReviewResultResponse {

    /* 依頼番号 */
    public String orderId;

    /* 依頼判定種別 */
    public String reviewStatus;

    /* 依頼内容 */
    public String orderType;

    /* 依頼判定者 */
    public ReviewerResponse reviewer;

    /* 否認理由 */
    public RejectedReasonResponse rejectedReason;

    /* 依頼日時 */
    public ZonedDateTime orderedAt;

    /* 承認/否認 日時 */
    @Nullable
    public ZonedDateTime operatedAt;

    public static <TYPE> ReviewResultResponse create(
            BpmServiceUserDetail serviceUserDetail, ReviewServiceUserOrderResult<TYPE> result
    ) {
        ServiceUserOrderEntity order = result.order;
        ReasonCodeEntity reasonCode = result.reasonCode;

        return ReviewResultResponse.builder()
                .orderId(order.orderId.format())
                .reviewStatus(order.orderStatus.getValue())
                .orderType(order.serviceOrderType.getValue())
                .reviewer(ReviewerResponse.create(serviceUserDetail))
                .rejectedReason(RejectedReasonResponse.create(order, reasonCode))
                .orderedAt(order.orderedAt.zonedDateTime())
                .operatedAt((order.reviewedAt != null) ? order.reviewedAt.zonedDateTime() : null)
                .build();
    }

    public static ReviewResultResponse create(BpmServiceUserDetail serviceUserDetail,
                                              ReviewChangeUserStatusOrderResult result) {
        ServiceUserOrderEntity order = result.serviceUserOrder;
        ReasonCodeEntity reasonCode = result.reasonCode;

        return ReviewResultResponse.builder()
                .orderId(order.orderId.format())
                .reviewStatus(order.orderStatus.getValue())
                .orderType(order.serviceOrderType.getValue())
                .reviewer(ReviewerResponse.create(serviceUserDetail))
                .rejectedReason(RejectedReasonResponse.create(order, reasonCode))
                .orderedAt(order.orderedAt.zonedDateTime())
                .operatedAt((order.reviewedAt != null) ? order.reviewedAt.zonedDateTime() : null)
                .build();
    }
}
