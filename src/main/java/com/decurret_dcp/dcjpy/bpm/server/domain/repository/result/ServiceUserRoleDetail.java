package com.decurret_dcp.dcjpy.bpm.server.domain.repository.result;

import java.util.List;

import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ServiceUserRoleAuthorityEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ServiceUserRoleEntity;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class ServiceUserRoleDetail {

    public final ServiceUserRoleEntity serviceUserRole;

    public final List<ServiceUserRoleAuthorityEntity> serviceUserRoleAuthorities;
}
