package com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.validation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;

import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders.request.transactions.DcUserBurnRequest;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services.request.transactions.ServiceUserBurnRequest;

@Documented
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = { ValidBurnRequest.DcUserBurnRequestValidator.class,
        ValidBurnRequest.ServiceUserBurnRequestValidator.class })
public @interface ValidBurnRequest {

    String message() default "burn_amount is required when full_burn is false";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    static class DcUserBurnRequestValidator implements ConstraintValidator<ValidBurnRequest, DcUserBurnRequest> {

        @Override
        public boolean isValid(DcUserBurnRequest request, ConstraintValidatorContext context) {
            boolean fullBurn = (request.fullBurn != null) && request.fullBurn;
            if (fullBurn) {
                return true; // fullBurnがtrueの場合はburnAmountがあってもなくてもOK
            }

            // fullBurnが未指定もしくはfalseの場合、burnAmountが必須
            return request.burnAmount != null;
        }
    }

    static class ServiceUserBurnRequestValidator implements
            ConstraintValidator<ValidBurnRequest, ServiceUserBurnRequest> {

        @Override
        public boolean isValid(
                ServiceUserBurnRequest request,
                ConstraintValidatorContext context) {
            boolean fullBurn = (request.fullBurn != null) && request.fullBurn;
            if (fullBurn) {
                return true; // fullBurnがtrueの場合はburnAmountがあってもなくてもOK
            }

            // fullBurnが未指定もしくはfalseの場合、burnAmountが必須
            return request.burnAmount != null;
        }
    }
}
