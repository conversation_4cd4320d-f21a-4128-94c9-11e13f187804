package com.decurret_dcp.dcjpy.bpm.server.adaptor.coreapi.transactions.response;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiFinZoneDischargeResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ZoneId;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class CoreApiFinZoneDischargeResponse {

    /* アカウントID */
    public String accountId;

    /* アカウント名 */
    public String accountName;

    /* ディスチャージ額 */
    public BigInteger dischargeAmount;

    /* ディスチャージ元のゾーンID */
    public Integer fromZoneId;

    /* ディスチャージ元のゾーン名 */
    public String fromZoneName;

    /* ディスチャージ先のゾーンID */
    public Integer toZoneId;

    /* ディスチャージ先のゾーン名 */
    public String toZoneName;

    public CoreApiFinZoneDischargeResult toResult() {
        return CoreApiFinZoneDischargeResult.builder()
                .accountId(AccountId.of(this.accountId))
                .accountName(this.accountName)
                .dischargeAmount(Amount.of(this.dischargeAmount))
                .fromZoneId(ZoneId.of(this.fromZoneId))
                .fromZoneName(this.fromZoneName)
                .toZoneId(ZoneId.of(this.toZoneId))
                .toZoneName(this.toZoneName)
                .build();
    }
}

