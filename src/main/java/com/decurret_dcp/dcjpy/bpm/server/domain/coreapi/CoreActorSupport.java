package com.decurret_dcp.dcjpy.bpm.server.domain.coreapi;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bpm.server.application.holders.exception.UpdateDcAccountException;
import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiFindAccountDetailResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.DcAccountEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.entity.ServiceOwnerEntity;
import com.decurret_dcp.dcjpy.bpm.server.domain.exception.ResourceNotFoundException;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.DcAccountRepository;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.ServiceOwnerRepository;
import com.decurret_dcp.dcjpy.bpm.server.domain.repository.command.FindDcAccountCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.Either;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.AccountStatus;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ValidatorId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class CoreActorSupport {

    private final CoreAccountsApi coreAccountsApi;

    private final DcAccountRepository dcAccountRepository;

    private final ServiceOwnerRepository serviceOwnerRepository;

    public CoreActorInfo findActor(FindDcAccountCommand findCommand) {
        DcAccountEntity dcAccount = this.dcAccountRepository.findDcAccount(findCommand);
        if (dcAccount == null) {
            throw new ResourceNotFoundException("DcBankNumber is not belonged. " + findCommand);
        }

        ServiceOwnerEntity serviceOwner = this.serviceOwnerRepository.findOwner(findCommand.serviceId);

        return new CoreActorInfo(dcAccount, serviceOwner);
    }

    public CoreApiFindAccountDetailResult findActiveAccount(FindDcAccountCommand findCommand) {
        // 2. DC口座テーブルから自身のアカウントIDを取得する
        // 3. サービスオーナーテーブルからバリデータIDを取得する
        CoreActorInfo ownerInfo = this.findActor(findCommand);

        return this.findActiveCoreAccount(ownerInfo);
    }

    public CoreApiFindAccountDetailResult findActiveCoreAccount(CoreActorInfo ownerInfo) {
        ValidatorId validatorId = ownerInfo.validatorId();
        AccountId accountId = ownerInfo.accountId();

        // 4. CoreのAPI「口座詳細取得」を呼び出す。
        Either<CoreApiFindAccountDetailResult> either = this.coreAccountsApi.findAccount(validatorId, accountId);

        // 自身のアカウントが取得できない場合はシステムエラーとする。
        if (either.isSuccess() == false) {
            throw either.toUnexpectedException();
        }

        // 5. 自身のアカウントの状態を確認する
        if (either.success().accountStatus != AccountStatus.ACTIVE) {
            // 自身のアカウントがアクティブではない場合はエラーとする。
            throw UpdateDcAccountException.fromBpm(MessageCode.INVALID_DC_ACCOUNT_STATUS);
        }

        return either.success();
    }
}
