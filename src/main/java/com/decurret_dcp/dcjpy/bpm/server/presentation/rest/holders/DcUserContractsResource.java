package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders;

import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.decurret_dcp.dcjpy.bpm.server.application.sharers.ContractApplication;
import com.decurret_dcp.dcjpy.bpm.server.application.sharers.command.CallContractCommand;
import com.decurret_dcp.dcjpy.bpm.server.application.sharers.command.SendContractCommand;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiCallContractResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.coreapi.result.CoreApiSendContractResult;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcUserAuthorityKey;
import com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.DcUserRoleType;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.request.CallContractRequest;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.request.SendContractRequest;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.CallContractResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.rest.shares.response.SendContractResponse;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.BpmDcUserDetail;
import com.decurret_dcp.dcjpy.bpm.server.presentation.security.annotation.DcUserAuthorize;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@RestController
@RequestMapping("/holders/contracts")
@Validated
public class DcUserContractsResource {

    private final ContractApplication application;

    /**
     * U95-91. ユーザ向け コントラクトcall.
     *
     * @param userDetail ユーザー情報
     * @param request リクエスト
     *
     * @return 処理結果
     */
    @PostMapping("/call")
    @DcUserAuthorize(
            roleType = { DcUserRoleType.OPERATOR, DcUserRoleType.INDIVIDUAL },
            authority = DcUserAuthorityKey.EXEC_CONTRACT,
            allowApplyingAccount = true
    )
    public CallContractResponse call(
            @AuthenticationPrincipal BpmDcUserDetail userDetail,
            @RequestBody @Validated CallContractRequest request
    ) {
        CallContractCommand command = request.initCommand(userDetail);
        CoreApiCallContractResult result = this.application.call(command);

        return CallContractResponse.create(result);
    }

    /**
     * U95-92. ユーザ向け コントラクトsend.
     *
     * @param userDetail ユーザー情報
     * @param request リクエスト
     *
     * @return 処理結果
     */
    @PostMapping("/send")
    @DcUserAuthorize(
            roleType = { DcUserRoleType.OPERATOR, DcUserRoleType.INDIVIDUAL },
            authority = DcUserAuthorityKey.EXEC_CONTRACT,
            allowApplyingAccount = true
    )
    public SendContractResponse send(
            @AuthenticationPrincipal BpmDcUserDetail userDetail,
            @RequestBody @Validated SendContractRequest request
    ) {
        SendContractCommand command = request.initCommand(userDetail);
        CoreApiSendContractResult result = this.application.send(command);

        return SendContractResponse.create(result);
    }
}
