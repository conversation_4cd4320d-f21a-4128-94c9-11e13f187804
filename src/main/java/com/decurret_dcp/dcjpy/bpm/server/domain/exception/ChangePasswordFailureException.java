package com.decurret_dcp.dcjpy.bpm.server.domain.exception;

import com.decurret_dcp.dcjpy.bpm.server.domain.MessageCode;

public class ChangePasswordFailureException extends BpmServerException {

    private final MessageCode messageCode;
    private ChangePasswordFailureException(MessageCode messageCode, String message) {
        super(message);
        this.messageCode = messageCode;
    }

    private ChangePasswordFailureException(MessageCode messageCode, String message, Throwable cause) {
        super(message, cause);
        this.messageCode = messageCode;
    }

    public static ChangePasswordFailureException invalidPreviousPassword(String username, Throwable cause) {
        String message = "Previous password is invalid. username : " + username;
        return new ChangePasswordFailureException(MessageCode.PREVIOUS_PASSWORD_IS_INVALID, message, cause);
    }

    public static ChangePasswordFailureException invalidNewPassword(String username) {
        String message = "New password is invalid. username : " + username;
        return new ChangePasswordFailureException(MessageCode.CAN_NOT_USE_PROPOSED_PASSWORD, message);
    }

    public static ChangePasswordFailureException invalidNewPassword(String username, Throwable cause) {
        String message = "New password is invalid. username : " + username;
        return new ChangePasswordFailureException(MessageCode.CAN_NOT_USE_PROPOSED_PASSWORD, message, cause);
    }

    @Override
    public MessageCode getMessageCode() {
        return this.messageCode;
    }
}
