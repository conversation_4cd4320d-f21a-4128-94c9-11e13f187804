package com.decurret_dcp.dcjpy.bpm.signer.application.result;

import com.decurret_dcp.dcjpy.bpm.signer.domain.value.AccountId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class SignResult {

    public final AccountId accountId;

    @ToString.Exclude
    public final String accountSignature;

    @ToString.Exclude
    public final String info;
}
