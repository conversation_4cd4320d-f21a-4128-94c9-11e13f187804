# ã¢ããªã±ã¼ã·ã§ã³è¨­å®
## ã¹ã±ã¸ã¥ã¼ã«ã«ä½¿ç¨ããã¹ã¬ããæ°
spring.task.scheduling.pool.size=5
## ã¡ã¤ã³WebSocket
app.webSocketUriHost=${WEBSOCKET_URI_HOST:host.docker.internal}
app.webSocketUriPort=${WEBSOCKET_URI_PORT:8541}
## ãµãWebSocket
app.useSubWebSocket=${USE_SUB_WEBSOCKET:true}
app.subWebSocketUriHost=${SUB_WEBSOCKET_URI_HOST:host.docker.internal}
app.subWebSocketUriPort=${SUB_WEBSOCKET_URI_PORT:8541}
app.requestTimeoutSec=${REQUEST_TIMEOUT_SEC:10000}
app.region=${REGION:ap-northeast-1}
app.contractFileBucketName=${CONTRACT_BUCKET_NAME:abijson-local-bucket}
app.externalContractFileBucketName=${EXTERNAL_CONTRACT_BUCKET_NAME:external-abijson-local-bucket}
app.subscriptionCheckInterval=${SUBSCRIPTION_CHECK_INTERVAL:3000}
app.gasLimit=${GAS_LIMIT:6721975}
# SQS
sqs.sqsQueueUri=${SQS_QUEUE_URI:http://localhost:9324/queue/dev-queue.fifo}
sqs.maxFetchSize=${SQS_MAX_FETCH_SIZE:5}
# Transfer
transfer.sendTransferInterval=${SEND_TRANSFER_INTERVAL:2000}
transfer.transferContractName=${TRANSFER_CONTRACT_NAME:Token}
transfer.transferMethodName=${TRANSFER_METHOD_NAME:transferSingle}
transfer.transferSingleMethodName=${TRANSFER_SINGLE_METHOD_NAME:transferSingle}
transfer.transferBatchMethodName=${TRANSFER_BATCH_METHOD_NAME:transferBatch}
transfer.transferCallContractName=${TRANSFER_CALL_CONTRACT_NAME:FinancialToken}
transfer.transferCallMethodName=${TRANSFER_CALL_METHOD_NAME:checkTransaction}
# ã­ã¼ã«ã«è¨­å®
## S3
local.useLocalS3Bucket=${USE_LOCAL_S3:true}
local.localS3Uri=${LOCAL_S3_URI:http://minio:9000}
local.s3AccessKey=access123
local.s3SecretKey=secret123
local.localExternalS3Uri=${LOCAL_EXTERNAL_S3_URI:http://minio-external:9000}
local.externalS3AccessKey=access123
local.externalS3SecretKey=secret123
## SQS
local.useLocalSQS=${USE_LOCAL_SQS:true}
local.localSQSUri=${LOCAL_SQS_URI:http://sqs:9324}
local.sqsAccessKey=access123
local.sqsSecretKey=secret123

# debugã­ã°ãæå¹ã«ããå ´åã¯ã³ã¡ã³ããå¤ã
logging.level.jp.co.decurret.dcbg.dcf_bcclient_sandbox=${APP_LOG_LEVEL:debug}
