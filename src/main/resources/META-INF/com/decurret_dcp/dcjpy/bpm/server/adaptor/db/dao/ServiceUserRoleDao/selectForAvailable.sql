SELECT
    *
FROM service_user_role
WHERE service_id = /* command.serviceId */'0'
    AND EXISTS (
        /** 自身より下位の権限のみ取得するための制御 */
        SELECT 1
        FROM service_user_role owner_role
        WHERE owner_role.role_id = /* command.ownerRoleId */'00000000-0000-4000-0000-000000000020'
            AND CASE service_user_role.role_type
                /*%for roleType : @com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ServiceUserRoleType@values() */
                WHEN /* roleType.getValue() */'service_owner' THEN /* roleType.getLevel() */9
                /*%end*/
                ELSE 99
            END < CASE owner_role.role_type
                /*%for roleType : @com.decurret_dcp.dcjpy.bpm.server.domain.value.atomic.ServiceUserRoleType@values() */
                WHEN /* roleType.getValue() */'service_owner' THEN /* roleType.getLevel() */9
                /*%end*/
                ELSE 99
            END
    )
ORDER BY
    CASE role_type
        WHEN 'service_owner' THEN 10
        WHEN 'user_owner' THEN 20
        WHEN 'operator' THEN 30
        WHEN 'reviewer' THEN 40
        ELSE 99
    END /*# sortOrder.name() */ ,
    role_id /*# sortOrder.name() */