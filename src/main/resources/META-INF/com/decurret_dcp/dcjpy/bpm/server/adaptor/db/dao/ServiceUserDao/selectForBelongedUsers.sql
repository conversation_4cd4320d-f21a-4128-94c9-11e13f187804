SELECT
    service_user.sign_in_id,
    service_user.user_name,
    CASE
        WHEN service_user.user_status = 'inactive' THEN 'inactive'
        WHEN failure.lock_expires_at >= CURRENT_TIMESTAMP THEN 'temporary_suspended'
        ELSE service_user.user_status
    END AS user_status,
    service_user.role_id,
    service_user_role.role_name,
    service_user_role.role_type,
    service_user.registered_at
FROM service_user
     INNER JOIN service_user_role
         ON service_user.role_id = service_user_role.role_id
     LEFT JOIN service_user_sign_in_failure failure
         ON service_user.sign_in_id = failure.sign_in_id
WHERE
/*%if condition.signInId != null */
    service_user.sign_in_id LIKE /* @prefix(condition.signInId) */'SID%'
    AND
/*%end*/
/*%if condition.userName != null */
    service_user.user_name LIKE /* @prefix(condition.userName) */'test%'
    AND
/*%end*/
    service_user.service_id = /* serviceId */''
    AND service_user_role.role_type IN /* filterRoleType */('')
ORDER BY service_user.registered_at /*# sortOrder.name() */