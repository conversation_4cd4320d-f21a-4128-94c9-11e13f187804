package jp.co.decurret.dcbg.dcf_bcclient_sandbox.util

import akka.actor.Terminated
import com.typesafe.config.Config
import com.typesafe.config.ConfigFactory
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.ApplicationProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.LocalProperties
import jp.co.decurret.dcbg.dcf_bcclient_sandbox.properties.SQSProperties
import org.elasticmq.server.ElasticMQServer
import org.elasticmq.server.config.ElasticMQServerConfig
import scala.Function0
import software.amazon.awssdk.services.sqs.model.DeleteMessageBatchResponse
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.SendMessageResponse
import spock.lang.Specification

class SQSUtilSpec extends Specification {
    private static ApplicationProperties applicationProperties
    private static SQSProperties sqsProperties
    private static LocalProperties localProperties
    private static Function0<Terminated> shutdown
    private static SQSUtil sqsUtil

    private static final String MESSAGE_GROUP_ID = "test";

    def setupSpec() {
        applicationProperties = new ApplicationProperties()
        applicationProperties.setRegion("ap-northeast-1")

        sqsProperties = new SQSProperties()
        sqsProperties.setSqsQueueUri("http://127.0.0.1:9326/queue/test-queue.fifo")

        localProperties = new LocalProperties()
        localProperties.setUseLocalSqs(true)
        localProperties.setLocalSqsUri("http://127.0.0.1:9326")
        localProperties.setSqsAccessKey("access123")
        localProperties.setSqsSecretKey("secret123")

        sqsUtil = new SQSUtil(applicationProperties, localProperties)

        Config config = ConfigFactory.load("elasticmq.conf")
        ElasticMQServer server = new ElasticMQServer(new ElasticMQServerConfig(config))
        shutdown = server.start()
    }

    def cleanupSpec() {
        shutdown.apply()
    }

    def "Send, Receive"() {
        when:
        SendMessageResponse response = sqsUtil.sendMessage(
                "test message",
                this.sqsProperties.getSqsQueueUri(),
                MESSAGE_GROUP_ID,
                UUID.randomUUID().toString()
        )

        then:
        response != null
        List<Message> messages = sqsUtil.receiveMessage(5, this.sqsProperties.getSqsQueueUri())
        messages.size() == 1
        messages.get(0).body() == "test message"
    }

    def "Send, Receive, Delete"() {
        when:
        sqsUtil.sendMessage(
                "test message 2",
                this.sqsProperties.getSqsQueueUri(),
                MESSAGE_GROUP_ID,
                UUID.randomUUID().toString()
        )
        List<Message> messages = sqsUtil.receiveMessage(5, this.sqsProperties.getSqsQueueUri())
        DeleteMessageBatchResponse response = sqsUtil.deleteMessageBatch(messages, this.sqsProperties.getSqsQueueUri())

        then:
        response != null
        List<Message> m = sqsUtil.receiveMessage(5, this.sqsProperties.getSqsQueueUri())
        m.size() == 0
    }
}
