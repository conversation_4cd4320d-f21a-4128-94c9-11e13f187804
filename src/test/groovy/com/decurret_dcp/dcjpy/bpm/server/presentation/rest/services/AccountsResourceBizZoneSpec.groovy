package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services

import com.decurret_dcp.dcjpy.bpm.server.BpmServerMain
import com.decurret_dcp.dcjpy.bpm.server.adaptor.CognitoAdaptor
import com.decurret_dcp.dcjpy.bpm.server.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bpm.server.helper.http.AdhocHttpHelper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.message.BasicNameValuePair
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest(
        classes = BpmServerMain.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class AccountsResourceBizZoneSpec extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    static Sql sql

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockCore

    AdhocHttpHelper adhocHttpHelper

    @Autowired
    CognitoAdaptor cognitoAdaptor

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        // BizZone の設定
        registry.add("bpmserver.zone_type", () -> "business_zone")
    }

    def setupSpec() {
        wiremockCore = new WireMockServer(options().port(8080))
        wiremockCore.start()
    }

    def cleanupSpec() {
        wiremockCore.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        adhocHttpHelper = new AdhocHttpHelper(applicationPort, cognitoAdaptor)
    }

    def cleanup() {
        // Do nothing.
    }

    // B31-01 銀行/事業者向け アカウント一覧取得
    def "fetchAccounts_1-1_BizZone:利用申込中、アカウント管理者:初期設定待ちの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts?sort_order=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                                {
                                "accounts": [
                                  {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸﾗｯｸﾕｰｻﾞ",
                                  "account_status": "applying",
                                  "reason_code": 10000,
                                  "balance": 210000
                                  }
                                ],
                                "offset": 0,
                                "limit": 1,
                                "total": 1
                                }
                                """)
                ))

        // テスト対象のアカウントに紐づくアカウント管理者が3人いるので、2人削除(アカウント管理者は、アカウントにつき1人のはず)
        // BizZoneのデータにするため、銀行口座の紐付けを消す。
        sql.execute("""
        DELETE FROM dc_user_phone WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        DELETE FROM dc_user WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        """)
        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-1234-1234-1', '0', '<EMAIL>');
        """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'initializing' WHERE sign_in_id = 'SID01AA-1001';
        """)


        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        sql.execute("""
        INSERT INTO dc_user(
                   sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
        VALUES
        ('SID01AA-1051', 'ｻｲﾝｲﾝﾃｲｼｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'suspended', '0',
        '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
        ('SID01AA-1061', 'ﾑｺｳｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'inactive', '0',
       '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900')
        """)
        sql.execute("""
            INSERT INTO dc_user_phone(sign_in_id, phone_number)
            VALUES 
            ('SID01AA-1051', '***********'),
            ('SID01AA-1061', '***********')
            """)

        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("accounts").get(0).get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("accounts").get(0).get("account_name").asText() == "モックラックユーザ"
        responseBody.get("accounts").get(0).get("account_status").asText() == "applying"
        responseBody.get("accounts").get(0).get("email_address").asText() == "<EMAIL>"

        responseBody.get("accounts").get(0).get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("accounts").get(0).get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("accounts").get(0).get("owner_user").get("user_status").asText() == "initializing"
        responseBody.get("accounts").get(0).get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("accounts").get(0).get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("accounts").get(0).get("applied_at"))
        Objects.isNull(responseBody.get("accounts").get(0).get("registered_at"))

        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 1
        responseBody.get("paging").get("total").asLong() == 1
    }

    def "fetchAccounts_1-2_BizZone:アクティブ、アカウント管理者:アクティブの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts?sort_order=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                                {
                                "accounts": [
                                  {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸﾗｯｸﾕｰｻﾞ",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "balance": 210000,
                                  "applied_at": "2023-12-15T09:13:16Z"
                                  }
                                ],
                                "offset": 0,
                                "limit": 1,
                                "total": 1
                                }
                                """)
                ))

        // テスト対象のアカウントに紐づくアカウント管理者が3人いるので、2人削除(アカウント管理者は、アカウントにつき1人のはず)
        // BizZoneのデータにするため、銀行口座の紐付けを消す。
        sql.execute("""
        DELETE FROM dc_user_phone WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        DELETE FROM dc_user WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        """)
        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-1234-1234-1', '0', '<EMAIL>');
        """)


        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        sql.execute("""
        INSERT INTO dc_user(
                   sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
        VALUES
        ('SID01AA-1051', 'ｻｲﾝｲﾝﾃｲｼｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'suspended', '0',
        '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
        ('SID01AA-1061', 'ﾑｺｳｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'inactive', '0',
       '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900')
        """)
        sql.execute("""
            INSERT INTO dc_user_phone(sign_in_id, phone_number)
            VALUES 
            ('SID01AA-1051', '***********'),
            ('SID01AA-1061', '***********')
            """)

        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("accounts").get(0).get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("accounts").get(0).get("account_name").asText() == "モックラックユーザ"
        responseBody.get("accounts").get(0).get("account_status").asText() == "active"
        responseBody.get("accounts").get(0).get("email_address").asText() == "<EMAIL>"

        responseBody.get("accounts").get(0).get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("accounts").get(0).get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("accounts").get(0).get("owner_user").get("user_status").asText() == "active"
        responseBody.get("accounts").get(0).get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("accounts").get(0).get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("accounts").get(0).get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("accounts").get(0).get("registered_at"))

        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 1
        responseBody.get("paging").get("total").asLong() == 1
    }

    def "fetchAccounts_1-3_BizZone:解約申込中、アカウント管理者:サインイン停止の場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts?sort_order=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                                {
                                "accounts": [
                                  {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸﾗｯｸﾕｰｻﾞ",
                                  "account_status": "terminating",
                                  "reason_code": 10000,
                                  "balance": 210000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "terminating_at": "2023-12-17T09:13:16Z"
                                  }
                                ],
                                "offset": 0,
                                "limit": 1,
                                "total": 1
                                }
                                """)
                ))

        // テスト対象のアカウントに紐づくアカウント管理者が3人いるので、2人削除(アカウント管理者は、アカウントにつき1人のはず)
        // BizZoneのデータにするため、銀行口座の紐付けを消す。
        sql.execute("""
        DELETE FROM dc_user_phone WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        DELETE FROM dc_user WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        """)
        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-1234-1234-1', '0', '<EMAIL>');
        """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'suspended' WHERE sign_in_id = 'SID01AA-1001';
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        sql.execute("""
        INSERT INTO dc_user(
                   sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
        VALUES
        ('SID01AA-1051', 'ｻｲﾝｲﾝﾃｲｼｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'suspended', '0',
        '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
        ('SID01AA-1061', 'ﾑｺｳｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'inactive', '0',
       '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900')
        """)
        sql.execute("""
            INSERT INTO dc_user_phone(sign_in_id, phone_number)
            VALUES 
            ('SID01AA-1051', '***********'),
            ('SID01AA-1061', '***********')
            """)

        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("accounts").get(0).get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("accounts").get(0).get("account_name").asText() == "モックラックユーザ"
        responseBody.get("accounts").get(0).get("account_status").asText() == "terminating"
        responseBody.get("accounts").get(0).get("email_address").asText() == "<EMAIL>"

        responseBody.get("accounts").get(0).get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("accounts").get(0).get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("accounts").get(0).get("owner_user").get("user_status").asText() == "suspended"
        responseBody.get("accounts").get(0).get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("accounts").get(0).get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("accounts").get(0).get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("accounts").get(0).get("registered_at"))

        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 1
        responseBody.get("paging").get("total").asLong() == 1
    }

    def "fetchAccounts_1-4_BizZone:解約済み、アカウント管理者:ユーザ無効の場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts?sort_order=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                                {
                                "accounts": [
                                  {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸﾗｯｸﾕｰｻﾞ",
                                  "account_status": "terminated",
                                  "reason_code": 10000,
                                  "balance": 210000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "terminating_at": "2023-12-17T09:13:16Z",
                                  "terminated_at": "2023-12-18T09:13:16Z"
                                  }
                                ],
                                "offset": 0,
                                "limit": 1,
                                "total": 1
                                }
                                """)
                ))

        // テスト対象のアカウントに紐づくアカウント管理者が3人いるので、2人削除(アカウント管理者は、アカウントにつき1人のはず)
        // BizZoneのデータにするため、銀行口座の紐付けを消す。
        sql.execute("""
        DELETE FROM dc_user_phone WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        DELETE FROM dc_user WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        """)
        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-1234-1234-1', '0', '<EMAIL>');
        """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'inactive' WHERE sign_in_id = 'SID01AA-1001';
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        sql.execute("""
        INSERT INTO dc_user(
                   sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
        VALUES
        ('SID01AA-1051', 'ｻｲﾝｲﾝﾃｲｼｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'suspended', '0',
        '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
        ('SID01AA-1061', 'ﾑｺｳｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'inactive', '0',
       '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900')
        """)
        sql.execute("""
            INSERT INTO dc_user_phone(sign_in_id, phone_number)
            VALUES 
            ('SID01AA-1051', '***********'),
            ('SID01AA-1061', '***********')
            """)

        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("accounts").get(0).get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("accounts").get(0).get("account_name").asText() == "モックラックユーザ"
        responseBody.get("accounts").get(0).get("account_status").asText() == "terminated"
        responseBody.get("accounts").get(0).get("email_address").asText() == "<EMAIL>"

        responseBody.get("accounts").get(0).get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("accounts").get(0).get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("accounts").get(0).get("owner_user").get("user_status").asText() == "inactive"
        responseBody.get("accounts").get(0).get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("accounts").get(0).get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("accounts").get(0).get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("accounts").get(0).get("registered_at"))

        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 1
        responseBody.get("paging").get("total").asLong() == 1
    }

    def "fetchAccounts_1-5_BizZone:利用申込中、業務担当者:初期設定待ちの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts?sort_order=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                                {
                                "accounts": [
                                  {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸﾗｯｸﾕｰｻﾞ",
                                  "account_status": "applying",
                                  "reason_code": 10000,
                                  "balance": 210000
                                  }
                                ],
                                "offset": 0,
                                "limit": 1,
                                "total": 1
                                }
                                """)
                ))

        // テスト対象のアカウントに紐づくアカウント管理者が3人いるので、2人削除(アカウント管理者は、アカウントにつき1人のはず)
        // BizZoneのデータにするため、銀行口座の紐付けを消す。
        sql.execute("""
        DELETE FROM dc_user_phone WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        DELETE FROM dc_user WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        """)
        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-1234-1234-1', '0', '<EMAIL>');
        """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'initializing' WHERE sign_in_id = 'SID01AA-1001';
        """)


        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        sql.execute("""
        INSERT INTO dc_user(
                   sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
        VALUES
        ('SID01AA-1051', 'ｻｲﾝｲﾝﾃｲｼｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'suspended', '0',
        '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
        ('SID01AA-1061', 'ﾑｺｳｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'inactive', '0',
       '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900')
        """)
        sql.execute("""
            INSERT INTO dc_user_phone(sign_in_id, phone_number)
            VALUES 
            ('SID01AA-1051', '***********'),
            ('SID01AA-1061', '***********')
            """)

        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("accounts").get(0).get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("accounts").get(0).get("account_name").asText() == "モックラックユーザ"
        responseBody.get("accounts").get(0).get("account_status").asText() == "applying"
        responseBody.get("accounts").get(0).get("email_address").asText() == "<EMAIL>"

        responseBody.get("accounts").get(0).get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("accounts").get(0).get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("accounts").get(0).get("owner_user").get("user_status").asText() == "initializing"
        responseBody.get("accounts").get(0).get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("accounts").get(0).get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("accounts").get(0).get("applied_at"))
        Objects.isNull(responseBody.get("accounts").get(0).get("registered_at"))

        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 1
        responseBody.get("paging").get("total").asLong() == 1
    }

    def "fetchAccounts_1-6_BizZone:利用申込中、業務承認者:初期設定待ちの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts?sort_order=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                                {
                                "accounts": [
                                  {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸﾗｯｸﾕｰｻﾞ",
                                  "account_status": "applying",
                                  "reason_code": 10000,
                                  "balance": 210000
                                  }
                                ],
                                "offset": 0,
                                "limit": 1,
                                "total": 1
                                }
                                """)
                ))

        // テスト対象のアカウントに紐づくアカウント管理者が3人いるので、2人削除(アカウント管理者は、アカウントにつき1人のはず)
        // BizZoneのデータにするため、銀行口座の紐付けを消す。
        sql.execute("""
        DELETE FROM dc_user_phone WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        DELETE FROM dc_user WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        """)
        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-1234-1234-1', '0', '<EMAIL>');
        """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'initializing' WHERE sign_in_id = 'SID01AA-1001';
        """)


        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        sql.execute("""
        INSERT INTO dc_user(
                   sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
        VALUES
        ('SID01AA-1051', 'ｻｲﾝｲﾝﾃｲｼｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'suspended', '0',
        '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
        ('SID01AA-1061', 'ﾑｺｳｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'inactive', '0',
       '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900')
        """)
        sql.execute("""
            INSERT INTO dc_user_phone(sign_in_id, phone_number)
            VALUES 
            ('SID01AA-1051', '***********'),
            ('SID01AA-1061', '***********')
            """)

        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("accounts").get(0).get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("accounts").get(0).get("account_name").asText() == "モックラックユーザ"
        responseBody.get("accounts").get(0).get("account_status").asText() == "applying"
        responseBody.get("accounts").get(0).get("email_address").asText() == "<EMAIL>"

        responseBody.get("accounts").get(0).get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("accounts").get(0).get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("accounts").get(0).get("owner_user").get("user_status").asText() == "initializing"
        responseBody.get("accounts").get(0).get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("accounts").get(0).get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("accounts").get(0).get("applied_at"))
        Objects.isNull(responseBody.get("accounts").get(0).get("registered_at"))

        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 1
        responseBody.get("paging").get("total").asLong() == 1
    }

    def "fetchAccounts_1-7_別テナントのユーザが自身に所属するアカウント一覧を取得できること - BizZone"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AB-3001", "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setServiceId("0d367be20aed7707d71dce4dbd8825f1")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts?sort_order=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                                {
                                "accounts": [
                                  {
                                  "account_id": "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "balance": 210000,
                                  "applied_at": "2023-12-15T09:13:16Z"
                                  }
                                ],
                                "offset": 0,
                                "limit": 1,
                                "total": 1
                                }
                                """)
                ))

        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-3333-0103-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-3333-0103-1', '0d367be2', '<EMAIL>');
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("accounts").get(0).get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("accounts").get(0).get("account_name").asText() == "モックギンコウコウザ３"
        responseBody.get("accounts").get(0).get("account_status").asText() == "active"
        responseBody.get("accounts").get(0).get("email_address").asText() == "<EMAIL>"

        responseBody.get("accounts").get(0).get("owner_user").get("sign_in_id").asText() == "SID01AC-3001"
        responseBody.get("accounts").get(0).get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＣ"
        responseBody.get("accounts").get(0).get("owner_user").get("user_status").asText() == "active"
        responseBody.get("accounts").get(0).get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("accounts").get(0).get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("accounts").get(0).get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("accounts").get(0).get("registered_at"))


        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 1
        responseBody.get("paging").get("total").asLong() == 1

        cleanup:
        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_name, bank_code, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-3333-0103-1', '0d367be2', '0310-302-02-6634567', 'モック銀行', '0310', '302', '02', '6634567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-3333-0103-1' AND service_id = '0d367be2';
            """)
    }

    def "fetchAccounts_9-1_ユーザ管理者は利用できないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery("sort_order", "desc")
                .addQuery("offset", 0)
                .addQuery("limit", 100)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "findAccount_1-5_BizZone:アカウントがapplyingの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts/DC001-1234-1234-1")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "applying",
                                  "reason_code": 10000,
                                  "zone_id": "3000",
                                  "zone_name": "モックギンコウゾーン",
                                  "balance": 210000,
                                  "cache_balance": 200000,
                                  "mint_limit": 9999,
                                  "burn_limit": 9999,
                                  "transfer_limit": 9999,
                                  "charge_limit": 9999,
                                  "cumulative_limit": 500,
                                  "cumulative_amount": 0,
                                  "cumulative_date": "2023-12-15",
                                  "applied_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        sql.execute("""
        INSERT INTO dc_account_reason (dc_bank_number, service_id, operation_type, reason_code, reason_detail, operated_at)
        VALUES
        ('DC001-1234-1234-1', '0', 'account_frozen', 'UAST0007', '凍結', '2023-12-01T12:00:00+0900');
        UPDATE dc_user SET user_status = 'initializing' WHERE sign_in_id = 'SID01AA-1001';
        """)

        // BizZoneのデータにするため、銀行との紐付けを削除
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES ('DC001-1234-1234-1', '0', '<EMAIL>');
        INSERT INTO dc_account_settlement (dc_bank_number,service_id , settlement_type, scheduled_day) 
        VALUES ('DC001-1234-1234-1', '0', 'immediate', NULL); 
        """)

        sql.execute("""
        UPDATE dc_user_sign_in_history SET last_signed_in_at = '2022-12-01T12:00:00+0900' WHERE sign_in_id = 'SID01AA-1001'
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)
        sql.execute("""
        DELETE FROM  dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
        """)
        sql.execute("""
        INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
        VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
        DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        DELETE FROM dc_account_settlement WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)
        sql.execute("""
        UPDATE dc_user_sign_in_history SET last_signed_in_at = NULL WHERE sign_in_id = 'SID01AA-1001'
        """)


        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_status").asText() == "applying"
        Objects.isNull(responseBody.get("balance"))

        responseBody.get("zone").get("zone_id").asInt() == 3000
        responseBody.get("zone").get("zone_name").asText() == "モックギンコウゾーン"

        responseBody.get("email_address").asText() == "<EMAIL>"

        responseBody.get("settlement").get("settlement_type").asText() == "immediate"
        Objects.isNull(responseBody.get("settlement").get("scheduled_day"))

        Objects.isNull(responseBody.get("operation_limit"))
        Objects.isNull(responseBody.get("cumulation"))

        responseBody.get("operated_reason").get("operated_reason_type").asText() == "account_frozen"
        responseBody.get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("operated_reason").get("reason_detail").asText() == "凍結"
        responseBody.get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("owner_user").get("user_status").asText() == "initializing"

        responseBody.get("owner_user").get("role").get("role_id").asText() == "********-0000-4000-0000-************"
        responseBody.get("owner_user").get("role").get("role_name").asText() == "口座管理者"
        responseBody.get("owner_user").get("role").get("role_type").asText() == "account_owner"

        responseBody.get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("owner_user").get("operated_reason"))

        responseBody.get("owner_user").get("last_signed_at").asText() == "2022-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("bank_account"))

        responseBody.get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("registered_at"))
        Objects.isNull(responseBody.get("terminating_at"))
        Objects.isNull(responseBody.get("terminated_at"))
    }

    def "findAccount_1-6_BizZone:アカウントがactiveの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts/DC001-1234-1234-1")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3000",
                                  "zone_name": "モックギンコウゾーン",
                                  "balance": 210000,
                                  "cache_balance": 200000,
                                  "mint_limit": 9999,
                                  "burn_limit": 9999,
                                  "transfer_limit": 9999,
                                  "charge_limit": 9999,
                                  "cumulative_limit": 500,
                                  "cumulative_amount": 50000,
                                  "cumulative_date": "2023-12-15",
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        sql.execute("""
        INSERT INTO dc_account_reason (dc_bank_number, service_id, operation_type, reason_code, reason_detail, operated_at)
        VALUES
        ('DC001-1234-1234-1', '0', 'account_activated', 'UAST0007', '凍結', '2023-12-01T12:00:00+0900');
        UPDATE dc_user SET role_id = '********-0000-4000-0000-********0011' WHERE sign_in_id = 'SID01AA-1001';
        INSERT INTO dc_user_reason (sign_in_id, operation_type, reason_code, reason_detail, operated_at) 
        VALUES ('SID01AA-1001', 'user_activated', 'UAST0007', 'アクティブ化',  '2023-12-01T12:00:00+0900');
        """)

        // BizZoneのデータにするため、銀行との紐付けを削除
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES ('DC001-1234-1234-1', '0', '<EMAIL>');
        INSERT INTO dc_account_settlement (dc_bank_number,service_id , settlement_type, scheduled_day) 
        VALUES ('DC001-1234-1234-1', '0', 'monthly_scheduled', '15'); 
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)
        sql.execute("""
        DELETE FROM  dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        UPDATE dc_user SET role_id = '********-0000-4000-0000-************' WHERE sign_in_id = 'SID01AA-1001';
        DELETE FROM dc_user_reason WHERE sign_in_id = 'SID01AA-1001';
        """)
        sql.execute("""
        INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
        VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
        DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        DELETE FROM dc_account_settlement WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)


        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_status").asText() == "active"
        Objects.isNull(responseBody.get("balance"))

        responseBody.get("zone").get("zone_id").asInt() == 3000
        responseBody.get("zone").get("zone_name").asText() == "モックギンコウゾーン"

        responseBody.get("email_address").asText() == "<EMAIL>"

        responseBody.get("settlement").get("settlement_type").asText() == "monthly_scheduled"
        responseBody.get("settlement").get("scheduled_day").asText() == "15"

        Objects.isNull(responseBody.get("operation_limit"))
        Objects.isNull(responseBody.get("cumulation"))

        responseBody.get("operated_reason").get("operated_reason_type").asText() == "account_activated"
        responseBody.get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("operated_reason").get("reason_detail").asText() == "凍結"
        responseBody.get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("owner_user").get("user_status").asText() == "active"

        responseBody.get("owner_user").get("role").get("role_id").asText() == "********-0000-4000-0000-********0011"
        responseBody.get("owner_user").get("role").get("role_name").asText() == "ユーザ管理者"
        responseBody.get("owner_user").get("role").get("role_type").asText() == "user_owner"

        responseBody.get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("operated_reason").get("operated_reason_type").asText() == "user_activated"
        responseBody.get("owner_user").get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("owner_user").get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("owner_user").get("operated_reason").get("reason_detail").asText() == "アクティブ化"
        responseBody.get("owner_user").get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("owner_user").get("last_signed_at"))

        Objects.isNull(responseBody.get("bank_account"))

        responseBody.get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        responseBody.get("registered_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("terminating_at"))
        Objects.isNull(responseBody.get("terminated_at"))
    }

    def "findAccount_1-7_BizZone:アカウントがterminatingの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts/DC001-1234-1234-1")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "terminating",
                                  "reason_code": 10000,
                                  "zone_id": "3000",
                                  "zone_name": "モックギンコウゾーン",
                                  "balance": 210000,
                                  "cache_balance": 200000,
                                  "mint_limit": 9999,
                                  "burn_limit": 9999,
                                  "transfer_limit": 9999,
                                  "charge_limit": 9999,
                                  "cumulative_limit": 500,
                                  "cumulative_amount": 50000,
                                  "cumulative_date": "2023-12-15",
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z",
                                  "terminating_at": "2023-12-18T09:13:16Z"
                                }
                                """)
                ))

        sql.execute("""
        INSERT INTO dc_account_reason (dc_bank_number, service_id, operation_type, reason_code, reason_detail, operated_at)
        VALUES
        ('DC001-1234-1234-1', '0', 'account_terminating', 'UAST0007', '凍結', '2023-12-01T12:00:00+0900');
        UPDATE dc_user SET role_id = '********-0000-4000-0000-********0012' WHERE sign_in_id = 'SID01AA-1001';
        INSERT INTO dc_user_reason (sign_in_id, operation_type, reason_code, reason_detail, operated_at) 
        VALUES ('SID01AA-1001', 'user_suspended', 'UAST0007', 'アクティブ化',  '2023-12-01T12:00:00+0900');
        UPDATE dc_user SET user_status = 'suspended' WHERE sign_in_id = 'SID01AA-1001';
        """)

        // BizZoneのデータにするため、銀行との紐付けを削除
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES ('DC001-1234-1234-1', '0', '<EMAIL>');
        INSERT INTO dc_account_settlement (dc_bank_number,service_id , settlement_type, scheduled_day) 
        VALUES ('DC001-1234-1234-1', '0', 'monthly_scheduled', '15'); 
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)
        sql.execute("""
        DELETE FROM  dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        UPDATE dc_user SET role_id = '********-0000-4000-0000-************' WHERE sign_in_id = 'SID01AA-1001';
        DELETE FROM dc_user_reason WHERE sign_in_id = 'SID01AA-1001';
        """)
        sql.execute("""
        INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
        VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
        DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        DELETE FROM dc_account_settlement WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
            """)


        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_status").asText() == "terminating"
        Objects.isNull(responseBody.get("balance"))

        responseBody.get("zone").get("zone_id").asInt() == 3000
        responseBody.get("zone").get("zone_name").asText() == "モックギンコウゾーン"

        responseBody.get("email_address").asText() == "<EMAIL>"

        responseBody.get("settlement").get("settlement_type").asText() == "monthly_scheduled"
        responseBody.get("settlement").get("scheduled_day").asText() == "15"

        Objects.isNull(responseBody.get("operation_limit"))
        Objects.isNull(responseBody.get("cumulation"))

        responseBody.get("operated_reason").get("operated_reason_type").asText() == "account_terminating"
        responseBody.get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("operated_reason").get("reason_detail").asText() == "凍結"
        responseBody.get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("owner_user").get("user_status").asText() == "suspended"

        responseBody.get("owner_user").get("role").get("role_id").asText() == "********-0000-4000-0000-********0012"
        responseBody.get("owner_user").get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("owner_user").get("role").get("role_type").asText() == "operator"

        responseBody.get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("operated_reason").get("operated_reason_type").asText() == "user_suspended"
        responseBody.get("owner_user").get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("owner_user").get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("owner_user").get("operated_reason").get("reason_detail").asText() == "アクティブ化"
        responseBody.get("owner_user").get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("owner_user").get("last_signed_at"))

        Objects.isNull(responseBody.get("bank_account"))

        responseBody.get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        responseBody.get("registered_at").asText() == "2023-12-15T18:13:16+09:00"
        responseBody.get("terminating_at").asText() == "2023-12-18T18:13:16+09:00"
        Objects.isNull(responseBody.get("terminated_at"))
    }

    def "findAccount_1-8_BizZone:アカウントがterminatedの場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts/DC001-1234-1234-1")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "terminated",
                                  "reason_code": 10000,
                                  "zone_id": "3000",
                                  "zone_name": "モックギンコウゾーン",
                                  "cache_balance": 200000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z",
                                  "terminating_at": "2023-12-18T09:13:16Z",
                                  "terminated_at": "2023-12-18T09:13:16Z"
                                }
                                """)
                ))

        sql.execute("""
        INSERT INTO dc_account_reason (dc_bank_number, service_id, operation_type, reason_code, reason_detail, operated_at)
        VALUES
        ('DC001-1234-1234-1', '0', 'account_terminated', 'UAST0007', '凍結', '2023-12-01T12:00:00+0900');
        UPDATE dc_user SET role_id = '********-0000-4000-0000-********0013' WHERE sign_in_id = 'SID01AA-1001';
        INSERT INTO dc_user_reason (sign_in_id, operation_type, reason_code, reason_detail, operated_at) 
        VALUES ('SID01AA-1001', 'user_terminated', 'UAST0007', 'アクティブ化',  '2023-12-01T12:00:00+0900');
        UPDATE dc_user SET user_status = 'inactive' WHERE sign_in_id = 'SID01AA-1001';
        """)

        // BizZoneのデータにするため、銀行との紐付けを削除
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES ('DC001-1234-1234-1', '0', '<EMAIL>');
        INSERT INTO dc_account_settlement (dc_bank_number,service_id , settlement_type, scheduled_day) 
        VALUES ('DC001-1234-1234-1', '0', 'monthly_scheduled', '15'); 
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)
        sql.execute("""
        DELETE FROM  dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        UPDATE dc_user SET role_id = '********-0000-4000-0000-************' WHERE sign_in_id = 'SID01AA-1001';
        DELETE FROM dc_user_reason WHERE sign_in_id = 'SID01AA-1001';
        """)
        sql.execute("""
        INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
        VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
        DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        DELETE FROM dc_account_settlement WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
            """)


        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_status").asText() == "terminated"
        Objects.isNull(responseBody.get("balance"))

        responseBody.get("zone").get("zone_id").asInt() == 3000
        responseBody.get("zone").get("zone_name").asText() == "モックギンコウゾーン"

        responseBody.get("email_address").asText() == "<EMAIL>"

        responseBody.get("settlement").get("settlement_type").asText() == "monthly_scheduled"
        responseBody.get("settlement").get("scheduled_day").asText() == "15"

        Objects.isNull(responseBody.get("operation_limit"))
        Objects.isNull(responseBody.get("cumulation"))

        responseBody.get("operated_reason").get("operated_reason_type").asText() == "account_terminated"
        responseBody.get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("operated_reason").get("reason_detail").asText() == "凍結"
        responseBody.get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("sign_in_id").asText() == "SID01AA-1001"
        responseBody.get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＡ"
        responseBody.get("owner_user").get("user_status").asText() == "inactive"

        responseBody.get("owner_user").get("role").get("role_id").asText() == "********-0000-4000-0000-********0013"
        responseBody.get("owner_user").get("role").get("role_name").asText() == "業務承認者"
        responseBody.get("owner_user").get("role").get("role_type").asText() == "reviewer"

        responseBody.get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("operated_reason").get("operated_reason_type").asText() == "user_terminated"
        responseBody.get("owner_user").get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("owner_user").get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("owner_user").get("operated_reason").get("reason_detail").asText() == "アクティブ化"
        responseBody.get("owner_user").get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("owner_user").get("last_signed_at"))

        Objects.isNull(responseBody.get("bank_account"))

        responseBody.get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        responseBody.get("registered_at").asText() == "2023-12-15T18:13:16+09:00"
        responseBody.get("terminating_at").asText() == "2023-12-18T18:13:16+09:00"
        responseBody.get("terminated_at").asText() == "2023-12-18T18:13:16+09:00"
    }

    def "fetchAccounts_1-11_BizZoneで、filter条件を全て指定した場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/accounts"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter",
                "dc_bank_number eq DC001-1234-1234-1 and "
                        + "bank_account.branch_code  eq  302  and  "
                        + "bank_account.bank_account_type eq saving and "
                        + "bank_account.bank_account_number eq 1234567 and "
                        + "owner_user.sign_in_id eq SID01AA-1001"
        )
        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .addQuery(filter).build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3002",
                                  "zone_name": "モックビズゾーン",
                                  "balance": 210000,
                                  "cache_balance": 200000,
                                  "mint_limit": 9999,
                                  "burn_limit": 9999,
                                  "transfer_limit": 9999,
                                  "charge_limit": 9999,
                                  "cumulative_limit": 500,
                                  "cumulative_amount": 50000,
                                  "cumulative_date": "2023-12-15",
                                  "applied_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        // テスト対象のアカウントに紐づくアカウント管理者が3人いるので、2人削除(アカウント管理者は、アカウントにつき1人のはず)
        // BizZoneのデータにするため、銀行口座の紐付けを消す。
        sql.execute("""
        DELETE FROM dc_user_phone WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        DELETE FROM dc_user WHERE sign_in_id = 'SID01AA-1051' OR sign_in_id = 'SID01AA-1061';
        """)
        // BizZoneのデータにするため、銀行口座の紐付けを消す、メールアドレスを登録する。
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-1234-1234-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES
        ('DC001-1234-1234-1', '0', '<EMAIL>');
        """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'initializing' WHERE sign_in_id = 'SID01AA-1001';
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        sql.execute("""
        INSERT INTO dc_user(
                   sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
        VALUES
        ('SID01AA-1051', 'ｻｲﾝｲﾝﾃｲｼｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'suspended', '0',
        '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
        ('SID01AA-1061', 'ﾑｺｳｱｶｳﾝﾄｶﾝﾘｼｬ', 'company_owner', 'inactive', '0',
       '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900')
        """)
        sql.execute("""
            INSERT INTO dc_user_phone(sign_in_id, phone_number)
            VALUES
            ('SID01AA-1051', '***********'),
            ('SID01AA-1061', '***********')
            """)

        sql.execute("""
           INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_code, bank_name, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
           VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', '0310', 'モック銀行', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1');
           DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-1234-1234-1' AND service_id = '0';
            """)

        sql.execute("""
        UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = 'SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        !Objects.isNull(responseBody.get("accounts"))
        Objects.isNull(responseBody.get("accounts").get(0))

        responseBody.get("paging").get("offset").asLong() == 0
        responseBody.get("paging").get("limit").asLong() == 100
        responseBody.get("paging").get("total").asLong() == 0
    }

    def "findAccount_1-12_[別テナントのイシュア管理者]自身の配下に紐づくアカウントの詳細情報が取得できること - BizZone"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AB-3001", "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def httpGet = adhocHttpHelper.httpGet("/services/accounts/DC001-3333-0103-1")
                .setServiceId("0d367be20aed7707d71dce4dbd8825f1")
                .setToken(accessToken)
                .addQuery("with_bank_account", true)
                .addQuery("with_owner_user", true)
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts/601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3000",
                                  "zone_name": "モックギンコウゾーン",
                                  "balance": 210000,
                                  "cache_balance": 200000,
                                  "mint_limit": 9999,
                                  "burn_limit": 9999,
                                  "transfer_limit": 9999,
                                  "charge_limit": 9999,
                                  "cumulative_limit": 500,
                                  "cumulative_amount": 50000,
                                  "cumulative_date": "2023-12-15",
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        sql.execute("""
        INSERT INTO dc_account_reason (dc_bank_number, service_id, operation_type, reason_code, reason_detail, operated_at)
        VALUES
        ('DC001-3333-0103-1', '0d367be2', 'account_activated', 'UAST0007', '凍結', '2023-12-01T12:00:00+0900');
        INSERT INTO dc_user_reason (sign_in_id, operation_type, reason_code, reason_detail, operated_at) 
        VALUES ('SID01AC-3001', 'user_activated', 'UAST0007', 'アクティブ化',  '2023-12-01T12:00:00+0900');
        """)

        // BizZoneのデータにするため、銀行との紐付けを削除
        sql.execute("""
        DELETE FROM bank_account WHERE dc_bank_number = 'DC001-3333-0103-1';
        INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
        VALUES ('DC001-3333-0103-1', '0d367be2', '<EMAIL>');
        INSERT INTO dc_account_settlement (dc_bank_number,service_id , settlement_type, scheduled_day) 
        VALUES ('DC001-3333-0103-1', '0d367be2', 'monthly_scheduled', '15'); 
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("account_name").asText() == "モックギンコウコウザ３"
        responseBody.get("account_status").asText() == "active"
        Objects.isNull(responseBody.get("balance"))

        responseBody.get("zone").get("zone_id").asInt() == 3000
        responseBody.get("zone").get("zone_name").asText() == "モックギンコウゾーン"

        responseBody.get("email_address").asText() == "<EMAIL>"

        responseBody.get("settlement").get("settlement_type").asText() == "monthly_scheduled"
        responseBody.get("settlement").get("scheduled_day").asText() == "15"

        Objects.isNull(responseBody.get("operation_limit"))
        Objects.isNull(responseBody.get("cumulation"))

        responseBody.get("operated_reason").get("operated_reason_type").asText() == "account_activated"
        responseBody.get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("operated_reason").get("reason_detail").asText() == "凍結"
        responseBody.get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("sign_in_id").asText() == "SID01AC-3001"
        responseBody.get("owner_user").get("user_name").asText() == "テストアカウントカンリシャＣ"
        responseBody.get("owner_user").get("user_status").asText() == "active"

        responseBody.get("owner_user").get("role").get("role_id").asText() == "********-0000-4000-0000-********0110"
        responseBody.get("owner_user").get("role").get("role_name").asText() == "口座管理者"
        responseBody.get("owner_user").get("role").get("role_type").asText() == "account_owner"

        responseBody.get("owner_user").get("user_attribute").get("phone_number").asText() == "***********"
        responseBody.get("owner_user").get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        responseBody.get("owner_user").get("operated_reason").get("operated_reason_type").asText() == "user_activated"
        responseBody.get("owner_user").get("operated_reason").get("reason_code").asText() == "UAST0007"
        responseBody.get("owner_user").get("operated_reason").get("reason_title").asText() == "その他"
        responseBody.get("owner_user").get("operated_reason").get("reason_detail").asText() == "アクティブ化"
        responseBody.get("owner_user").get("operated_reason").get("operated_at").asText() == "2023-12-01T12:00:00+09:00"

        Objects.isNull(responseBody.get("owner_user").get("last_signed_at"))

        Objects.isNull(responseBody.get("bank_account"))

        responseBody.get("applied_at").asText() == "2023-12-15T18:13:16+09:00"
        responseBody.get("registered_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("terminating_at"))
        Objects.isNull(responseBody.get("terminated_at"))

        cleanup:
        sql.execute("""
        DELETE FROM  dc_account_reason WHERE dc_bank_number = 'DC001-3333-0103-1' AND service_id = '0d367be2';
        UPDATE dc_user SET role_id = '********-0000-4000-0000-********0110' WHERE sign_in_id = 'SID01AC-3001';
        DELETE FROM dc_user_reason WHERE sign_in_id = 'SID01AC-3001';
        """)
        sql.execute("""
        INSERT INTO bank_account(dc_bank_number, service_id, bank_account_id, bank_name, bank_code, branch_code, bank_account_type,
                         bank_account_number, bank_account_name)
        VALUES ('DC001-3333-0103-1', '0d367be2', '0310-302-02-6634567', 'モック銀行', '0310', '302', '02', '6634567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3');
        DELETE FROM dc_account_email WHERE dc_bank_number = 'DC001-3333-0103-1' AND service_id = '0d367be2';
        DELETE FROM dc_account_settlement WHERE dc_bank_number = 'DC001-3333-0103-1' AND service_id = '0d367be2';
            """)
    }


    def "changeAccountName_1-1_#testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("$signInId", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "モックビジネス９９９"
                }
                """)
                .build()

        // 口座詳細取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3001",
                                  "zone_name": "モックビジネスゾーン",
                                  "cache_balance": 200000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        // 口座名義変更
        wiremockCore.stubFor(put(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸﾋﾞｼﾞﾈｽ999"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("before").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("after").get("account_name").asText() == "モックビジネス９９９"

        where:
        testCase               | signInId
        "サービス管理者の場合" | "SID11AA-1001"
        "業務担当者の場合"     | "SID11AA-1021"
    }

    def "changeAccountName_1-2_変更前のDC口座表示名と、変更後のDC口座表示名が同じ場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "モックギンコウコウザ１"
                }
                """)
                .build()

        // 口座詳細取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3001",
                                  "zone_name": "モックビジネスゾーン",
                                  "cache_balance": 200000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("before").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("after").get("account_name").asText() == "モックギンコウコウザ１"
    }

    def "changeAccountName_1-3_#testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/{$dcBankNumber}/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "${accountName}"
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                               | dcBankNumber         | accountName
        "DC口座番号が最小桁数より小さい場合"   | "DC001-1234-1234-"   | "モックビジネス９９９"
        "DC口座番号が最大桁数より大きい場合"   | "DC001-1234-1234-11" | "モックビジネス９９９"
        "DC口座表示名が最大桁数より大きい場合" | "DC001-1234-1234-1"  | "ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVW"
    }

    def "changeAccountName_1-4_DC口座表示名が最大桁数の場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "アイウエオカキクケコサシスセソタチツテトナニヌネノワヲンアイウエオカキクケコサシスセソタチツテト"
                }
                """)
                .build()

        // 口座詳細取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3001",
                                  "zone_name": "モックビジネスゾーン",
                                  "cache_balance": 200000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        // 口座名義変更
        wiremockCore.stubFor(put(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ｱｲｳｴｵｶｷｸｹｺｻｼｽｾｿﾀﾁﾂﾃﾄﾅﾆﾇﾈﾉﾜｦﾝｱｲｳｴｵｶｷｸｹｺｻｼｽｾｿﾀﾁﾂﾃﾄ"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("before").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("after").get("account_name").asText() == "アイウエオカキクケコサシスセソタチツテトナニヌネノワヲンアイウエオカキクケコサシスセソタチツテト"
    }

    def "changeAccountName_1-5_DC口座表示名が指定されていない場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"
    }

    def "changeAccountName_1-6_業務エラー_口座詳細取得でE0001"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "モックビジネス９９９"
                }
                """)
                .build()

        // 口座詳細取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0001"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "changeAccountName_1-7_業務エラー_アカウントの状態が凍結の場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "モックビジネス９９９"
                }
                """)
                .build()

        // 口座詳細取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "terminated",
                                  "reason_code": 10000,
                                  "zone_id": "3001",
                                  "zone_name": "モックビジネスゾーン",
                                  "cache_balance": 200000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE2001"
    }

    def "changeAccountName_1-8_業務エラー_#testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "モックビジネス９９９"
                }
                """)
                .build()

        // 口座詳細取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3001",
                                  "zone_name": "モックビジネスゾーン",
                                  "cache_balance": 200000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        // 口座名義変更
        wiremockCore.stubFor(put(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "$coreErrorCode"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == Integer.valueOf("$statusCode")
        responseBody.get("error_code").asText() == "$errorCode"

        where:
        testCase                    | coreErrorCode | statusCode | errorCode
        "口座名義変更でE0001の場合" | "E0001"       | 404        | "EGE0100"
        "口座名義変更でE0012の場合" | "E0012"       | 404        | "EGE0100"
        "口座名義変更でE0032の場合" | "E0032"       | 400        | "EUE2001"

    }

    def "changeAccountName_1-9_#testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("$signInId", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "モックビジネス９９９"
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"

        where:
        testCase             | signInId
        "ユーザ管理者の場合" | "SID11AA-1011"
        "業務承認者の場合"   | "SID11AA-1031"
    }

    def "changeAccountName_1-10_トークンを指定しない場合"() {
        setup:
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-1234-1234-1/name")
                .setBody("""
                {
                    "account_name": "モックビジネス９９９"
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "changeAccountName_1-11_別テナントの #testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(signInId, "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-3333-0103-1/name")
                .setServiceId("0d367be20aed7707d71dce4dbd8825f1")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "ベツテナント３"
                }
                """)
                .build()

        // 口座詳細取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3",
                                  "account_status": "active",
                                  "reason_code": 10000,
                                  "zone_id": "3001",
                                  "zone_name": "モックビジネスゾーン",
                                  "cache_balance": 200000,
                                  "applied_at": "2023-12-15T09:13:16Z",
                                  "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        // 口座名義変更
        wiremockCore.stubFor(put(urlEqualTo("/accounts/601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5",
                                  "account_name": "ベツテナント３"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("before").get("account_name").asText() == "モックギンコウコウザ３"
        responseBody.get("after").get("account_name").asText() == "ベツテナント３"

        where:
        testCase               | signInId
        "サービス管理者の場合" | "SID11AB-3001"
        "業務担当者の場合"     | "SID11AB-3021"
    }

    def "changeAccountName_1-12_別テナントのDC口座番号を指定した場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def httpPut = adhocHttpHelper.httpPut("/services/accounts/DC001-3333-0103-1/name")
                .setToken(accessToken)
                .setBody("""
                {
                    "account_name": "モックギンコウコウザ１"
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }
}
