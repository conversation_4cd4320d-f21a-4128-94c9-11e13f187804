package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services

import com.decurret_dcp.dcjpy.bpm.server.BpmServerMain
import com.decurret_dcp.dcjpy.bpm.server.adaptor.CognitoAdaptor
import com.decurret_dcp.dcjpy.bpm.server.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bpm.server.helper.http.AdhocHttpHelper
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.apache.commons.lang3.RandomStringUtils
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.message.BasicNameValuePair
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest(
        classes = BpmServerMain.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
class ServiceUsersResourceSpec extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    ObjectMapper mapper = new ObjectMapper()

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockServer

    AdhocHttpHelper adhocHttpHelper

    @Autowired
    CognitoAdaptor cognitoAdaptor

    static Sql sql

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        wiremockServer = new WireMockServer(options().port(8081))
        wiremockServer.start()
    }

    def cleanupSpec() {
        AdhocHelper.cleanupSpec()
        wiremockServer.stop()
    }

    def setup() {
        adhocHttpHelper = new AdhocHttpHelper(applicationPort, cognitoAdaptor)
    }

    def cleanup() {
        // Do nothing.
    }

    def "fetchUsers_1-1_ユーザ管理者配下のユーザ一覧が取得できること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AA-1021"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000022"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        // キー情報のみ確認し、他の情報は省略
        responseBody.get("users")[1].get("sign_in_id").asText() == "SID11AA-1031"
        responseBody.get("users")[2].get("sign_in_id").asText() == "SID11AA-1022"
        responseBody.get("users")[3].get("sign_in_id").asText() == "SID11AA-1111"
        responseBody.get("users")[4].get("sign_in_id").asText() == "SID11AA-1023"
        responseBody.get("users")[5].get("sign_in_id").asText() == "SID11AA-1081"
        responseBody.get("users")[6].get("sign_in_id").asText() == "SID11AA-1091"
        responseBody.get("users")[7].get("sign_in_id").asText() == "SID11AA-1131"
        responseBody.get("users")[8].get("sign_in_id").asText() == "SID11AA-1141"
        responseBody.get("users")[9].get("sign_in_id").asText() == "SID11AA-1221"
        responseBody.get("users")[10].get("sign_in_id").asText() == "SID11AA-1222"
        responseBody.get("users")[11].get("sign_in_id").asText() == "SID11AA-1223"
        responseBody.get("users")[12].get("sign_in_id").asText() == "SID11AA-1224"
        responseBody.get("users")[13].get("sign_in_id").asText() == "SID11AA-1225"
        responseBody.get("users")[14].get("sign_in_id").asText() == "SID11AA-1226"
        responseBody.get("users")[15].get("sign_in_id").asText() == "SID11AA-1227"
        responseBody.get("users")[16].get("sign_in_id").asText() == "SID11AA-1228"
        responseBody.get("users")[17].get("sign_in_id").asText() == "SID11AA-1229"
        responseBody.get("users")[18].get("sign_in_id").asText() == "SID11AA-1231"
        responseBody.get("users")[19].get("sign_in_id").asText() == "SID11AA-1232"
        responseBody.get("users")[20].get("sign_in_id").asText() == "SID11AA-1233"
        responseBody.get("users")[21].get("sign_in_id").asText() == "SID11AA-1234"
        responseBody.get("users")[22].get("sign_in_id").asText() == "SID11AA-1235"
        responseBody.get("users")[23].get("sign_in_id").asText() == "SID11AA-1236"
        responseBody.get("users")[24].get("sign_in_id").asText() == "SID11AB-2021"
        responseBody.get("users")[25].get("sign_in_id").asText() == "SID11AB-2031"

        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "26"
    }

    def "fetchUsers_1-1_ユーザ管理者配下のユーザ一覧が取得できること(サービスIDがデフォルト以外)"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AB-3011", "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def path = "/services/users"

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AB-3021"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＣ"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000222"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        // キー情報のみ確認し、他の情報は省略
        responseBody.get("users")[1].get("sign_in_id").asText() == "SID11AB-3031"

        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "2"
    }

    def "fetchUsers_1-2_アカウント管理者配下のユーザ一覧が取得できること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users"

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AA-1011"
        responseBody.get("users")[0].get("user_name").asText() == "テストユ－ザカンリシャＡ"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000021"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "ユーザ管理者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "user_owner"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        // キー情報のみ確認し、他の情報は省略
        responseBody.get("users")[1].get("sign_in_id").asText() == "SID11AA-1021"
        responseBody.get("users")[2].get("sign_in_id").asText() == "SID11AA-1031"
        responseBody.get("users")[3].get("sign_in_id").asText() == "SID11AA-1022"
        responseBody.get("users")[4].get("sign_in_id").asText() == "SID11AA-1101"
        responseBody.get("users")[5].get("sign_in_id").asText() == "SID11AA-1111"
        responseBody.get("users")[6].get("sign_in_id").asText() == "SID11AA-1023"
        responseBody.get("users")[7].get("sign_in_id").asText() == "SID11AA-1071"
        responseBody.get("users")[8].get("sign_in_id").asText() == "SID11AA-1081"
        responseBody.get("users")[9].get("sign_in_id").asText() == "SID11AA-1091"
        responseBody.get("users")[10].get("sign_in_id").asText() == "SID11AA-1121"
        responseBody.get("users")[11].get("sign_in_id").asText() == "SID11AA-1131"
        responseBody.get("users")[12].get("sign_in_id").asText() == "SID11AA-1141"
        responseBody.get("users")[13].get("sign_in_id").asText() == "SID11AA-1211"
        responseBody.get("users")[14].get("sign_in_id").asText() == "SID11AA-1212"
        responseBody.get("users")[15].get("sign_in_id").asText() == "SID11AA-1213"
        responseBody.get("users")[16].get("sign_in_id").asText() == "SID11AA-1221"
        responseBody.get("users")[17].get("sign_in_id").asText() == "SID11AA-1222"
        responseBody.get("users")[18].get("sign_in_id").asText() == "SID11AA-1223"
        responseBody.get("users")[19].get("sign_in_id").asText() == "SID11AA-1224"
        responseBody.get("users")[20].get("sign_in_id").asText() == "SID11AA-1225"
        responseBody.get("users")[21].get("sign_in_id").asText() == "SID11AA-1226"
        responseBody.get("users")[22].get("sign_in_id").asText() == "SID11AA-1227"
        responseBody.get("users")[23].get("sign_in_id").asText() == "SID11AA-1228"
        responseBody.get("users")[24].get("sign_in_id").asText() == "SID11AA-1229"
        responseBody.get("users")[25].get("sign_in_id").asText() == "SID11AA-1231"
        responseBody.get("users")[26].get("sign_in_id").asText() == "SID11AA-1232"
        responseBody.get("users")[27].get("sign_in_id").asText() == "SID11AA-1233"
        responseBody.get("users")[28].get("sign_in_id").asText() == "SID11AA-1234"
        responseBody.get("users")[29].get("sign_in_id").asText() == "SID11AA-1235"
        responseBody.get("users")[30].get("sign_in_id").asText() == "SID11AA-1236"
        responseBody.get("users")[31].get("sign_in_id").asText() == "SID11AB-2011"
        responseBody.get("users")[32].get("sign_in_id").asText() == "SID11AB-2021"
        responseBody.get("users")[33].get("sign_in_id").asText() == "SID11AB-2031"

        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "34"
    }

    def "fetchUsers_1-2_アカウント管理者配下のユーザ一覧が取得できること(サービスIDがデフォルト以外)"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AB-3001", "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def path = "/services/users"

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AB-3011"
        responseBody.get("users")[0].get("user_name").asText() == "テストユ－ザカンリシャＣ"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000221"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "ユーザ管理者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "user_owner"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        // キー情報のみ確認し、他の情報は省略
        responseBody.get("users")[1].get("sign_in_id").asText() == "SID11AB-3012"
        responseBody.get("users")[2].get("sign_in_id").asText() == "SID11AB-3021"
        responseBody.get("users")[3].get("sign_in_id").asText() == "SID11AB-3031"

        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "4"
    }

    def "fetchUsers_1-3_offset limitが が設定されている場合、指定された数の一覧が取得できること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def value1 = new BasicNameValuePair("offset", "1")
        def value2 = new BasicNameValuePair("limit", "2")

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken).addQuery(value1, value2).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AA-1022"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＡ２"
        responseBody.get("users")[0].get("user_status").asText() == "suspended"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000022"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"
        responseBody.get("paging").get("offset").asText() == "1"
        responseBody.get("paging").get("limit").asText() == "2"
        responseBody.get("paging").get("total").asText() == "26"
    }

    def "fetchUsers_1-4_sign_in_id を指定した場合、前方一致条件で一致した一覧が取得されること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter",
                "sign_in_id eq SID11AA-102*"
        )

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AA-1021"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000022"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"
        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "3"
    }

    def "fetchUsers_1-5_sign_in_id を指定した場合、前方一致条件で一致しなかった場合、一覧が取得されないこと[0件取得]"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter",
                "sign_in_id eq test*"
        )

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users").asText() == ""
        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "0"
    }

    def "fetchUsers_1-6_user_name を指定した場合、前方一致条件で一致した一覧が取得されること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter",
                "user_name  eq  テストギョウムタントウシャ*"
        )

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AA-1021"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000022"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"
        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "4"
    }

    def "fetchUsers_1-7_user_name を指定した場合、前方一致条件で一致しなかった場合、一覧が取得されないこと[0件取得]"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter",
                "user_name eq test*"
        )

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        responseBody.get("users").asText() == ""
        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "0"
    }

    def "fetchUsers_1-8_user_name と sign_in_id を指定した場合、前方一致条件で一致した一覧が取得されること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter",
                "sign_in_id eq SID11AA-102* and user_name eq テストギョウムタントウシャ*"
        )

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID11AA-1021"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000022"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"
        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "3"
    }

    def "fetchUsers_1-9_user_name と sign_in_id を指定した場合、#testCase が前方一致条件で一致しなかった場合、一覧が取得されないこと[0件取得]"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter", vFilter)

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users").asText() == ""
        responseBody.get("paging").get("offset").asText() == "0"
        responseBody.get("paging").get("limit").asText() == "100"
        responseBody.get("paging").get("total").asText() == "0"

        where:
        testCase     | vFilter
        "sign_in_id" | "sign_in_id eq test* and user_name eq テストギョウムタントウシャ*"
        "user_name"  | "sign_in_id eq SID11AA-102* and user_name eq test*"
    }

    def "fetchUsers_2-1_#item が #testCase の時、バリデーションエラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def value1 = new BasicNameValuePair("offset", vOffset)
        def value2 = new BasicNameValuePair("limit", vLimit)
        def filter = new BasicNameValuePair("filter", vFilter)

        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).addQuery(value1, value2)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"


        where:
        item     | testCase     | vOffset         | vLimit | vFilter
        "limit"  | "最小値未満" | "0"             | "0"    | "sign_in_id eq foo*"
        "limit"  | "最大値超過" | "0"             | "101"  | "sign_in_id eq foo*"
        "offset" | "最小値未満" | "-1"            | "100"  | "sign_in_id eq foo*"
        "offset" | "最大値超過" | "9999999999991" | "100"  | "sign_in_id eq foo*"
        "filter" | "最大値超過" | "0"             | "100"  | RandomStringUtils.randomAlphanumeric(4097)
    }

    def "fetchUsers_2-2_#item が #testCase の時、バリデーションエラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def filter = new BasicNameValuePair("filter", vFilter)

        def httpGet = adhocHttpHelper.httpGet(path).setToken(accessToken)
                .addQuery(filter)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"


        where:
        item     | testCase                   | vFilter
        "filter" | "filter 不正"              | "abc"
        "filter" | "sign_in_id 不正"          | "sign_in_id eq foo"
        "filter" | "user_name 不正"           | "user_name eq foo"
        "filter" | "* のみ(前方一致条件なし)" | "sign_in_id eq *"
        "filter" | "指定できない項目"         | "dummy eq foo*"
        "filter" | "sign_in_id 複数指定"      | "sign_in_id eq foo* and user_name eq foo* and sign_in_id eq bar*"
        "filter" | "user_name 複数指定"       | "user_name eq foo* and sign_in_id eq foo* and user_name eq bar*"
    }

    def "fetchUsers_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def path = "/services/users"
        // リクエストパラメータを定義
        def value1 = new BasicNameValuePair("offset", "0")
        def value2 = new BasicNameValuePair("limit", "100")

        def httpGet = adhocHttpHelper.httpGet(path).addQuery(value1, value2).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "fetchUsers_9-2_業務担当者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def value1 = new BasicNameValuePair("offset", "0")
        def value2 = new BasicNameValuePair("limit", "100")

        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).addQuery(value1, value2).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "fetchUsers_9-3_業務承認者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def path = "/services/users"
        // リクエストパラメータを定義
        def value1 = new BasicNameValuePair("offset", "0")
        def value2 = new BasicNameValuePair("limit", "100")

        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).addQuery(value1, value2).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "findUser_1-1_ユーザ管理者配下のユーザーが見つかること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID11AA-1021"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == targetSignInId
        responseBody.get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("user_status").asText() == "active"
        responseBody.get("role").get("role_type").asText() == "operator"
        responseBody.get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("role").has("authorities") == false
        responseBody.get("user_attribute").get("phone_number").asText() == "07012341021"
        responseBody.has("operated_reason") == false
    }

    def "findUser_1-1_ユーザ管理者配下のユーザーが見つかること(サービスIDがデフォルト以外)"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AB-3011", "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def targetSignInId = "SID11AB-3021"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == targetSignInId
        responseBody.get("user_name").asText() == "テストギョウムタントウシャＣ"
        responseBody.get("user_status").asText() == "active"
        responseBody.get("role").get("role_type").asText() == "operator"
        responseBody.get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("role").has("authorities") == false
        responseBody.get("user_attribute").get("phone_number").asText() == "07012343021"
        responseBody.has("operated_reason") == false
    }

    def "findUser_1-2_指定したユーザがサインイン停止"() {
        // 指定したユーザがサインイン停止状態のテストコード
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID11AA-1022"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == targetSignInId
        responseBody.get("user_name").asText() == "テストギョウムタントウシャＡ２"
        responseBody.get("user_status").asText() == "suspended"
        responseBody.get("role").get("role_type").asText() == "operator"
        responseBody.get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("role").has("authorities") == false
        responseBody.get("user_attribute").get("phone_number").asText() == "07012341022"

        responseBody.has("operated_reason") == true
        responseBody.get("operated_reason").get("operated_reason_type").asText() == "user_suspended"
        responseBody.get("operated_reason").get("reason_code").asText() == "SUSS0001"
        responseBody.get("operated_reason").get("reason_title").asText() == "端末紛失"
        responseBody.get("operated_reason").get("reason_detail").asText() == "2023/10/15 本人より連絡あり"
    }

    def "findUser_1-3_指定したユーザがサインイン一時停止"() {
        // 指定したユーザがサインイン一時停止状態のテストコード
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID11AA-1023"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == targetSignInId
        responseBody.get("user_name").asText() == "テストギョウムタントウシャＡ３"
        responseBody.get("user_status").asText() == "temporary_suspended"
        responseBody.get("role").get("role_type").asText() == "operator"
        responseBody.get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("role").has("authorities") == false
        responseBody.get("user_attribute").get("phone_number").asText() == "07012341023"

        responseBody.has("operated_reason") == false
    }

    def "findUser_1-4_ユーザステータスが suspended_in_init の場合"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID11AA-1021"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()
        sql.execute("""
            UPDATE service_user
            SET user_status = 'suspended_in_init'
            WHERE sign_in_id = 'SID11AA-1021'
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == targetSignInId
        responseBody.get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("user_status").asText() == "suspended"
        responseBody.get("role").get("role_type").asText() == "operator"
        responseBody.get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("role").has("authorities") == false
        responseBody.get("user_attribute").get("phone_number").asText() == "07012341021"
        responseBody.has("operated_reason") == false

        cleanup:
        sql.execute("""
            UPDATE service_user
            SET user_status = 'active'
            WHERE sign_in_id = 'SID11AA-1021'
        """)
    }

    def "findUser_2-1_指定したユーザーが存在しない"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID99FF-9999"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "findUser_2-2_指定したユーザーは別テナント"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID11AB-3021"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "findUser_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def targetSignInId = "SID11AA-1022"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "findUser_9-2_アカウント管理者は利用可能"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def targetSignInId = "SID11AA-1021"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == targetSignInId
        responseBody.get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("user_status").asText() == "active"
        responseBody.get("role").get("role_type").asText() == "operator"
        responseBody.get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("role").has("authorities") == false
        responseBody.get("user_attribute").get("phone_number").asText() == "07012341021"
    }

    def "findUser_9-3_業務担当者はアクセスできない"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def targetSignInId = "SID11AA-1021"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "findUser_9-4_業務承認者はアクセスできない"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def targetSignInId = "SID11AA-1021"
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "findUser_10-1_SignInIdが無効です - #testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users/${targetSignInId}"
        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                         | targetSignInId
        "SignInId - Don't contain 'SID'" | "00001AA-1011"
        "SignInId - Invalid format 1"    | "SID0100"
        "SignInId - Invalid format 2"    | "SIDAA10-1011"
        "SignInId - Invalid format 3"    | "SID00GG-1000"
        "SignInId - Invalid format 4"    | "SID00AA-100"
        "SignInId - Invalid format 5"    | "SID00AA-10000"
        "SignInId - Invalid format 6"    | "SID0A-10000"
        "SignInId - Invalid format 7"    | "SIDAAAA-BBBB"
        "SignInId - Invalid format 8"    | "SID01"
        "SignInId - Invalid format 9"    | "SID01-1001-1001"
        "SignInId - Invalid format 10"   | "SID01AA-1001-1001"
    }

    def "addUser_1-1_#operater が実行者の場合、#vRoleType のユーザが作成されること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
                Map.entry("role_id", vRoleId)
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.has("sign_in_id") == true
        responseBody.has("temporary_password") == true
        responseBody.get("user_name").asText() == vUserName
        responseBody.get("role").get("role_id").asText() == vRoleId
        responseBody.get("role").get("role_name").asText() == vRoleName
        responseBody.get("role").get("role_type").asText() == vRoleType
        Objects.isNull(responseBody.get("role").get("created_at"))

        where:
        operater         | vSignInId      | vPassword   | vUserName                      | vRoleId                                | vRoleName      | vRoleType
        "サービス管理者" | "SID11AA-1001" | "Password1" | "テストユ－ザカンリシャ"        | "00000000-0000-4000-0000-000000000021" | "ユーザ管理者" | "user_owner"
        "サービス管理者" | "SID11AA-1001" | "Password1" | "テストギョウムタントウシャ"   | "00000000-0000-4000-0000-000000000022" | "業務担当者"   | "operator"
        "サービス管理者" | "SID11AA-1001" | "Password1" | "テストギョウムショウニンシャ" | "00000000-0000-4000-0000-000000000023" | "業務承認者"   | "reviewer"
        "ユーザ管理者"   | "SID11AA-1011" | "Password1" | "テストギョウムタントウシャ"   | "00000000-0000-4000-0000-000000000022" | "業務担当者"   | "operator"
        "ユーザ管理者"   | "SID11AA-1011" | "Password1" | "テストギョウムショウニンシャ" | "00000000-0000-4000-0000-000000000023" | "業務承認者"   | "reviewer"
    }

    def "addUser_1-1_#operater が実行者の場合、#vRoleType のユーザが作成されること（サービスIDがデフォルト以外）"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
                Map.entry("role_id", vRoleId)
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.has("sign_in_id") == true
        responseBody.has("temporary_password") == true
        responseBody.get("user_name").asText() == vUserName
        responseBody.get("role").get("role_id").asText() == vRoleId
        responseBody.get("role").get("role_name").asText() == vRoleName
        responseBody.get("role").get("role_type").asText() == vRoleType
        Objects.isNull(responseBody.get("role").get("created_at"))

        def dcUser = sql.firstRow("""
            SELECT * FROM service_user WHERE sign_in_id = '${responseBody.get("sign_in_id").asText()}'
        """)
        dcUser.get("user_name") == vDbUserName
        dcUser.get("service_user_type") == "normal"
        dcUser.get("user_status") == "initializing"
        dcUser.get("service_id") == "0d367be2"
        dcUser.get("role_id") == vRoleId
        dcUser.get("registered_at") != null
        dcUser.get("terminated_at") == null

        def dcUserHistory = sql.firstRow("""
            SELECT * FROM service_user_sign_in_history WHERE sign_in_id = '${responseBody.get("sign_in_id").asText()}'
        """)
        dcUserHistory.get("current_signed_in_at") != null
        dcUserHistory.get("last_signed_in_at") == null

        where:
        operater         | vSignInId      | vUserName                                | vRoleId                                | vRoleName      | vRoleType    | vDbUserName
        "サービス管理者" | "SID11AB-3001" | "テストユ－ザカンリシャ　サクセイ１"        | "00000000-0000-4000-0000-000000000221" | "ユーザ管理者" | "user_owner" | "ﾃｽﾄﾕ-ｻﾞｶﾝﾘｼｬ ｻｸｾｲ1"
        "サービス管理者" | "SID11AB-3001" | "テストギョウムタントウシャ　サクセイ１"   | "00000000-0000-4000-0000-000000000222" | "業務担当者"   | "operator"   | "ﾃｽﾄｷﾞｮｳﾑﾀﾝﾄｳｼｬ ｻｸｾｲ1"
        "サービス管理者" | "SID11AB-3001" | "テストギョウムショウニンシャ　サクセイ１" | "00000000-0000-4000-0000-000000000223" | "業務承認者"   | "reviewer"   | "ﾃｽﾄｷﾞｮｳﾑｼｮｳﾆﾝｼｬ ｻｸｾｲ1"
        "ユーザ管理者"   | "SID11AB-3011" | "テストギョウムタントウシャ　サクセイ２"   | "00000000-0000-4000-0000-000000000222" | "業務担当者"   | "operator"   | "ﾃｽﾄｷﾞｮｳﾑﾀﾝﾄｳｼｬ ｻｸｾｲ2"
        "ユーザ管理者"   | "SID11AB-3011" | "テストギョウムショウニンシャ　サクセイ２" | "00000000-0000-4000-0000-000000000223" | "業務承認者"   | "reviewer"   | "ﾃｽﾄｷﾞｮｳﾑｼｮｳﾆﾝｼｬ ｻｸｾｲ2"
    }

    def "addUser_2-1_#item が #testCase の時、バリデーションエラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
                Map.entry("role_id", vRoleId)
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        item        | testCase                   | vUserName                                           | vRoleId
        "user_name" | "ブランク"                 | " "                                                 | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最低文字数未満"           | ""                                                  | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最大文字数超過"           | "１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９" | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角数字"           | "ユ－ザ1"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾がひらがな"           | "ユ－ザあ"                                           | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角カタカナ"       | "ユ－ザｱ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角英字"           | "ユ－ザa"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角スペース"       | "ユ－ザ "                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角カッコ"         | "ユ－ザ("                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角カッコ"         | "ユ－ザ)"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角ハイフン"       | "ユ－ザ-"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角ピリオド"       | "ユ－ザ."                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角スラッシュ"     | "ユ－ザ/"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "末尾が半角カンマ"         | "ユ－ザ,"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角数字"           | "1ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初がひらがな"           | "あユ－ザ"                                           | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角カタカナ"       | "ｱユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角英字"           | "aユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角スペース"       | " ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角カッコ"         | "(ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角カッコ"         | ")ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角ハイフン"       | "-ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角ピリオド"       | ".ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角スラッシュ"     | "/ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "最初が半角カンマ"         | ",ユ－ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角数字が含まれる"       | "ユ－1ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "ひらがなが含まれる"       | "ユ－あザ"                                           | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角カタカナが含まれる"   | "ユ－ｱザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角英字が含まれる"       | "ユ－aザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角スペースが含まれる"   | "ユ－ ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角カッコが含まれる"     | "ユ－(ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角カッコが含まれる"     | "ユ－)ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角ハイフンが含まれる"   | "ユ－-ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角ピリオドが含まれる"   | "ユ－.ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角スラッシュが含まれる" | "ユ－/ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "user_name" | "半角カンマが含まれる"     | "ユ－,ザ"                                            | "00000000-0000-4000-0000-000000000022"
        "role_id"   | "最低文字数未満"           | "ギョウムタントウシャ"                              | ""
        "role_id"   | "最大文字数超過"           | "ギョウムタントウシャ"                              | "00000000-0000-4000-0000-0000000000221"
    }

    def "addUser_2-2_サービス管理者のユーザが作成できないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", "サ－ビスカンリシャ"),
                Map.entry("role_id", "00000000-0000-4000-0000-000000000020")
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE3100"
    }

    def "addUser_2-3_ユーザ管理者権限のユーザが自分以上の権限のもつユーザを作成できないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
                Map.entry("role_id", vRoleId)
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE3100"

        where:
        vUserName           | vRoleId
        "サ－ビスカンリシャ" | "00000000-0000-4000-0000-000000000020"
        "サ－ビスカンリシャ" | "00000000-0000-4000-0000-000000000021"
    }

    def "addUser_2-4_存在しないrole_id を指定した場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", "サ－ビスカンリシャ"),
                Map.entry("role_id", "00000000-0000-4000-0000-999999999999")
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "addUser_2-5_違うテナントのrole_id を指定した場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", "サ－ビスカンリシャ"),
                Map.entry("role_id", "00000000-0000-4000-0000-000000000222")
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "addUser_2-6_バリデーション確認_UserName_許容文字で登録できること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", "アン０９ＡＺａｚ（）－，／．ー　"),
                Map.entry("role_id", "00000000-0000-4000-0000-000000000021")
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.has("sign_in_id") == true
        responseBody.has("temporary_password") == true
        responseBody.get("user_name").asText() == "アン０９ＡＺａｚ（）－，／．ー　"
        responseBody.get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000021"
        responseBody.get("role").get("role_name").asText() == "ユーザ管理者"
        responseBody.get("role").get("role_type").asText() == "user_owner"
    }

    def "addUser_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def path = "/services/users"
        def userName = "テストギョウムタントウシャ"
        def roleId = "00000000-0000-4000-0000-000000000022"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
                Map.entry("role_id", roleId)
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "addUser_9-2_業務担当者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def path = "/services/users"
        def userName = "テストギョウムタントウシャ"
        def roleId = "00000000-0000-4000-0000-000000000022"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
                Map.entry("role_id", roleId)
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "addUser_9-3_業務承認者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def path = "/services/users"
        def userName = "テストギョウムショウニンシャ"
        def roleId = "00000000-0000-4000-0000-000000000022"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
                Map.entry("role_id", roleId)
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUser_1-1_#operater が実行者の場合、#vTargetUser のユーザ情報が変更されること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users/${vTargetSignInId}"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)

        then:
        response.getStatusLine().getStatusCode() == 200

        where:
        operater         | vTargetUser    | vSignInId      | vPassword   | vTargetSignInId | vUserName
        "サービス管理者" | "ユーザ管理者" | "SID11AA-1001" | "Password1" | "SID11AA-1011"  | "ユ－ザカンリシャＡ"
        "サービス管理者" | "業務担当者"   | "SID11AA-1001" | "Password1" | "SID11AA-1021"  | "ギョウムタントウシャＡ"
        "サービス管理者" | "業務承認者"   | "SID11AA-1001" | "Password1" | "SID11AA-1031"  | "ギョウムショウニンシャＡ"
        "ユーザ管理者"   | "業務担当者"   | "SID11AA-1011" | "Password1" | "SID11AA-1021"  | "ギョウムタントウシャＡ１"
        "ユーザ管理者"   | "業務承認者"   | "SID11AA-1011" | "Password1" | "SID11AA-1031"  | "ギョウムショウニンシャＡ１"
    }

    def "updateUser_1-1_#operater が実行者の場合、#vTargetUser のユーザ情報が変更されること(サービスIDがデフォルト以外)"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def path = "/services/users/${vTargetSignInId}"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)

        then:
        response.getStatusLine().getStatusCode() == 200

        def dcUser = sql.firstRow("""
            SELECT * FROM service_user WHERE sign_in_id = '${vTargetSignInId}'
        """)
        dcUser.get("user_name") == vDbUserName

        where:
        operater         | vTargetUser    | vSignInId      | vTargetSignInId | vUserName                          | vDbUserName
        "サービス管理者" | "ユーザ管理者" | "SID11AB-3001" | "SID11AB-3011"  | "ユ－ザカンリシャ　ヘンコウ"         | "ﾕ-ｻﾞｶﾝﾘｼｬ ﾍﾝｺｳ"
        "サービス管理者" | "業務担当者"   | "SID11AB-3001" | "SID11AB-3021"  | "ギョウムタントウシャ　ヘンコウ"    | "ｷﾞｮｳﾑﾀﾝﾄｳｼｬ ﾍﾝｺｳ"
        "サービス管理者" | "業務承認者"   | "SID11AB-3001" | "SID11AB-3031"  | "ギョウムショウニンシャ　ヘンコウ"  | "ｷﾞｮｳﾑｼｮｳﾆﾝｼｬ ﾍﾝｺｳ"
        "ユーザ管理者"   | "業務担当者"   | "SID11AB-3011" | "SID11AB-3021"  | "ギョウムタントウシャ　ヘンコウ２"   | "ｷﾞｮｳﾑﾀﾝﾄｳｼｬ ﾍﾝｺｳ2"
        "ユーザ管理者"   | "業務承認者"   | "SID11AB-3011" | "SID11AB-3031"  | "ギョウムショウニンシャ　ヘンコウ２" | "ｷﾞｮｳﾑｼｮｳﾆﾝｼｬ ﾍﾝｺｳ2"
    }

    def "updateUser_2-1_存在しないサインインIDを指定した場合、業務エラーになること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AB-9999"
        def userName = "ソンザイシナイユ－ザ"
        def path = "/services/users/${signInId}"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "updateUser_2-2_別テナントのユーザ情報が変更されないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${vTargetSignInId}"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        where:
        vTargetSignInId | vUserName
        "SID11AB-3001"  | "ベツテナントＣ０"
        "SID11AB-3011"  | "ベツテナントＣ１"
        "SID11AB-3021"  | "ベツテナントＣ２"
        "SID11AB-3031"  | "ベツテナントＣ３"
    }

    def "updateUser_2-3_user_name が #testCase の時、バリデーションエラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/SID11AA-1011"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                   | vUserName
        "ブランク"                 | " "
        "最低文字数未満"           | ""
        "最大文字数超過"           | "１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９"
        "末尾が半角数字"           | "ユ－ザ1"
        "末尾がひらがな"           | "ユ－ザあ"
        "末尾が半角カタカナ"       | "ユ－ザｱ"
        "末尾が半角英字"           | "ユ－ザa"
        "末尾が半角スペース"       | "ユ－ザ "
        "末尾が半角カッコ"         | "ユ－ザ("
        "末尾が半角カッコ"         | "ユ－ザ)"
        "末尾が半角ハイフン"       | "ユ－ザ-"
        "末尾が半角ピリオド"       | "ユ－ザ."
        "末尾が半角スラッシュ"     | "ユ－ザ/"
        "末尾が半角カンマ"         | "ユ－ザ,"
        "最初が半角数字"           | "1ユ－ザ"
        "最初がひらがな"           | "あユ－ザ"
        "最初が半角カタカナ"       | "ｱユ－ザ"
        "最初が半角英字"           | "aユ－ザ"
        "最初が半角スペース"       | " ユ－ザ"
        "最初が半角カッコ"         | "(ユ－ザ"
        "最初が半角カッコ"         | ")ユ－ザ"
        "最初が半角ハイフン"       | "-ユ－ザ"
        "最初が半角スラッシュ"     | "/ユ－ザ"
        "半角数字が含まれる"       | "ユ－1ザ"
        "ひらがなが含まれる"       | "ユ－あザ"
        "半角カタカナが含まれる"   | "ユ－ｱザ"
        "半角英字が含まれる"       | "ユ－aザ"
        "半角スペースが含まれる"   | "ユ－ ザ"
        "半角カッコが含まれる"     | "ユ－(ザ"
        "半角カッコが含まれる"     | "ユ－)ザ"
        "半角ハイフンが含まれる"   | "ユ－-ザ"
        "半角ピリオドが含まれる"   | "ユ－.ザ"
        "半角スラッシュが含まれる" | "ユ－/ザ"
        "半角カンマが含まれる"     | "ユ－,ザ"
    }

    def "updateUser_2-4_指定したサインインIDのユーザロールが操作者のロール権限より上位の場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users/${vTargetSignInId}"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", vUserName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE3101"

        where:
        vSignInId      | vPassword   | vTargetSignInId | vUserName
        "SID11AA-1011" | "Password1" | "SID11AA-1001"  | "コウザカンリシャ（ヘンコウ）"
        "SID11AA-1011" | "Password1" | "SID11AA-1071"  | "ユ－ザカンリシャ（ヘンコウ）"
    }

    def "updateUser_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def path = "/services/users/SID11AA-1011"
        def userName = "ト－クンナシ"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUser_9-2_業務担当者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def path = "/services/users/SID11AA-1031"
        def userName = "ギョウムタントウシャ"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUser_9-3_業務承認者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def path = "/services/users/SID11AA-1021"
        def userName = "ギョウムショウニンシャ"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUser_10-1_SignInIdが無効です - #testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def userName = "ソンザイシナイユ－ザ"
        def path = "/services/users/${targetSignInId}"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_name", userName),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                         | targetSignInId
        "SignInId - Don't contain 'SID'" | "00001AA-1011"
        "SignInId - Invalid format 1"    | "SID0100"
        "SignInId - Invalid format 2"    | "SIDAA10-1011"
        "SignInId - Invalid format 3"    | "SID00GG-1000"
        "SignInId - Invalid format 4"    | "SID00AA-100"
        "SignInId - Invalid format 5"    | "SID00AA-10000"
        "SignInId - Invalid format 6"    | "SID0A-10000"
        "SignInId - Invalid format 7"    | "SIDAAAA-BBBB"
        "SignInId - Invalid format 8"    | "SID01"
        "SignInId - Invalid format 9"    | "SID01-1001-1001"
        "SignInId - Invalid format 10"   | "SID01AA-1001-1001"
    }

    def "resetUserAuthentication_1-1_口座管理者が実行者の場合、ユーザ管理者の認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def targetSignInId = "SID11AA-1211"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-1_口座管理者が実行者の場合、ユーザ管理者の認証リセットができること(サービスIDがデフォルト以外)"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AB-3001", "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def targetSignInId = "SID11AB-3012"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in")
                .setBody(body)
                .setServiceId("0d367be2")
                .build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-2_口座管理者が実行者の場合、業務担当者の認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def targetSignInId = "SID11AA-1221"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-3_口座管理者が実行者の場合、業務承認者の認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def targetSignInId = "SID11AA-1231"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-4_ユーザ管理者が実行者の場合、業務担当者の認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID11AA-1222"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-5_ユーザ管理者が実行者の場合、業務承認者の認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1011", "Password1")
        def targetSignInId = "SID11AA-1232"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-6_口座管理者が初期設定待ちユーザの認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def targetSignInId = "SID11AA-1223"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-7_口座管理者がサインイン停止ユーザの認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def targetSignInId = "SID11AA-1224"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_1-8_口座管理者がサインイン一時停止ユーザの認証リセットができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def targetSignInId = "SID11AA-1225"
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response1 = httpClient.execute(httpPut)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def body = """
            {
                "sign_in_id": "${targetSignInId}",
                "password": "${responseBody1.get("temporary_password").asText()}"
            }
            """
        def httpPost = adhocHttpHelper.httpPost("/services/sign_in").setBody(body).build()

        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        then:
        response1.getStatusLine().getStatusCode() == 200
        responseBody1.get("sign_in_id").asText() == targetSignInId
        responseBody1.has("temporary_password") == true

        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("id_token").asText() != null
        responseBody2.get("access_token").asText() != null
        responseBody2.get("refresh_token").asText() != null
        responseBody2.get("sign_in_status").asText() == "need_setup_password"
    }

    def "resetUserAuthentication_2-1_存在しないサインインIDを指定した場合、業務エラーになること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AA-9999"
        def path = "/services/users/${signInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "resetUserAuthentication_2-2_別テナントの #target のユーザ情報が変更されないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${vTargetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        where:
        target           | vTargetSignInId
        "サービス管理者" | "SID11AB-3001"
        "ユーザ管理者"   | "SID11AB-3011"
        "業務担当者"     | "SID11AB-3021"
        "業務承認者"     | "SID11AB-3031"
    }

    def "resetUserAuthentication_2-3_指定したサインインIDがユーザ無効の場合、業務エラーになりこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AA-1121"
        def path = "/services/users/${signInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE2503"
    }

    def "resetUserAuthentication_2-4_指定したサインインIDのユーザロールが操作者のロール権限より上位の場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users/${vTargetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE3101"

        where:
        vSignInId      | vPassword   | vTargetSignInId
        "SID11AA-1011" | "Password1" | "SID11AA-1001"
        "SID11AA-1011" | "Password1" | "SID11AA-1071"
    }

    def "resetUserAuthentication_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def path = "/services/users/SID11AA-1011/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "resetUserAuthentication_9-2_業務担当者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def path = "/services/users/SID11AA-1031/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "resetUserAuthentication_9-3_業務承認者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def path = "/services/users/SID11AA-1021/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "resetUserAuthentication_10-1_SignInIdが無効です - #testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${targetSignInId}/authentication/reset"
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(null).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                         | targetSignInId
        "SignInId - Don't contain 'SID'" | "00001AA-1011"
        "SignInId - Invalid format 1"    | "SID0100"
        "SignInId - Invalid format 2"    | "SIDAA10-1011"
        "SignInId - Invalid format 3"    | "SID00GG-1000"
        "SignInId - Invalid format 4"    | "SID00AA-100"
        "SignInId - Invalid format 5"    | "SID00AA-10000"
        "SignInId - Invalid format 6"    | "SID0A-10000"
        "SignInId - Invalid format 7"    | "SIDAAAA-BBBB"
        "SignInId - Invalid format 8"    | "SID01"
        "SignInId - Invalid format 9"    | "SID01-1001-1001"
        "SignInId - Invalid format 10"   | "SID01AA-1001-1001"
    }

    def "updateUserRole_1-1_#operater が実行者の場合、#vTargetUser の法人ユーザロールが変更されること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users/${vTargetSignInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", vRoleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        response.close()

        then:
        response.getStatusLine().getStatusCode() == 200

        where:
        operater       | vTargetUser    | vSignInId      | vPassword   | vTargetSignInId | vRoleId
        "口座管理者"   | "ユーザ管理者" | "SID11AA-1001" | "Password1" | "SID11AA-1212"  | "00000000-0000-4000-0000-000000000022"
        "口座管理者"   | "業務担当者"   | "SID11AA-1001" | "Password1" | "SID11AA-1226"  | "00000000-0000-4000-0000-000000000023"
        "口座管理者"   | "業務承認者"   | "SID11AA-1001" | "Password1" | "SID11AA-1233"  | "00000000-0000-4000-0000-000000000022"
        "ユーザ管理者" | "業務担当者"   | "SID11AA-1011" | "Password1" | "SID11AA-1233"  | "00000000-0000-4000-0000-000000000023"
        "ユーザ管理者" | "業務承認者"   | "SID11AA-1011" | "Password1" | "SID11AA-1226"  | "00000000-0000-4000-0000-000000000022"
    }

    def "updateUserRole_1-1_#operater が実行者の場合、#vTargetUser の法人ユーザロールが変更されること(サービスIDがデフォルト以外)"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def path = "/services/users/${vTargetSignInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", vRoleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        response.close()

        then:
        response.getStatusLine().getStatusCode() == 200

        def dcUser = sql.firstRow("""
            SELECT * FROM service_user WHERE sign_in_id = '${vTargetSignInId}'
        """)
        dcUser.get("role_id") == vRoleId

        where:
        operater       | vTargetUser    | vSignInId      | vTargetSignInId | vRoleId
        "口座管理者"   | "ユーザ管理者" | "SID11AB-3001" | "SID11AB-3012"  | "00000000-0000-4000-0000-000000000222"
        "口座管理者"   | "業務担当者"   | "SID11AB-3001" | "SID11AB-3021"  | "00000000-0000-4000-0000-000000000223"
        "口座管理者"   | "業務承認者"   | "SID11AB-3001" | "SID11AB-3031"  | "00000000-0000-4000-0000-000000000222"
        "ユーザ管理者" | "業務担当者"   | "SID11AB-3011" | "SID11AB-3021"  | "00000000-0000-4000-0000-000000000223"
        "ユーザ管理者" | "業務承認者"   | "SID11AB-3011" | "SID11AB-3031"  | "00000000-0000-4000-0000-000000000222"
    }

    def "updateUserRole_2-1_存在しないサインインIDを指定した場合、業務エラーになること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AA-9999"
        def userRole = "00000000-0000-4000-0000-000000000022"
        def path = "/services/users/${signInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", userRole),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "updateUserRole_2-2_別テナントの #target のユーザ情報が変更されないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${vTargetSignInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", "00000000-0000-4000-0000-000000000022"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        where:
        target           | vTargetSignInId
        "サービス管理者" | "SID11AB-3001"
        "ユーザ管理者"   | "SID11AB-3011"
        "業務担当者"     | "SID11AB-3021"
        "業務承認者"     | "SID11AB-3031"
    }

    def "updateUserRole_2-3_指定したサインインIDがユーザ無効の場合、業務エラーになりこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AA-1121"
        def userRole = "00000000-0000-4000-0000-000000000022"
        def path = "/services/users/${signInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", userRole),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE2503"
    }

    def "updateUserRole_2-4_指定したサインインIDのユーザロールが操作者のロール権限より上位の場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users/${vTargetSignInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", vRoleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE3101"

        where:
        vSignInId      | vPassword   | vTargetSignInId | vRoleId
        "SID11AA-1011" | "Password1" | "SID11AA-1001"  | "00000000-0000-4000-0000-000000000022"
        "SID11AA-1011" | "Password1" | "SID11AA-1071"  | "00000000-0000-4000-0000-000000000022"
    }

    def "updateUserRole_2-5_role_id が #testCase の時、バリデーションエラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AA-1121"
        def path = "/services/users/${signInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", vRoleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase         | vRoleId
        "最低文字数未満" | ""
        "最大文字数超過" | "00000000-0000-4000-0000-1234567890123"
    }

    def "updateUserRole_2-6_指定したロールIDが存在しない場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AA-1021"
        def userRole = "00000000-0000-4000-0000-999999999999"
        def path = "/services/users/${signInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", userRole),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "updateUserRole_2-7_指定したロールIDが操作者のロール権限より上位の場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users/${vTargetSignInId}/role"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", vRoleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE3100"

        where:
        vSignInId      | vPassword   | vTargetSignInId | vRoleId
        "SID11AA-1001" | "Password1" | "SID11AA-1021"  | "00000000-0000-4000-0000-000000000020"
        "SID11AA-1011" | "Password1" | "SID11AA-1021"  | "00000000-0000-4000-0000-000000000020"
        "SID11AA-1011" | "Password1" | "SID11AA-1021"  | "00000000-0000-4000-0000-000000000021"
    }

    def "updateUserRole_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def path = "/services/users/SID11AA-1011/role"
        def userRole = "00000000-0000-4000-0000-000000000022"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_role", userRole),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUserRole_9-2_業務担当者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def path = "/services/users/SID11AA-1031/role"
        def roleId = "00000000-0000-4000-0000-000000000023"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", roleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUserRole_9-3_業務承認者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def path = "/services/users/SID11AA-1021/role"
        def roleId = "00000000-0000-4000-0000-000000000022"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", roleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUserRole_10-1_SignInIdが無効です - #testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${targetSignInId}/role"
        def roleId = "00000000-0000-4000-0000-000000000022"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("role_id", roleId),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                         | targetSignInId
        "SignInId - Don't contain 'SID'" | "00001AA-1011"
        "SignInId - Invalid format 1"    | "SID0100"
        "SignInId - Invalid format 2"    | "SIDAA10-1011"
        "SignInId - Invalid format 3"    | "SID00GG-1000"
        "SignInId - Invalid format 4"    | "SID00AA-100"
        "SignInId - Invalid format 5"    | "SID00AA-10000"
        "SignInId - Invalid format 6"    | "SID0A-10000"
        "SignInId - Invalid format 7"    | "SIDAAAA-BBBB"
        "SignInId - Invalid format 8"    | "SID01"
        "SignInId - Invalid format 9"    | "SID01-1001-1001"
        "SignInId - Invalid format 10"   | "SID01AA-1001-1001"
    }

    def "updateUserStatus_1-1_#operater が実行者の場合、#vTargetUser の法人ユーザの状態を #vUserStatus から #vToStatus に変更できること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, "Password1")
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", vToStatus),
                Map.entry("reason_code", vReasonCode),
                Map.entry("reason_detail", "YYYY/MM/DD その他"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == vTargetSignInId
        responseBody.has("user_name") == true
        responseBody.get("user_status").asText() == vToStatus

        cleanup:
        sql.execute("""
                UPDATE service_user
                SET user_status = 'active'
                WHERE sign_in_id IN ('SID11AA-1213', 'SID11AA-1227', 'SID11AA-1234', 'SID11AA-1228', 'SID11AA-1235', 'SID11AA-1229')
        """)

        where:
        operater       | vTargetUser    | vSignInId      | vTargetSignInId | vUserStatus           | vToStatus   | vReasonCode
        "口座管理者"   | "ユーザ管理者" | "SID11AA-1001" | "SID11AA-1213"  | "active"              | "suspended" | "SUSS0001"
        "口座管理者"   | "ユーザ管理者" | "SID11AA-1001" | "SID11AA-1213"  | "suspended"           | "active"    | "SUSA0005"
        "口座管理者"   | "ユーザ管理者" | "SID11AA-1001" | "SID11AA-1213"  | "active"              | "inactive"  | "SUST0004"
        "口座管理者"   | "業務担当者"   | "SID11AA-1001" | "SID11AA-1227"  | "active"              | "suspended" | "SUSS0003"
        "口座管理者"   | "業務担当者"   | "SID11AA-1001" | "SID11AA-1227"  | "suspended"           | "active"    | "SUSA0006"
        "口座管理者"   | "業務担当者"   | "SID11AA-1001" | "SID11AA-1227"  | "active"              | "inactive"  | "SUST0004"
        "口座管理者"   | "業務承認者"   | "SID11AA-1001" | "SID11AA-1234"  | "active"              | "suspended" | "SUSS0003"
        "口座管理者"   | "業務承認者"   | "SID11AA-1001" | "SID11AA-1234"  | "suspended"           | "active"    | "SUSA0006"
        "口座管理者"   | "業務承認者"   | "SID11AA-1001" | "SID11AA-1234"  | "active"              | "inactive"  | "SUST0006"
        "ユーザ管理者" | "業務担当者"   | "SID11AA-1011" | "SID11AA-1228"  | "active"              | "suspended" | "SUSS0006"
        "ユーザ管理者" | "業務担当者"   | "SID11AA-1011" | "SID11AA-1228"  | "suspended"           | "inactive"  | "SUST0006"
        "ユーザ管理者" | "業務承認者"   | "SID11AA-1011" | "SID11AA-1235"  | "active"              | "suspended" | "UUSS0001"
        "ユーザ管理者" | "業務承認者"   | "SID11AA-1011" | "SID11AA-1235"  | "suspended"           | "inactive"  | "SUST0006"
        "ユーザ管理者" | "業務担当者"   | "SID11AA-1011" | "SID11AA-1229"  | "temporary_suspended" | "inactive"  | "SUST0006"
    }

    def "updateUserStatus_1-1_#operater が実行者の場合、#vTargetUser の法人ユーザの状態を #vUserStatus から #vToStatus に変更できること（サービスIDがデフォルト以外）"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", vToStatus),
                Map.entry("reason_code", vReasonCode),
                Map.entry("reason_detail", "YYYY/MM/DD その他"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == vTargetSignInId
        responseBody.has("user_name") == true
        responseBody.get("user_status").asText() == vToStatus

        cleanup:
        sql.execute("""
                UPDATE service_user
                SET user_status = 'active'
                WHERE sign_in_id IN ('SID11AB-3001','SID11AB-3011','SID11AB-3021','SID11AB-3031')
        """)

        where:
        operater       | vTargetUser    | vSignInId      | vTargetSignInId | vUserStatus           | vToStatus   | vReasonCode
        "口座管理者"   | "ユーザ管理者" | "SID11AB-3001" | "SID11AB-3011"  | "active"              | "suspended" | "SUSS0001"
        "口座管理者"   | "ユーザ管理者" | "SID11AB-3001" | "SID11AB-3011"  | "suspended"           | "active"    | "SUSA0005"
        "口座管理者"   | "ユーザ管理者" | "SID11AB-3001" | "SID11AB-3011"  | "active"              | "inactive"  | "SUST0004"
        "口座管理者"   | "業務担当者"   | "SID11AB-3001" | "SID11AB-3021"  | "active"              | "suspended" | "SUSS0003"
        "口座管理者"   | "業務担当者"   | "SID11AB-3001" | "SID11AB-3021"  | "suspended"           | "active"    | "SUSA0006"
        "口座管理者"   | "業務担当者"   | "SID11AB-3001" | "SID11AB-3021"  | "active"              | "inactive"  | "SUST0004"
        "口座管理者"   | "業務承認者"   | "SID11AB-3001" | "SID11AB-3031"  | "active"              | "suspended" | "SUSS0003"
        "口座管理者"   | "業務承認者"   | "SID11AB-3001" | "SID11AB-3031"  | "suspended"           | "active"    | "SUSA0006"
        "口座管理者"   | "業務承認者"   | "SID11AB-3001" | "SID11AB-3031"  | "active"              | "inactive"  | "SUST0006"
        "ユーザ管理者" | "業務担当者"   | "SID11AB-3011" | "SID11AB-3021"  | "active"              | "suspended" | "SUSS0006"
        "ユーザ管理者" | "業務担当者"   | "SID11AB-3011" | "SID11AB-3021"  | "suspended"           | "inactive"  | "SUST0006"
        "ユーザ管理者" | "業務承認者"   | "SID11AB-3011" | "SID11AB-3031"  | "active"              | "suspended" | "UUSS0001"
        "ユーザ管理者" | "業務承認者"   | "SID11AB-3011" | "SID11AB-3031"  | "suspended"           | "inactive"  | "SUST0006"
        "ユーザ管理者" | "業務担当者"   | "SID11AB-3011" | "SID11AB-3021"  | "temporary_suspended" | "inactive"  | "SUST0006"
    }

    def "updateUserStatus_1-2_#operater が実行者で理由詳細が #testCase の場合、#vTargetUser の法人ユーザの状態を #vUserStatus から #vToStatus に変更できること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, "Password1")
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", vToStatus),
                Map.entry("reason_code", vReasonCode),
                Map.entry("reason_detail", vReasonDetail),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("sign_in_id").asText() == vTargetSignInId
        responseBody.has("user_name") == true
        responseBody.get("user_status").asText() == vToStatus

        cleanup:
        sql.execute("""
                UPDATE service_user
                SET user_status = 'active'
                WHERE sign_in_id IN ('SID11AA-1213', 'SID11AA-1227', 'SID11AA-1234', 'SID11AA-1228', 'SID11AA-1235', 'SID11AA-1229')
        """)

        where:
        operater       | vTargetUser    | vSignInId      | vTargetSignInId | vUserStatus           | vToStatus   | vReasonCode | testCase                   | vReasonDetail
        "口座管理者"   | "ユーザ管理者" | "SID11AA-1001" | "SID11AA-1213"  | "active"              | "suspended" | "SUSS0001"  | "空文字列"                 | ""
        "口座管理者"   | "ユーザ管理者" | "SID11AA-1001" | "SID11AA-1213"  | "suspended"           | "active"    | "SUSA0005"  | "半角スペース3文字のみ"    | "   "
        "口座管理者"   | "ユーザ管理者" | "SID11AA-1001" | "SID11AA-1213"  | "active"              | "inactive"  | "SUST0004"  | "全角スペース3文字のみ"    | "　　　"
        "口座管理者"   | "業務担当者"   | "SID11AA-1001" | "SID11AA-1227"  | "active"              | "suspended" | "SUSS0003"  | "半角全角スペース混在のみ" | "  　"
        "口座管理者"   | "業務担当者"   | "SID11AA-1001" | "SID11AA-1227"  | "suspended"           | "active"    | "SUSA0006"  | "全角半角スペース混在のみ" | "　  "
        "口座管理者"   | "業務担当者"   | "SID11AA-1001" | "SID11AA-1227"  | "active"              | "inactive"  | "SUST0004"  | "空文字列"                 | ""
        "口座管理者"   | "業務承認者"   | "SID11AA-1001" | "SID11AA-1234"  | "active"              | "suspended" | "SUSS0003"  | "半角スペース3文字のみ"    | "   "
        "口座管理者"   | "業務承認者"   | "SID11AA-1001" | "SID11AA-1234"  | "suspended"           | "active"    | "SUSA0006"  | "全角スペース3文字のみ"    | "　　　"
        "口座管理者"   | "業務承認者"   | "SID11AA-1001" | "SID11AA-1234"  | "active"              | "inactive"  | "SUST0006"  | "半角全角スペース混在のみ" | "  　"
        "ユーザ管理者" | "業務担当者"   | "SID11AA-1011" | "SID11AA-1228"  | "active"              | "suspended" | "SUSS0006"  | "全角半角スペース混在のみ" | "　  "
        "ユーザ管理者" | "業務担当者"   | "SID11AA-1011" | "SID11AA-1228"  | "suspended"           | "inactive"  | "SUST0006"  | "空文字列"                 | ""
        "ユーザ管理者" | "業務承認者"   | "SID11AA-1011" | "SID11AA-1235"  | "active"              | "suspended" | "UUSS0001"  | "半角スペース3文字のみ"    | "   "
        "ユーザ管理者" | "業務承認者"   | "SID11AA-1011" | "SID11AA-1235"  | "suspended"           | "inactive"  | "SUST0006"  | "全角スペース3文字のみ"    | "　　　"
        "ユーザ管理者" | "業務担当者"   | "SID11AA-1011" | "SID11AA-1229"  | "temporary_suspended" | "inactive"  | "SUST0006"  | "半角全角スペース混在のみ" | "  　"
    }

    //TODO updateUserStatus_1-2_初期設定が完了していないユーザがサインイン停止状態でアクティブに更新した際に、初期設定待ち状態に遷移すること(FinZone,BizZone)

    def "updateUserStatus_2-1_存在しないサインインIDを指定した場合、業務エラーになること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID01AB-9999"
        def path = "/services/users/${signInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"
    }

    def "updateUserStatus_2-2_別テナントの #target のユーザ情報が変更されないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        where:
        target           | vTargetSignInId
        "サービス管理者" | "SID11AB-3001"
        "ユーザ管理者"   | "SID11AB-3011"
        "業務担当者"     | "SID11AB-3021"
        "業務承認者"     | "SID11AB-3031"
    }

    def "updateUserStatus_2-3_#item が #testCase の時、バリデーションエラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def vTargetSignInId = "SID11AA-1021"
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", vUserStatus),
                Map.entry("reason_code", vReasonCode),
                Map.entry("reason_detail", vReasonDetail),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        item            | testCase             | vUserStatus   | vReasonCode         | vReasonDetail
        "user_status"   | "想定外のステータス" | "test_status" | "SUSS0001"          | "YYYY/MM/DD 端末紛失"
        "user_status"   | "空文字"             | ""            | "SUSS0001"          | "YYYY/MM/DD 端末紛失"
        "reason_code"   | "最低文字数未満"     | "active"      | ""                  | "YYYY/MM/DD 端末紛失"
        "reason_code"   | "最大文字数超過"     | "active"      | "TEST1234567890123" | "YYYY/MM/DD 端末紛失"
        "reason_detail" | "最大文字数超過"     | "active"      | "SUSS0001"          | "テスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバーテスト文字数オーバー"
    }

    def "updateUserStatus_2-4_#vToStatus の状態には変更できないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def vTargetSignInId = "SID11AA-1021"
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", vUserStatus),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        vToStatus            | vUserStatus
        "初期設定待ち"       | "initializing"
        "サインイン一時停止" | "temporary_suspended"
    }

    def "updateUserStatus_2-5_指定したサインインIDがユーザ無効の場合、業務エラーになりこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def signInId = "SID11AA-1121"
        def path = "/services/users/${signInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE2503"
    }

    def "updateUserStatus_2-6_reason_code が #testCase の場合、業務エラーになること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def vTargetSignInId = "SID11AA-1021"
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", vReason_code),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        where:
        testCase                                 | vReason_code
        "存在しない"                             | "UUSS9999"
        "変更後の状態と一致しない操作理由コード" | "SUSA0005"
    }

    def "updateUserStatus_2-7_#vBeforUserStatus の法人ユーザの状態を #vUserStatus の状態には変更できないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", vUserStatus),
                Map.entry("reason_code", vReasonCode),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE2502"

        where:
        vTargetSignInId | vBeforUserStatus     | vUserStatus | vReasonCode
        "SID11AA-1236"  | "サインイン一時停止" | "active"    | "SUSA0005"
        "SID11AA-1236"  | "サインイン一時停止" | "suspended" | "SUSS0001"
    }

    def "updateUserStatus_2-8_指定したサインインIDのユーザロールが操作者のロール権限より上位の場合、業務エラーとなること"() {
        setup:
        def accessToken = adhocHttpHelper.signInService(vSignInId, vPassword)
        def path = "/services/users/${vTargetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE3101"

        where:
        vSignInId      | vPassword   | vTargetSignInId
        "SID11AA-1011" | "Password1" | "SID11AA-1001"
        "SID11AA-1011" | "Password1" | "SID11AA-1071"
    }

    def "updateUserStatus_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def signInId = "SID01AB-1011"
        def path = "/services/users/${signInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUserStatus_9-2_業務担当者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1021", "Password1")
        def signInId = "SID01AB-1011"
        def path = "/services/users/${signInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUserStatus_9-3_業務承認者はアクセスできないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def signInId = "SID01AB-1011"
        def path = "/services/users/${signInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "updateUserStatus_10-1_SignInIdが無効です - #testCase"() {
        setup:
        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def path = "/services/users/${targetSignInId}/status"
        // リクエストを定義
        Map<String, Object> requestMap = Map.ofEntries(
                Map.entry("user_status", "suspended"),
                Map.entry("reason_code", "SUSS0001"),
                Map.entry("reason_detail", "YYYY/MM/DD 端末紛失"),
        )
        def requestData = mapper.writeValueAsString(requestMap)
        def httpPut = adhocHttpHelper.httpPut(path).setToken(accessToken).setBody(requestData).build()

        when:
        def response = httpClient.execute(httpPut)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                         | targetSignInId
        "SignInId - Don't contain 'SID'" | "00001AA-1011"
        "SignInId - Invalid format 1"    | "SID0100"
        "SignInId - Invalid format 2"    | "SIDAA10-1011"
        "SignInId - Invalid format 3"    | "SID00GG-1000"
        "SignInId - Invalid format 4"    | "SID00AA-100"
        "SignInId - Invalid format 5"    | "SID00AA-10000"
        "SignInId - Invalid format 6"    | "SID0A-10000"
        "SignInId - Invalid format 7"    | "SIDAAAA-BBBB"
        "SignInId - Invalid format 8"    | "SID01"
        "SignInId - Invalid format 9"    | "SID01-1001-1001"
        "SignInId - Invalid format 10"   | "SID01AA-1001-1001"
    }
}
