package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders

import com.decurret_dcp.dcjpy.bpm.server.BpmServerMain
import com.decurret_dcp.dcjpy.bpm.server.adaptor.CognitoAdaptor
import com.decurret_dcp.dcjpy.bpm.server.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bpm.server.helper.http.AdhocHttpHelper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest(
        classes = BpmServerMain.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class DcUserNftResourceSpec extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    static Sql sql

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockCore

    AdhocHttpHelper adhocHttpHelper

    @Autowired
    CognitoAdaptor cognitoAdaptor

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        // BizZone の設定
        registry.add("bpmserver.zone_type", () -> "business_zone")
    }

    def setupSpec() {
        wiremockCore = new WireMockServer(options().port(8080))
        wiremockCore.start()
    }

    def cleanupSpec() {
        wiremockCore.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        adhocHttpHelper = new AdhocHttpHelper(applicationPort, cognitoAdaptor)
    }

    def cleanup() {
        // Do nothing.
    }

    // U23-31. ユーザ向け NFT 照会
    def "fetchNfts_1-1_アカウント管理者が実行した場合、正常終了すること"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/nft")
                .setToken(idToken)
                .addQuery("nft_id", "renewable_energy_token")
                .build()

        wiremockCore.stubFor(get(urlEqualTo("/tokenSpecs/nft?nft_id=renewable_energy_token&account_id=6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi&date_sort=desc&offset=0&limit=100"))
                .willReturn(
                        okJson("""
                        {
                            "nfts" : [
                                {
                                    "nft_token_id" : "nft_token_id" ,
                                    "token_status" : "token_status" ,
                                    "metadata_id" : "metadata_id" ,
                                    "metadata_hash" : "metadata_hash" ,
                                    "metadata_detail" : {
                                        "metadata_detail" : "1"
                                    },
                                    "minted_account_id" : "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi" ,
                                    "owner_account_id" : "601nYszi6baA9yiXsjn0fMelnsbi1sA6" ,
                                    "operator_account_id" : "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5" ,
                                    "locked" : false
                                },
                                {
                                    "nft_token_id" : "nft_token_id2" ,
                                    "token_status" : "token_status2" ,
                                    "metadata_id" : "metadata_id2" ,
                                    "metadata_hash" : "metadata_hash2" ,
                                    "metadata_detail" : {
                                        "metadata_detail" : "2"
                                    },
                                    "minted_account_id" : "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5" ,
                                    "owner_account_id" : "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5" ,
                                    "operator_account_id" : "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5" ,
                                    "locked" : false
                                } 
                            ],
                            "offset" : 0 ,
                            "limit" : 100 ,
                            "total" : 2
                        }
                """)
                ))

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("nfts").size() == 2

        responseBody.get("nfts")[0].get("nft_token_id").asText() == "nft_token_id"
        responseBody.get("nfts")[0].get("owner_account").get("dc_bank_number").asText() == "DC001-2222-0102-1"
        responseBody.get("nfts")[0].get("mint_account").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("nfts")[0].get("previous_account").get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("nfts")[0].get("token_status").asText() == "token_status"
        responseBody.get("nfts")[0].get("metadata_detail").toString() == '{"metadata_detail":"1"}'
        responseBody.get("nfts")[0].get("metadata_hash").asText() == "metadata_hash"

        responseBody.get("nfts")[1].get("nft_token_id").asText() == "nft_token_id2"
        responseBody.get("nfts")[1].get("owner_account").get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("nfts")[1].get("mint_account").get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("nfts")[1].get("previous_account").get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("nfts")[1].get("token_status").asText() == "token_status2"
        responseBody.get("nfts")[1].get("metadata_detail").toString() == '{"metadata_detail":"2"}'
        responseBody.get("nfts")[1].get("metadata_hash").asText() == "metadata_hash2"

        responseBody.get("paging").get("offset").asInt() == 0
        responseBody.get("paging").get("limit").asInt() == 100
        responseBody.get("paging").get("total").asLong() == 2L
    }

    def "fetchNfts_1-2_アカウント管理者が実行した場合、正常終了すること"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/nft")
                .setToken(idToken)
                .addQuery("nft_id", "renewable_energy_token")
                .addQuery("sort_order", vSortOrder)
                .addQuery("offset", vOffset)
                .addQuery("limit", vLimit)
                .build()


        wiremockCore.stubFor(get(urlEqualTo("/tokenSpecs/nft?nft_id=renewable_energy_token&account_id=6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi&date_sort=${vSortOrder}&offset=${vOffset}&limit=${vLimit}"))
                .willReturn(
                        okJson("""
                        {
                            "nfts" : [
                                {
                                    "nft_token_id" : "nft_token_id" ,
                                    "token_status" : "token_status" ,
                                    "metadata_id" : "metadata_id" ,
                                    "metadata_hash" : "metadata_hash" ,
                                    "metadata_detail" : {
                                        "metadata_detail" : "1"
                                    },
                                    "minted_account_id" : "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi" ,
                                    "owner_account_id" : "601nYszi6baA9yiXsjn0fMelnsbi1sA6" ,
                                    "operator_account_id" : "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5" ,
                                    "locked" : false
                                }
                            ],
                            "offset" : 0 ,
                            "limit" : 100 ,
                            "total" : 1
                        }
                """)
                ))

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("nfts").size() == 1

        responseBody.get("nfts")[0].get("nft_token_id").asText() == "nft_token_id"
        responseBody.get("nfts")[0].get("owner_account").get("dc_bank_number").asText() == "DC001-2222-0102-1"
        responseBody.get("nfts")[0].get("mint_account").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("nfts")[0].get("previous_account").get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("nfts")[0].get("token_status").asText() == "token_status"
        responseBody.get("nfts")[0].get("metadata_detail").toString() == '{"metadata_detail":"1"}'
        responseBody.get("nfts")[0].get("metadata_hash").asText() == "metadata_hash"

        responseBody.get("paging").get("offset").asText() == vOffset
        responseBody.get("paging").get("limit").asText() == vLimit
        responseBody.get("paging").get("total").asText() == "1"

        where:
        vSortOrder | vOffset     | vLimit
        "desc"     | "0"         | "1"
        "asc"      | "*********" | "100"
    }

    def "fetchNfts_2-1_バリデーションエラー - nft_id が設定されていない"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/nft")
                .setToken(idToken)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"
    }

    def "fetchNfts_2-2_バリデーションエラー - #testCase"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/nft")
                .setToken(idToken)
                .addQuery("nft_id", vNftId)
                .addQuery("sort_order", vSortOrder)
                .addQuery("offset", vOffset)
                .addQuery("limit", vLimit)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "ERV0001"

        where:
        testCase                 | vNftId                   | vSortOrder | vOffset         | vLimit
        "nft_id が不正"          | "aaa"                    | "desc"     | "0"             | "100"
        "sortOrder が不正"       | "renewable_energy_token" | "aaa"      | "0"             | "100"
        "offset が不正 "         | "renewable_energy_token" | "desc"     | "aaa"           | "100"
        "offset が最小値より下 " | "renewable_energy_token" | "desc"     | "-1"            | "100"
        "offset が最大値より上 " | "renewable_energy_token" | "desc"     | "1000000000000" | "100"
        "limit が不正 "          | "renewable_energy_token" | "desc"     | "0"             | "aaa"
        "limit が最小値より下 "  | "renewable_energy_token" | "desc"     | "0"             | "0"
        "limit が最大値より上 "  | "renewable_energy_token" | "desc"     | "0"             | "101"
    }

    def "fetchNfts_9-1_ID_TOKENなしではアクセスできない"() {
        setup:
        def httpGet = adhocHttpHelper.httpGet("/holders/nft")
                .addQuery("nft_id", "renewable_energy_token")
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
    }

    def "fetchNfts_9-2_ #roleType では実行できない"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder(vSignInId, "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/nft")
                .setToken(idToken)
                .addQuery("nft_id", "renewable_energy_token")
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"

        where:
        roleType       | vSignInId
        "ユーザ管理者" | "SID01AA-1011"
        "業務承認者"   | "SID01AA-1031"
        "業務担当者"   | "SID01AA-1021"

    }

    def "fetchNfts_9-3_ #testCase の時、実行できない"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/nft")
                .setToken(idToken)
                .addQuery("nft_id", "renewable_energy_token")
                .build()


        wiremockCore.stubFor(get(urlEqualTo("/tokenSpecs/nft?nft_id=renewable_energy_token&account_id=6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi&date_sort=desc&offset=0&limit=100"))
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "${vCoreError}"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == vStatusCode
        responseBody.get("error_code").asText() == vErrorCode

        where:
        testCase               | vCoreError | vStatusCode | vErrorCode
        "nft_idのヒットがない" | "E0079"    | 404         | "EGE0100"
    }
}