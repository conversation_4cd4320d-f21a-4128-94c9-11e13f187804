package com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.resolver

import com.decurret_dcp.dcjpy.bpm.server.BpmServerMain
import com.decurret_dcp.dcjpy.bpm.server.adaptor.CognitoAdaptor
import com.decurret_dcp.dcjpy.bpm.server.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bpm.server.helper.http.AdhocHttpHelper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.get
import static com.github.tomakehurst.wiremock.client.WireMock.okJson
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest(
        classes = BpmServerMain.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
class FinZoneServiceIdHeaderResolverNeverSpec extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockCore

    AdhocHttpHelper adhocHttpHelper

    @Autowired
    CognitoAdaptor cognitoAdaptor

    static Sql sql

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)
        registry.add("bpmserver.zone_type", () -> "financial_zone")
        var mode = "never"
        registry.add("bpmserver.authenticator-app.company", () -> mode)
        var multiMode = "never"
        registry.add("bpmserver.multitenant.mode", () -> multiMode)
    }

    def setupSpec() {
        wiremockCore = new WireMockServer(options().port(8080))
        wiremockCore.start()
    }

    def cleanupSpec() {
        wiremockCore.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        adhocHttpHelper = new AdhocHttpHelper(applicationPort, cognitoAdaptor)
    }

    def cleanup() {
        // Do nothing.
    }

    // U01-02_法人ユーザ向け DCJPY WEB サインイン受付 (認証アプリなし)
    def "signIn_1-1_[FZ:マルチテナントモード:never]法人ユーザ向け サインイン受付 (認証アプリなし)ができること - 利用可能 - #testCase (メソッドにServiceIdHeaderあり)"() {
        setup:
        def path = "/holders/sign_in"
        def body = """
                {
                    "sign_in_id": "${signInId}",
                    "password": "Password1"
                }
                """
        def httpPost = adhocHttpHelper.httpPost(path)
                .setBody(body)
                .build()

        // サインイン前のDBを取得
        def beforeHistoryEntity = sql.firstRow("""
                SELECT * FROM dc_user_sign_in_history
                WHERE sign_in_id = ${signInId}
        """)

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.asText("id_token") != null
        responseBody.asText("access_token") != null
        responseBody.asText("refresh_token") != null
        responseBody.get("sign_in_status").asText() == "available"

        // サインイン後のDBを取得
        def afterHistoryEntity = sql.firstRow("""
                SELECT * FROM dc_user_sign_in_history
                WHERE sign_in_id = ${signInId}
        """)

        afterHistoryEntity.get("sign_in_id") == signInId
        afterHistoryEntity.get("current_signed_in_at") != null
        afterHistoryEntity.get("last_signed_in_at") == beforeHistoryEntity.get("current_signed_in_at")

        where:
        testCase                  | signInId
        "テストアカウント管理者A" | "SID01AA-1001"
        "テストユーザ管理者A"     | "SID01AA-1011"
        "テスト業務担当者A1"      | "SID01AA-1021"
        "テスト業務承認者A1"      | "SID01AA-1031"
    }

    def "signIn_1-2_[FZ:マルチテナントモード:never]法人ユーザ向け サインイン受付 (認証アプリなし)ができること - 利用可能 - #testCase (メソッドにServiceIdHeaderあり-異なるservice_idを指定しても無視されること)"() {
        setup:
        def path = "/holders/sign_in"
        def body = """
                {
                    "sign_in_id": "${signInId}",
                    "password": "Password1"
                }
                """
        def httpPost = adhocHttpHelper.httpPost(path)
                .setServiceId("0d367be20aed7707d71dce4dbd8825f1")
                .setBody(body)
                .build()

        // サインイン前のDBを取得
        def beforeHistoryEntity = sql.firstRow("""
                SELECT * FROM dc_user_sign_in_history
                WHERE sign_in_id = ${signInId}
        """)

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.asText("id_token") != null
        responseBody.asText("access_token") != null
        responseBody.asText("refresh_token") != null
        responseBody.get("sign_in_status").asText() == "available"

        // サインイン後のDBを取得
        def afterHistoryEntity = sql.firstRow("""
                SELECT * FROM dc_user_sign_in_history
                WHERE sign_in_id = ${signInId}
        """)

        afterHistoryEntity.get("sign_in_id") == signInId
        afterHistoryEntity.get("current_signed_in_at") != null
        afterHistoryEntity.get("last_signed_in_at") == beforeHistoryEntity.get("current_signed_in_at")

        where:
        testCase                  | signInId
        "テストアカウント管理者A" | "SID01AA-1001"
        "テストユーザ管理者A"     | "SID01AA-1011"
        "テスト業務担当者A1"      | "SID01AA-1021"
        "テスト業務承認者A1"      | "SID01AA-1031"
    }

    def "fetchUsers_1-3_[FZ:マルチテナントモード:never]ユーザ管理者配下のユーザ一覧が取得できること(メソッドにServiceIdHeaderなし)"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1011", "Password1")
        def path = "/holders/users"

        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(idToken)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID01AA-1021"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000012"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        // キー情報のみ確認し、他の情報は省略
        responseBody.get("users")[1].get("sign_in_id").asText() == "SID01AA-1022"
        responseBody.get("users")[2].get("sign_in_id").asText() == "SID01AA-1023"
        responseBody.get("users")[3].get("sign_in_id").asText() == "SID01AA-1031"
        responseBody.get("users")[4].get("sign_in_id").asText() == "SID01AA-1032"
        responseBody.get("users")[5].get("sign_in_id").asText() == "SID01AA-1033"
        responseBody.get("users")[6].get("sign_in_id").asText() == "SID01AA-1421"
        responseBody.get("users")[7].get("sign_in_id").asText() == "SID01AA-1422"
        responseBody.get("users")[8].get("sign_in_id").asText() == "SID01AA-1431"
        responseBody.get("users")[9].get("sign_in_id").asText() == "SID01AA-1432"
        responseBody.get("users")[10].get("sign_in_id").asText() == "SID01AA-1521"
        responseBody.get("users")[11].get("sign_in_id").asText() == "SID01AA-1522"
        responseBody.get("users")[12].get("sign_in_id").asText() == "SID01AA-1523"
        responseBody.get("users")[13].get("sign_in_id").asText() == "SID01AA-1721"
        responseBody.get("users")[14].get("sign_in_id").asText() == "SID01AA-1722"
        responseBody.get("users")[15].get("sign_in_id").asText() == "SID01AA-1731"
        responseBody.get("users")[16].get("sign_in_id").asText() == "SID01AA-1921"
        responseBody.get("users")[17].get("sign_in_id").asText() == "SID01AA-1931"

        responseBody.get("paging").get("offset").asInt() == 0
        responseBody.get("paging").get("limit").asInt() == 100
        responseBody.get("paging").get("total").asLong() == 18L
    }

    def "fetchUsers_1-4_[FZ:マルチテナントモード:never]ユーザ管理者配下のユーザ一覧が取得できること(メソッドにServiceIdHeaderなし - メソッドにServiceIdHeaderあり-異なるservice_idを指定しても無視されること)"() {
        setup:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1011", "Password1")
        def path = "/holders/users"

        def httpGet = adhocHttpHelper.httpGet(path)
                .setToken(idToken)
                .setServiceId("0d367be20aed7707d71dce4dbd8825f1")
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("users")[0].get("sign_in_id").asText() == "SID01AA-1021"
        responseBody.get("users")[0].get("user_name").asText() == "テストギョウムタントウシャＡ１"
        responseBody.get("users")[0].get("user_status").asText() == "active"
        responseBody.get("users")[0].get("role").get("role_id").asText() == "00000000-0000-4000-0000-000000000012"
        responseBody.get("users")[0].get("role").get("role_name").asText() == "業務担当者"
        responseBody.get("users")[0].get("role").get("role_type").asText() == "operator"
        responseBody.get("users")[0].get("registered_at").asText() == "2023-12-01T12:00:00+09:00"

        // キー情報のみ確認し、他の情報は省略
        responseBody.get("users")[1].get("sign_in_id").asText() == "SID01AA-1022"
        responseBody.get("users")[2].get("sign_in_id").asText() == "SID01AA-1023"
        responseBody.get("users")[3].get("sign_in_id").asText() == "SID01AA-1031"
        responseBody.get("users")[4].get("sign_in_id").asText() == "SID01AA-1032"
        responseBody.get("users")[5].get("sign_in_id").asText() == "SID01AA-1033"
        responseBody.get("users")[6].get("sign_in_id").asText() == "SID01AA-1421"
        responseBody.get("users")[7].get("sign_in_id").asText() == "SID01AA-1422"
        responseBody.get("users")[8].get("sign_in_id").asText() == "SID01AA-1431"
        responseBody.get("users")[9].get("sign_in_id").asText() == "SID01AA-1432"
        responseBody.get("users")[10].get("sign_in_id").asText() == "SID01AA-1521"
        responseBody.get("users")[11].get("sign_in_id").asText() == "SID01AA-1522"
        responseBody.get("users")[12].get("sign_in_id").asText() == "SID01AA-1523"
        responseBody.get("users")[13].get("sign_in_id").asText() == "SID01AA-1721"
        responseBody.get("users")[14].get("sign_in_id").asText() == "SID01AA-1722"
        responseBody.get("users")[15].get("sign_in_id").asText() == "SID01AA-1731"
        responseBody.get("users")[16].get("sign_in_id").asText() == "SID01AA-1921"
        responseBody.get("users")[17].get("sign_in_id").asText() == "SID01AA-1931"

        responseBody.get("paging").get("offset").asInt() == 0
        responseBody.get("paging").get("limit").asInt() == 100
        responseBody.get("paging").get("total").asLong() == 18L
    }

    // U31-01. アカウント情報取得
    def "fetchAccount_1-1_[FZ:マルチテナントモード:never]アカウント情報を取得ができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/my_account").setToken(accessToken).build()
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                    "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                    "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                    "account_status": "active",
                                    "reason_code": "SASA0006",
                                    "zone_id": "3000",
                                    "zone_name": "モックギンコウゾーン",
                                    "balance": 210000,
                                    "cache_balance": 200000,
                                    "mint_limit": 9999,
                                    "burn_limit": 9999,
                                    "transfer_limit": 9999,
                                    "charge_limit": 9999,
                                    "cumulative_limit": 500,
                                    "cumulative_amount": 50000,
                                    "cumulative_date": "2023-12-15",
                                    "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        sql.execute("""
        INSERT INTO dc_account_reason (dc_bank_number, service_id, operation_type, reason_code, reason_detail, operated_at)
        VALUES ('DC001-1234-1234-1', '0', 'account_activated', 'SASA0006', '202401本人確認ができたため、解除する。', '2023-12-15T09:13:16Z')
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)
        // データを元に戻す
        sql.execute("""
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1'
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_status").asText() == "active"
        responseBody.get("balance").asLong() == 210000
        responseBody.get("zone").get("zone_id").asLong() == 3000
        responseBody.get("zone").get("zone_name").asText() == "モックギンコウゾーン"
        responseBody.get("operation_limit").get("mint_limit").asLong() == 9999
        responseBody.get("operation_limit").get("burn_limit").asLong() == 9999
        responseBody.get("operation_limit").get("transfer_limit").asLong() == 9999
        responseBody.get("operation_limit").get("charge_limit").asLong() == 9999
        responseBody.get("operation_limit").get("daily_cumulative_limit").asLong() == 500
        responseBody.get("cumulation").get("cumulative_amount").asLong() == 50000
        responseBody.get("cumulation").get("cumulative_date").asText() == "2023-12-15"
        responseBody.has("operated_reason") == false
        Objects.isNull(responseBody.get("applied_at"))
        Objects.isNull(responseBody.get("terminating_at"))
        responseBody.get("registered_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("terminated_at"))
    }
}
