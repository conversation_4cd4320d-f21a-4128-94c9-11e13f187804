package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.holders

import com.decurret_dcp.dcjpy.bpm.server.BpmServerMain
import com.decurret_dcp.dcjpy.bpm.server.adaptor.CognitoAdaptor
import com.decurret_dcp.dcjpy.bpm.server.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bpm.server.helper.http.AdhocHttpHelper
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.MappingBuilder
import groovy.sql.Sql
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.AwsCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.Delete
import software.amazon.awssdk.services.s3.model.DeleteObjectsRequest
import software.amazon.awssdk.services.s3.model.ListObjectsRequest
import software.amazon.awssdk.services.s3.model.ObjectIdentifier
import software.amazon.awssdk.services.s3.model.S3Object
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.badRequest
import static com.github.tomakehurst.wiremock.client.WireMock.get
import static com.github.tomakehurst.wiremock.client.WireMock.okJson
import static com.github.tomakehurst.wiremock.client.WireMock.put
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest(
        classes = BpmServerMain.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
class DcUserNftFileTasksResourceSpec extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    ObjectMapper mapper = new ObjectMapper()

    static Sql sql

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockCore

    AdhocHttpHelper adhocHttpHelper

    @Autowired
    CognitoAdaptor cognitoAdaptor

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        // BizZone の設定
        registry.add("bpmserver.zone_type", () -> "business_zone")
    }

    def setupSpec() {
        wiremockCore = new WireMockServer(options().port(8080))
        wiremockCore.start()
    }

    def cleanupSpec() {
        wiremockCore.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        adhocHttpHelper = new AdhocHttpHelper(applicationPort, cognitoAdaptor)
    }

    def cleanup() {
        sql.execute("""
            DELETE FROM dc_user_file_task
        """)
    }

    // CoreApiのモック（口座詳細取得）
    static MappingBuilder getAccount_success() {
        return get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                    "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                    "account_name": "モックギンコウコウザ1",
                                    "account_status": "active",
                                    "reason_code": 10000,
                                    "zone_id": "3000",
                                    "zone_name": "モックギンコウゾーン",
                                    "balance": 210000,
                                    "cache_balance": 200000,
                                    "mint_limit": 9999,
                                    "burn_limit": 9999,
                                    "transfer_limit": 9999,
                                    "charge_limit": 9999,
                                    "cumulative_limit": 500,
                                    "cumulative_amount": 50000,
                                    "cumulative_date": "2023-12-15",
                                    "applied_at": "2023-12-15T09:13:16Z",
                                    "registered_at": "2023-12-15T09:13:16Z",
                                    "terminating_at": "2023-12-15T09:13:16Z",
                                    "terminated_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                )
    }

    /* U81-66 ユーザ向け NFT照会ファイル 作成依頼 */
    def "listNftFile_1_1_NFT照会ファイル作成依頼のレコードが登録できること。"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def path = "/holders/file_tasks/nft/list"
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).build()

        wiremockCore.stubFor(getAccount_success())

        when:
        def response = httpClient.execute(httpPost)
        AdhocHttpHelper.toJson(response)
        httpPost.releaseConnection()

        then:
        response.getStatusLine().getStatusCode() == 200
        def rows = sql.rows("""
            SELECT
                *
            FROM
                dc_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_type").toString() == "nft_list"
        rows.get(0).get("task_status").toString() == "initialized"

        def orderDetailJson = rows.get(0).get("task_order_detail").toString()
        def orderDetail = mapper.readTree(orderDetailJson)
        orderDetail.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        orderDetail.get("file_task_detail_type").asText() == "nft_list"

    }

    def "listNftFile_3_1_アカウントの状態がアクティブ以外の場合、業務エラーになること"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def path = "/holders/file_tasks/nft/list"
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).build()

        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                    "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                    "account_name": "モックギンコウコウザ1",
                                    "account_status": "terminated",
                                    "reason_code": 10000,
                                    "zone_id": "3000",
                                    "zone_name": "モックギンコウゾーン",
                                    "balance": 0,
                                    "cache_balance": 0,
                                    "mint_limit": 9999,
                                    "burn_limit": 9999,
                                    "transfer_limit": 9999,
                                    "charge_limit": 9999,
                                    "cumulative_limit": 500,
                                    "cumulative_amount": 0,
                                    "cumulative_date": "2023-12-15",
                                    "applied_at": "2023-12-15T09:13:16Z",
                                    "registered_at": "2023-12-15T09:13:16Z",
                                    "terminating_at": "2023-12-15T09:13:16Z",
                                    "terminated_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE2000"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                dc_user_file_task
        """)
        rows.size() == 0
    }

    def "listNftFile_3_2_アカウントが存在しない場合、業務エラーになること"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def path = "/holders/file_tasks/nft/list"
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).build()

        //wiremockの設定
        wiremockCore.stubFor(put(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0001"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGE2000"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                dc_user_file_task
        """)
        rows.size() == 0
    }

    def "listNftFile_9_1_ACCESSトークンなしで、NFT照会ファイル作成依頼ができないこと"() {
        setup:
        def path = "/holders/file_tasks/nft/list"
        def httpPost = adhocHttpHelper.httpPost(path).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 401
        responseBody.get("error_code").asText() == "EGA0200"
        def rows = sql.rows("""
            SELECT
                *
            FROM
                dc_user_file_task
        """)
        rows.size() == 0
    }

    def "listNftFile_9_2_ユーザ管理者はNFT照会ファイル作成依頼ができないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1011", "Password1")
        def path = "/holders/file_tasks/nft/list"
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
        def rows = sql.rows("""
            SELECT
                *
            FROM
                dc_user_file_task
        """)
        rows.size() == 0

    }

    def "listNftFile_9_3_業務担当者はNFT照会ファイル作成依頼ができないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1021", "Password1")
        def path = "/holders/file_tasks/nft/list"
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
        def rows = sql.rows("""
            SELECT
                *
            FROM
                dc_user_file_task
        """)
        rows.size() == 0

    }

    def "listNftFile_9_4_業務承認者はNFT照会ファイル作成依頼ができないこと"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1031", "Password1")
        def path = "/holders/file_tasks/nft/list"
        def httpPost = adhocHttpHelper.httpPost(path).setToken(accessToken).build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        then:
        response.getStatusLine().getStatusCode() == 403
        responseBody.get("error_code").asText() == "EGA0200"
        def rows = sql.rows("""
            SELECT
                *
            FROM
                dc_user_file_task
        """)
        rows.size() == 0

    }
}
