package com.decurret_dcp.dcjpy.bpm.server.presentation.annotation.resolver

import com.decurret_dcp.dcjpy.bpm.server.BpmServerMain
import com.decurret_dcp.dcjpy.bpm.server.adaptor.CognitoAdaptor
import com.decurret_dcp.dcjpy.bpm.server.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bpm.server.helper.http.AdhocHttpHelper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import spock.lang.Shared
import spock.lang.Specification

import java.sql.Timestamp
import java.util.concurrent.TimeUnit
import java.util.regex.Matcher
import java.util.regex.Pattern

import static com.github.tomakehurst.wiremock.client.WireMock.get
import static com.github.tomakehurst.wiremock.client.WireMock.okJson
import static com.github.tomakehurst.wiremock.client.WireMock.post
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest(
        classes = BpmServerMain.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
class BizZoneServiceIdHeaderResolverMustUseSpec extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    static Sql sql

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockCore

    @Shared
    WireMockServer wiremockSigner

    AdhocHttpHelper adhocHttpHelper

    @Autowired
    CognitoAdaptor cognitoAdaptor

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        def zoneType = "business_zone"
        registry.add("bpmserver.zone_type", () -> zoneType)

        def mode = "never"
        registry.add("bpmserver.authenticator-app.company", () -> mode)

        def multiMode = "must_use"
        registry.add("bpmserver.multitenant.mode", () -> multiMode)
    }

    def setupSpec() {
        wiremockCore = new WireMockServer(options().port(8080))
        wiremockCore.start()
        wiremockSigner = new WireMockServer(options().port(8010))
        wiremockSigner.start()
    }

    def cleanupSpec() {
        wiremockCore.stop()
        wiremockSigner.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        adhocHttpHelper = new AdhocHttpHelper(applicationPort, cognitoAdaptor)
    }

    def cleanup() {
        // Do nothing.
    }

    private static final String REDIRECT_URI = "http://localhost:18080/holders/signer/token"

    // U03-01. Signer 認証開始
    def "authToken_1-1_[BZ:マルチテナントモード:must_use]リダイレクト先のURLが返却されること。"() {
        given:
        def idToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def path = "/holders/signer/token"

        def httpPost = adhocHttpHelper.httpPost(path)
                .setForm()
                .addQuery("bank_code", "0310")
                .addQuery("id_token", "${idToken}")
                .addQuery("redirect_uri", "${REDIRECT_URI}")
                .build()

        when:
        def response = httpClient.execute(httpPost)

        then:
        response.getStatusLine().getStatusCode() == 302
        def authUrl = response.getFirstHeader("Location").getValue()
        String regexPattern = "^https?://localhost:\\d+/signin\\?state_code=[A-Za-z0-9]+&redirect_uri=https?://localhost:\\d+/holders/signer/token"
        // パターンをコンパイル
        Pattern pattern = Pattern.compile(regexPattern)
        // URLをマッチング
        Matcher matcher = pattern.matcher(authUrl)
        matcher.matches() == Boolean.TRUE
        response.close()
    }

    // U03-02. Signer トークン発行
    def "authorization_1-1_[BZ:マルチテナントモード:must_use]認可が成功しアクセストークンを保存できること（サービスIDがデフォルト以外）"() {
        def signInId = "SID01AC-3001"
        def code = "dummyCode"
        def state = "dummyState0310"
        def path = "/holders/signer/token"

        def httpGet = adhocHttpHelper.httpGet(path)
                .addQuery("code", code)
                .addQuery("state_code", state)
                .build()

        wiremockSigner.stubFor(post(urlPathEqualTo("/auth/token"))
                .willReturn(
                        okJson("""
                                {
                                    "access_token": "dummy_token",
                                    "expires_in": "3600",
                                    "dc_bank_number": "DC001-1234-1234-1"
                                }
                                """)
                )
        )

        sql.execute("""
            INSERT INTO dc_user_oauth_state (oauth_state, sign_in_id, expires_at)
            VALUES ('dummyState0310', ${signInId}, ${new Timestamp(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(5L))})
        """)

        when:
        def response = httpClient.execute(httpGet)

        then:
        // レスポンスの確認
        response.getStatusLine().getStatusCode() == 200
        response.close()

        cleanup:
        sql.execute("DELETE FROM dc_user_oauth_state WHERE sign_in_id = ${signInId}")
    }

    // U31-01. アカウント情報取得
    def "fetchAccount_1-1_[BZ:マルチテナントモード:must_use]アカウント情報を取得ができること"() {
        setup:
        def accessToken = adhocHttpHelper.signInHolder("SID01AA-1001", "Password1")
        def httpGet = adhocHttpHelper.httpGet("/holders/my_account").setToken(accessToken).build()
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                    "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                    "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                    "account_status": "active",
                                    "reason_code": "SASA0006",
                                    "zone_id": "3001",
                                    "zone_name": "モックギンコウゾーン",
                                    "balance": 210000,
                                    "cache_balance": 200000,
                                    "mint_limit": 9999,
                                    "burn_limit": 9999,
                                    "transfer_limit": 9999,
                                    "charge_limit": 9999,
                                    "cumulative_limit": 500,
                                    "cumulative_amount": 50000,
                                    "cumulative_date": "2023-12-15",
                                    "registered_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                ))

        sql.execute("""
        INSERT INTO dc_account_reason (dc_bank_number, service_id, operation_type, reason_code, reason_detail, operated_at)
        VALUES ('DC001-1234-1234-1', '0', 'account_activated', 'SASA0006', '202401本人確認ができたため、解除する。', '2023-12-15T09:13:16Z')
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)
        // データを元に戻す
        sql.execute("""
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1'
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_status").asText() == "active"
        responseBody.get("balance").asLong() == 210000
        responseBody.get("zone").get("zone_id").asLong() == 3001
        responseBody.get("zone").get("zone_name").asText() == "モックギンコウゾーン"
        responseBody.get("operation_limit").get("mint_limit").asLong() == 9999
        responseBody.get("operation_limit").get("burn_limit").asLong() == 9999
        responseBody.get("operation_limit").get("transfer_limit").asLong() == 9999
        responseBody.get("operation_limit").get("charge_limit").asLong() == 9999
        responseBody.get("operation_limit").get("daily_cumulative_limit").asLong() == 500
        responseBody.get("cumulation").get("cumulative_amount").asLong() == 50000
        responseBody.get("cumulation").get("cumulative_date").asText() == "2023-12-15"
        responseBody.has("operated_reason") == false
        Objects.isNull(responseBody.get("applied_at"))
        Objects.isNull(responseBody.get("terminating_at"))
        responseBody.get("registered_at").asText() == "2023-12-15T18:13:16+09:00"
        Objects.isNull(responseBody.get("terminated_at"))
    }
}
