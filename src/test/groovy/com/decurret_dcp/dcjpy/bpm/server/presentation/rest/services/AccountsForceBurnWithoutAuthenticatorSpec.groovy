package com.decurret_dcp.dcjpy.bpm.server.presentation.rest.services

import com.decurret_dcp.dcjpy.bpm.server.BpmServerMain
import com.decurret_dcp.dcjpy.bpm.server.adaptor.CognitoAdaptor
import com.decurret_dcp.dcjpy.bpm.server.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bpm.server.helper.http.AdhocHttpHelper
import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.MappingBuilder
import com.github.tomakehurst.wiremock.stubbing.Scenario
import groovy.sql.Sql
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper
import org.testcontainers.spock.Testcontainers
import spock.lang.Shared
import spock.lang.Specification

import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest(
        classes = BpmServerMain.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class AccountsForceBurnWithoutAuthenticatorSpec extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    static final ObjectMapper mapper = new ObjectMapper()

    static Sql sql

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockCore

    @Shared
    WireMockServer wiremockBankGw

    AdhocHttpHelper adhocHttpHelper

    @Autowired
    CognitoAdaptor cognitoAdaptor

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        // 認証アプリ の設定
        registry.add("bpmserver.authenticator-app.service", () -> "never")

        // 口座同期 の設定
        registry.add("bpmserver.synchronize.account_name", () -> true)
    }

    def setupSpec() {
        wiremockCore = new WireMockServer(options().port(8080))
        wiremockCore.start()

        wiremockBankGw = new WireMockServer(options().port(18083))
        wiremockBankGw.start()
    }

    def cleanupSpec() {
        wiremockBankGw.stop()
        wiremockCore.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        adhocHttpHelper = new AdhocHttpHelper(applicationPort, cognitoAdaptor)
    }

    def cleanup() {
        sql.execute("""
            UPDATE dc_account_bank_linkage
            SET bank_link_status = 'authenticated'
            WHERE dc_bank_number = 'DC001-1234-1234-1'
            AND service_id = '0'
        """)
    }

    static MappingBuilder getAccount_success() {
        return get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                    "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                    "account_name": "モックギンコウコウザ1",
                                    "account_status": "active",
                                    "reason_code": 10000,
                                    "zone_id": "3000",
                                    "zone_name": "モックギンコウゾーン",
                                    "balance": 210000,
                                    "cache_balance": 200000,
                                    "mint_limit": 9999,
                                    "burn_limit": 9999,
                                    "transfer_limit": 9999,
                                    "charge_limit": 9999,
                                    "cumulative_limit": 500,
                                    "cumulative_amount": 50000,
                                    "cumulative_date": "2023-12-15",
                                    "applied_at": "2023-12-15T09:13:16Z",
                                    "registered_at": "2023-12-15T09:13:16Z",
                                    "terminating_at": "2023-12-15T09:13:16Z",
                                    "terminated_at": "2023-12-15T09:13:16Z"
                                }
                                """)
                )
    }

    static MappingBuilder getAccountBalance_success() {
        return get(urlPathEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/balances"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "total_balance": "1000000",
                                  "items": [{
                                  "zone_id": "3000",
                                  "zone_name": "モックギンコウゾーン1",
                                  "balance": 210000,
                                  "account_name": "モックギンコウコウザ1",
                                  "account_status": "active"
                                  },
                                  {
                                  "zone_id": "3001",
                                  "zone_name": "モックギンコウゾーン2",
                                  "balance": 210000,
                                  "account_name": "モックギンコウコウザ2",
                                  "account_status": "active"
                                  }
                                  ]
                                }
                                """)
                )
    }

    static MappingBuilder postCoreCheckForceBurn_success() {
        return post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                )
    }

    static MappingBuilder postCoreForceBurn_success() {
        return post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1",
                                  "balance": "0",
                                  "burn_amount": "10000"
                                }
                                """)
                )
    }

    static MappingBuilder getCoreBalances_success() {
        return get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/balances"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "total_balance": "0",
                                  "items": [
                                    {
                                      "zone_id": "3000",
                                      "zone_name": "モックギンコウゾーン",
                                      "balance": "0",
                                      "account_name":  "モックギンコウコウザ1",
                                      "account_status": "force_burned"
                                    }, {
                                      "zone_id": "3333",
                                      "zone_name": "モックビズゾーン1",
                                      "balance": "0",
                                      "account_name": "モックビズ",
                                      "account_status": "force_burned"
                                    }
                                  ]
                                }
                        """)
                )
    }

    static MappingBuilder postBankGwDeposit_success(String requestId) {
        return post(urlEqualTo("/bank_account/0310-302-02-1234567/deposit?dc_bank_number=DC001-1234-1234-1"))
                .willReturn(
                        okJson("""
                                {
                                    "bankgw_transaction_id": "${requestId}-response",
                                    "operated_at": "${ZonedDateTime.now().format(DateTimeFormatter.ISO_ZONED_DATE_TIME)}",
                                    "bank_account": {
                                        "bank_account_id": "0310-302-02-1234567",
                                        "bank_code": "0310",
                                        "branch_code": "302",
                                        "bank_account_type": "02",
                                        "bank_account_number": "1234567",
                                        "bank_account_name": "モックギンコウコウザ1",
                                        "bank_account_name_kana": "モックギンコウコウザ1"
                                    },
                                    "extra_info": {
                                        "extra_type": "ganb",
                                        "extra_detail": {
                                          "item1" : "value1",
                                          "item2" : "value2"
                                        }
                                    }
                                }
                                """)
                )
    }

    static MappingBuilder getBankGwFetchBalance() {
        return get("/bank_account/0310-302-02-1234567/balance?dc_bank_number=DC001-1234-1234-1")
                .willReturn(
                        okJson("""
                          {
                            "real_balance": "10000",
                            "available_balance": "10001"
                          }
                        """)
                )
    }

    static MappingBuilder postCorePutAccountName_success(String account_id) {
        return put(urlEqualTo("/accounts/${account_id}"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                )
    }

    /* B31-82. 銀行向け アカウント 強制償却 承認/承認受付 */

    def "reviewAccountBurn_1-1_#role が承認できること（DC口座名 と 銀行口座名 が同一の場合）"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService(signInId, "Password1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(postCoreCheckForceBurn_success())
        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(postCoreForceBurn_success())
        // Core 残高取得
        wiremockCore.stubFor(getCoreBalances_success())
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(postBankGwDeposit_success(requestId))
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(getBankGwFetchBalance())

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)
        def serviceUserOrder = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)
        def qrOperation = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = '${signInId}';
        """)
        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("review_result").get("order_id").asInt() == 1001
        responseBody.get("review_result").get("review_status").asText() == "approval"
        responseBody.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody.get("review_result").get("reviewer").get("reviewer_name").asText() == reviewerName
        Objects.isNull(responseBody.get("review_result").get("rejected_reason"))
        !Objects.isNull(responseBody.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody.get("review_result").get("operated_at"))
        Objects.isNull(responseBody.get("qr_token"))
        !Objects.isNull(responseBody.get("account_force_burned_detail"))

        responseBody.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody.get("account_force_burned_detail").get("reason_code").asText() == "SASB0003"
        responseBody.get("account_force_burned_detail").get("reason_title").asText() == "不正利用"
        responseBody.get("account_force_burned_detail").get("reason_detail").asText() == "activate"
        responseBody.get("account_force_burned_detail").get("balance").asText() == "0"
        responseBody.get("account_force_burned_detail").get("bank_account_balance").asText() == "10000"

        // DBデータ確認
        serviceUserOrder.get("order_status") == "approval"
        serviceUserOrder.get("reviewer_sign_in_id") == signInId
        !Objects.isNull(serviceUserOrder.get("reviewed_at"))
        Objects.isNull(serviceUserOrder.get("reason_code"))
        Objects.isNull(serviceUserOrder.get("reason_detail"))

        Objects.isNull(qrOperation)

        where:
        role             | signInId       | reviewerName
        "イシュア管理者" | "SID11AA-1001" | "テストサ－ビスカンリシャＡ"
        "業務承認者"     | "SID11AA-1031" | "テストギョウムショウニンシャＡ１"
    }

    def "reviewAccountBurn_1-1_#role が承認できること（DC口座名 と 銀行口座名 が異なり、口座名同期が正常終了の場合）"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)
        def beforeDcUser = sql.firstRow("SELECT * FROM dc_user WHERE sign_in_id='SID01AA-1001'")
        def beforeBankAccount = sql.firstRow("SELECT * FROM bank_account WHERE dc_bank_number='DC001-1234-1234-1'")

        def accessToken = adhocHttpHelper.signInService(signInId, "Password1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(postCoreCheckForceBurn_success())
        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(postCoreForceBurn_success())
        // Core 口座名義変更
        wiremockCore.stubFor(postCorePutAccountName_success("6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
        // Core 残高取得
        wiremockCore.stubFor(getCoreBalances_success())
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(post(urlEqualTo("/bank_account/0310-302-02-1234567/deposit?dc_bank_number=DC001-1234-1234-1"))
                .willReturn(
                        okJson("""
                                {
                                    "bankgw_transaction_id": "${requestId}-response",
                                    "operated_at": "${ZonedDateTime.now().format(DateTimeFormatter.ISO_ZONED_DATE_TIME)}",
                                    "bank_account": {
                                        "bank_account_id": "0310-302-02-1234567",
                                        "bank_code": "0310",
                                        "branch_code": "302",
                                        "bank_account_type": "02",
                                        "bank_account_number": "1234567",
                                        "bank_account_name": "モックギンコウコウザ1",
                                        "bank_account_name_kana": "コウザメイ ドウキテスト"
                                    },
                                    "extra_info": {
                                        "extra_type": "ganb",
                                        "extra_detail": {
                                          "item1" : "value1",
                                          "item2" : "value2"
                                        }
                                    }
                                }
                                """)
                )
        )
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(getBankGwFetchBalance())

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)
        def serviceUserOrder = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)
        def qrOperation = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = '${signInId}';
        """)
        def afterDcUser = sql.firstRow("SELECT * FROM dc_user WHERE sign_in_id='SID01AA-1001'")
        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1';
        UPDATE dc_user SET user_name='テストアカウント管理者A' WHERE sign_in_id='SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("review_result").get("order_id").asInt() == 1001
        responseBody.get("review_result").get("review_status").asText() == "approval"
        responseBody.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody.get("review_result").get("reviewer").get("reviewer_name").asText() == reviewerName
        Objects.isNull(responseBody.get("review_result").get("rejected_reason"))
        !Objects.isNull(responseBody.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody.get("review_result").get("operated_at"))
        Objects.isNull(responseBody.get("qr_token"))
        !Objects.isNull(responseBody.get("account_force_burned_detail"))

        responseBody.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody.get("account_force_burned_detail").get("reason_code").asText() == "SASB0003"
        responseBody.get("account_force_burned_detail").get("reason_title").asText() == "不正利用"
        responseBody.get("account_force_burned_detail").get("reason_detail").asText() == "activate"
        responseBody.get("account_force_burned_detail").get("balance").asText() == "0"
        responseBody.get("account_force_burned_detail").get("bank_account_balance").asText() == "10000"

        // DBデータ確認
        serviceUserOrder.get("order_status") == "approval"
        serviceUserOrder.get("reviewer_sign_in_id") == signInId
        !Objects.isNull(serviceUserOrder.get("reviewed_at"))
        Objects.isNull(serviceUserOrder.get("reason_code"))
        Objects.isNull(serviceUserOrder.get("reason_detail"))

        // dc_userテーブルの確認
        afterDcUser.get("sign_in_id") == beforeDcUser.get("sign_in_id")
        afterDcUser.get("user_name") == "コウザメイ ドウキテスト"
        afterDcUser.get("dc_user_type") == beforeDcUser.get("dc_user_type")
        afterDcUser.get("user_status") == beforeDcUser.get("user_status")
        afterDcUser.get("service_id") == beforeDcUser.get("service_id")
        afterDcUser.get("role_id") == beforeDcUser.get("role_id")
        afterDcUser.get("registered_at") == beforeDcUser.get("registered_at")
        afterDcUser.get("terminated_at") == beforeDcUser.get("terminated_at")

        // bank_accountテーブルの確認
        def afterBankAccount = sql.firstRow("SELECT * FROM bank_account WHERE dc_bank_number='DC001-1234-1234-1'")
        afterBankAccount.get("dc_bank_number") == beforeBankAccount.get("dc_bank_number")
        afterBankAccount.get("service_id") == beforeBankAccount.get("service_id")
        afterBankAccount.get("bank_account_id") == beforeBankAccount.get("bank_account_id")
        afterBankAccount.get("bank_code") == beforeBankAccount.get("bank_code")
        afterBankAccount.get("bank_name") == beforeBankAccount.get("bank_name")
        afterBankAccount.get("branch_code") == beforeBankAccount.get("branch_code")
        afterBankAccount.get("bank_account_type") == beforeBankAccount.get("bank_account_type")
        afterBankAccount.get("bank_account_number") == beforeBankAccount.get("bank_account_number")
        afterBankAccount.get("bank_account_name") == "コウザメイ ドウキテスト"

        Objects.isNull(qrOperation)

        cleanup:
        sql.execute("""
            UPDATE dc_user SET user_name='テストアカウント管理者A' WHERE sign_in_id='SID01AA-1001';
            UPDATE bank_account SET bank_account_name='モック銀行口座1' WHERE dc_bank_number='DC001-1234-1234-1';
        """)

        where:
        role             | signInId       | reviewerName
        "イシュア管理者" | "SID11AA-1001" | "テストサ－ビスカンリシャＡ"
        "業務承認者"     | "SID11AA-1031" | "テストギョウムショウニンシャＡ１"
    }

    def "reviewAccountBurn_1-1_#role が承認できること（DC口座名 と 銀行口座名 が異なり、口座名同期が #vResponse の場合）"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)
        def beforeDcUser = sql.firstRow("SELECT * FROM dc_user WHERE sign_in_id='SID01AA-1001'")
        def beforeBankAccount = sql.firstRow("SELECT * FROM bank_account WHERE dc_bank_number='DC001-1234-1234-1'")

        def accessToken = adhocHttpHelper.signInService(signInId, "Password1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(postCoreCheckForceBurn_success())
        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(postCoreForceBurn_success())
        // Core 口座名義変更
        wiremockCore.stubFor(put(urlPathEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "${vResponse}"
                                }
                                """)
                ))
        // Core 残高取得
        wiremockCore.stubFor(getCoreBalances_success())
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(post(urlEqualTo("/bank_account/0310-302-02-1234567/deposit?dc_bank_number=DC001-1234-1234-1"))
                .willReturn(
                        okJson("""
                                {
                                    "bankgw_transaction_id": "${requestId}-response",
                                    "operated_at": "${ZonedDateTime.now().format(DateTimeFormatter.ISO_ZONED_DATE_TIME)}",
                                    "bank_account": {
                                        "bank_account_id": "0310-302-02-1234567",
                                        "bank_code": "0310",
                                        "branch_code": "302",
                                        "bank_account_type": "02",
                                        "bank_account_number": "1234567",
                                        "bank_account_name": "モックギンコウコウザ1",
                                        "bank_account_name_kana": "コウザメイ ドウキテスト"
                                    },
                                    "extra_info": {
                                        "extra_type": "ganb",
                                        "extra_detail": {
                                          "item1" : "value1",
                                          "item2" : "value2"
                                        }
                                    }
                                }
                                """)
                )
        )
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(getBankGwFetchBalance())

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)
        def serviceUserOrder = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)
        def qrOperation = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = '${signInId}';
        """)
        def afterDcUser = sql.firstRow("SELECT * FROM dc_user WHERE sign_in_id='SID01AA-1001'")
        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1';
        UPDATE dc_user SET user_name='テストアカウント管理者A' WHERE sign_in_id='SID01AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("review_result").get("order_id").asInt() == 1001
        responseBody.get("review_result").get("review_status").asText() == "approval"
        responseBody.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody.get("review_result").get("reviewer").get("reviewer_name").asText() == reviewerName
        Objects.isNull(responseBody.get("review_result").get("rejected_reason"))
        !Objects.isNull(responseBody.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody.get("review_result").get("operated_at"))
        Objects.isNull(responseBody.get("qr_token"))
        !Objects.isNull(responseBody.get("account_force_burned_detail"))

        responseBody.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody.get("account_force_burned_detail").get("reason_code").asText() == "SASB0003"
        responseBody.get("account_force_burned_detail").get("reason_title").asText() == "不正利用"
        responseBody.get("account_force_burned_detail").get("reason_detail").asText() == "activate"
        responseBody.get("account_force_burned_detail").get("balance").asText() == "0"
        responseBody.get("account_force_burned_detail").get("bank_account_balance").asText() == "10000"

        // DBデータ確認
        serviceUserOrder.get("order_status") == "approval"
        serviceUserOrder.get("reviewer_sign_in_id") == signInId
        !Objects.isNull(serviceUserOrder.get("reviewed_at"))
        Objects.isNull(serviceUserOrder.get("reason_code"))
        Objects.isNull(serviceUserOrder.get("reason_detail"))

        // dc_userテーブルの確認
        afterDcUser.get("sign_in_id") == beforeDcUser.get("sign_in_id")
        afterDcUser.get("user_name") == beforeDcUser.get("user_name")
        afterDcUser.get("dc_user_type") == beforeDcUser.get("dc_user_type")
        afterDcUser.get("user_status") == beforeDcUser.get("user_status")
        afterDcUser.get("service_id") == beforeDcUser.get("service_id")
        afterDcUser.get("role_id") == beforeDcUser.get("role_id")
        afterDcUser.get("registered_at") == beforeDcUser.get("registered_at")
        afterDcUser.get("terminated_at") == beforeDcUser.get("terminated_at")

        // bank_accountテーブルの確認
        def afterBankAccount = sql.firstRow("SELECT * FROM bank_account WHERE dc_bank_number='DC001-1234-1234-1'")
        afterBankAccount.get("dc_bank_number") == beforeBankAccount.get("dc_bank_number")
        afterBankAccount.get("service_id") == beforeBankAccount.get("service_id")
        afterBankAccount.get("bank_account_id") == beforeBankAccount.get("bank_account_id")
        afterBankAccount.get("bank_code") == beforeBankAccount.get("bank_code")
        afterBankAccount.get("bank_name") == beforeBankAccount.get("bank_name")
        afterBankAccount.get("branch_code") == beforeBankAccount.get("branch_code")
        afterBankAccount.get("bank_account_type") == beforeBankAccount.get("bank_account_type")
        afterBankAccount.get("bank_account_number") == beforeBankAccount.get("bank_account_number")
        afterBankAccount.get("bank_account_name") == beforeBankAccount.get("bank_account_name")

        Objects.isNull(qrOperation)

        where:
        role             | signInId       | reviewerName            | vResponse
        "イシュア管理者" | "SID11AA-1001" | "テストサ－ビスカンリシャＡ" | "E0001"
        "イシュア管理者" | "SID11AA-1001" | "テストサ－ビスカンリシャＡ" | "E0012"
        "イシュア管理者" | "SID11AA-1001" | "テストサ－ビスカンリシャＡ" | "E0032"
        "イシュア管理者" | "SID11AA-1001" | "テストサ－ビスカンリシャＡ" | "E0075"
        "業務承認者"     | "SID11AA-1031" | "テストギョウムショウニンシャＡ１"    | "E0001"
        "業務承認者"     | "SID11AA-1031" | "テストギョウムショウニンシャＡ１"    | "E0012"
        "業務承認者"     | "SID11AA-1031" | "テストギョウムショウニンシャＡ１"    | "E0032"
        "業務承認者"     | "SID11AA-1031" | "テストギョウムショウニンシャＡ１"    | "E0075"
    }

    def "reviewAccountBurn_1-2_否認の場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": null
                          }'::jsonb)
            )       
        """)


        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpGet = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "rejected",
                    "rejected_reason": {
                        "reason_code": "SABR0001",
                        "reason_detail": "reject"
                    }
                }
                """)
                .build()

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("review_result").get("order_id").asInt() == 1001
        responseBody.get("review_result").get("review_status").asText() == "rejected"
        responseBody.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody.get("review_result").get("reviewer").get("reviewer_name").asText() == "テストギョウムショウニンシャＡ１"
        !Objects.isNull(responseBody.get("review_result").get("rejected_reason"))
        responseBody.get("review_result").get("rejected_reason").get("operated_reason_type").asText() == "reject_account_force_burned"
        responseBody.get("review_result").get("rejected_reason").get("reason_code").asText() == "SABR0001"
        !Objects.isNull(responseBody.get("review_result").get("rejected_reason").get("reason_title"))
        responseBody.get("review_result").get("rejected_reason").get("reason_detail").asText() == "reject"
        !Objects.isNull(responseBody.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody.get("review_result").get("operated_at"))
        Objects.isNull(responseBody.get("qr_token"))
        Objects.isNull(responseBody.get("account_force_burned_detail"))

        // DBデータ確認
        orderResult.get("order_status") == "rejected"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        orderResult.get("reason_code") == "SABR0001"
        orderResult.get("reason_detail") == "reject"

    }

    def "reviewAccountBurn_1-3_指定した依頼番号が存在しない場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)


        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpGet = adhocHttpHelper.httpPost("/services/orders/9999/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "rejected",
                    "rejected_reason": {
                        "reason_code": "SABR0001",
                        "reason_detail": "reject"
                    }
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        // DBデータ確認
        Objects.isNull(orderResult)
        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-4_対象の依頼が完了している場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'approval', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": null
                          }'::jsonb)
            )       
        """)


        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE3000"

        // DBデータ確認
        !Objects.isNull(orderResult)
        orderResult.get("order_status") == "approval"
        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-5_対象の依頼が処理中の場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'in_approving', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": null
                          }'::jsonb)
            )       
        """)


        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EGO0001"

        // DBデータ確認
        !Objects.isNull(orderResult)
        orderResult.get("order_status") == "in_approving"
        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-6_CoreのAPI(FinZoneコイン強制償却前確認)で、アカウントが存在しない場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": null
                          }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0001"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        // DBデータ確認
        orderResult.get("order_status") == "rejected"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        orderResult.get("reason_code") == "SSYR0001"
        Objects.isNull(orderResult.get("reason_detail"))
        orderResult.get("service_order_type") == "account_force_burned"
        !Objects.isNull(orderResult.get("order_detail"))
        orderResult.get("error_code") == "EGE0100"

        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-7_CoreのAPI(FinZoneコイン強制償却前確認)で、アカウントが凍結中でない場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "reason_code": "SASB0003",
                                 "reason_detail": "activate",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_title": "原因解消"
                               }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0032"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE2040"

        // DBデータ確認
        orderResult.get("order_status") == "rejected"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        orderResult.get("reason_code") == "SSYR0001"
        Objects.isNull(orderResult.get("reason_detail"))
        orderResult.get("service_order_type") == "account_force_burned"
        !Objects.isNull(orderResult.get("order_detail"))
        orderResult.get("error_code") == "EUE2040"

        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-8_CoreのAPI(FinZoneコイン強制償却前確認)で、アカウントに残高が存在しない場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "reason_code": "SASB0003",
                                 "reason_detail": "activate",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_title": "原因解消"
                               }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0005"
                                }
                                """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE4401"

        // DBデータ確認
        orderResult.get("order_status") == "rejected"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        orderResult.get("reason_code") == "SSYR0001"
        Objects.isNull(orderResult.get("reason_detail"))
        orderResult.get("service_order_type") == "account_force_burned"
        !Objects.isNull(orderResult.get("order_detail"))
        orderResult.get("error_code") == "EUE4401"

        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-9_CoreのAPI(FinZoneコイン強制償却)で、アカウントが存在しない場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "reason_code": "SASB0003",
                                 "reason_detail": "activate",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_title": "原因解消"
                               }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                ))

        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0001"
                                }
                                """)
                ))

        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(get("/bank_account/0310-302-02-1234567/balance?dc_bank_number=DC001-1234-1234-1")
                .willReturn(
                        okJson("""
                          {
                            "real_balance": "10000",
                            "available_balance": "10001"
                          }
                        """)
                ))


        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        // DBデータ確認
        orderResult.get("order_status") == "rejected"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        orderResult.get("reason_code") == "SSYR0001"
        Objects.isNull(orderResult.get("reason_detail"))
        orderResult.get("service_order_type") == "account_force_burned"
        !Objects.isNull(orderResult.get("order_detail"))
        orderResult.get("error_code") == "EGE0100"

        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-10_CoreのAPI(FinZoneコイン強制償却)で、アカウントが凍結中でない場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "reason_code": "SASB0003",
                                 "reason_detail": "activate",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_title": "原因解消"
                               }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                ))

        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0032"
                                }
                                """)
                ))

        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(get("/bank_account/0310-302-02-1234567/balance?dc_bank_number=DC001-1234-1234-1")
                .willReturn(
                        okJson("""
                          {
                            "real_balance": "10000",
                            "available_balance": "10001"
                          }
                        """)
                ))


        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE2040"

        // DBデータ確認
        orderResult.get("order_status") == "rejected"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        orderResult.get("reason_code") == "SSYR0001"
        Objects.isNull(orderResult.get("reason_detail"))
        orderResult.get("service_order_type") == "account_force_burned"
        !Objects.isNull(orderResult.get("order_detail"))
        orderResult.get("error_code") == "EUE2040"

        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-11_CoreのAPI(FinZoneコイン強制償却)で、アカウントの残高が存在しない場合"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "reason_code": "SASB0003",
                                 "reason_detail": "activate",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_title": "原因解消"
                               }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                ))

        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn"))
                .willReturn(
                        badRequest()
                                .withHeader("Content-Type", "application/json")
                                .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "E0005"
                                }
                                """)
                ))

        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(get("/bank_account/0310-302-02-1234567/balance?dc_bank_number=DC001-1234-1234-1")
                .willReturn(
                        okJson("""
                          {
                            "real_balance": "10000",
                            "available_balance": "10001"
                          }
                        """)
                ))


        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        then:
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EUE4401"

        // DBデータ確認
        orderResult.get("order_status") == "rejected"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        orderResult.get("reason_code") == "SSYR0001"
        Objects.isNull(orderResult.get("reason_detail"))
        orderResult.get("service_order_type") == "account_force_burned"
        !Objects.isNull(orderResult.get("order_detail"))
        orderResult.get("error_code") == "EUE4401"

        Objects.isNull(qrResult)
    }

    def "reviewAccountBurn_1-12_BankGw(BG-22_銀行預金口座入金) が #vResponse の場合正常終了し、現在の残高は取得できないこと"() {
        setup:

        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)

        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": null
                          }'::jsonb)
            )       
        """)


        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                ))

        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1",
                                  "balance": "0",
                                  "burn_amount": "10000"
                                }
                                """)
                ))

        // Core 口座名義変更
        wiremockCore.stubFor(put(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                ))

        // Core 残高取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/balances"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "total_balance": "0",
                                  "items": [
                                    {
                                      "zone_id": "3000",
                                      "zone_name": "モックギンコウゾーン",
                                      "balance": "0",
                                      "account_name":  "モックギンコウコウザ1",
                                      "account_status": "force_burned"
                                    }, {
                                      "zone_id": "3333",
                                      "zone_name": "モックビズゾーン1",
                                      "balance": "0",
                                      "account_name": "モックビズ",
                                      "account_status": "force_burned"
                                    }
                                  ]
                                }
                        """)
                ))
        def requestId = UUID.randomUUID().toString()
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(post(urlEqualTo("/bank_account/0310-302-02-1234567/deposit?dc_bank_number=DC001-1234-1234-1"))
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "error_code": "${vResponse}",
                                    "message": "....."
                                }
                                """)
                )
        )

        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(get("/bank_account/0310-302-02-1234567/balance?dc_bank_number=DC001-1234-1234-1")
                .willReturn(
                        okJson("""
                          {
                            "real_balance": "10000",
                            "available_balance": "10001"
                          }
                        """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1';
        """)


        then:
        response.getStatusLine().getStatusCode() == httpStatus
        responseBody.get("review_result").get("order_id").asInt() == 1001
        responseBody.get("review_result").get("review_status").asText() == "approval"
        responseBody.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody.get("review_result").get("reviewer").get("reviewer_name").asText() == "テストギョウムショウニンシャＡ１"
        Objects.isNull(responseBody.get("review_result").get("rejected_reason"))
        !responseBody.get("review_result").get("ordered_at").isNull()
        !responseBody.get("review_result").get("operated_at").isNull()

        Objects.isNull(responseBody.get("qr_token"))

        !Objects.isNull(responseBody.get("account_force_burned_detail"))
        responseBody.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody.get("account_force_burned_detail").get("reason_code").asText() == "SASB0003"
        responseBody.get("account_force_burned_detail").get("reason_title").asText() == "不正利用"
        Objects.isNull(responseBody.get("account_force_burned_detail").get("reason_detail"))
        Objects.isNull(responseBody.get("account_force_burned_detail").get("balance"))
        Objects.isNull(responseBody.get("account_force_burned_detail").get("bank_account_balance"))

        // DBデータ確認
        orderResult.get("order_status") == "approval"
        orderResult.get("reviewer_sign_in_id") == "SID11AA-1031"
        !Objects.isNull(orderResult.get("reviewed_at"))
        Objects.isNull(orderResult.get("reason_code"))
        Objects.isNull(orderResult.get("reason_detail"))

        Objects.isNull(qrResult)

        where:
        vResponse            | httpStatus
        "out_of_service"     | 200
        "account_disabled"   | 200
        "unexpected_account" | 200
        "failed_operation"   | 200
    }

    def "reviewAccountBurn_1-13_残高、限度額が16桁の場合、15桁にトリムされた情報を取得ができること"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)

        def accessToken = adhocHttpHelper.signInService(signInId, "Password1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(postCoreCheckForceBurn_success())
        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(postCoreForceBurn_success())
        // Core 残高取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/balances"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "total_balance": "****************",
                                  "items": [
                                    {
                                      "zone_id": "3000",
                                      "zone_name": "モックギンコウゾーン",
                                      "balance": "****************",
                                      "account_name":  "モックギンコウコウザ1",
                                      "account_status": "force_burned"
                                    }, {
                                      "zone_id": "3333",
                                      "zone_name": "モックビズゾーン1",
                                      "balance": "****************",
                                      "account_name": "モックビズ",
                                      "account_status": "force_burned"
                                    }
                                  ]
                                }
                        """)
                ))
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(postBankGwDeposit_success(requestId))
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(get("/bank_account/0310-302-02-1234567/balance?dc_bank_number=DC001-1234-1234-1")
                .willReturn(
                        okJson("""
                          {
                            "real_balance": "****************",
                            "available_balance": "****************"
                          }
                        """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)
        def serviceUserOrder = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)
        def qrOperation = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = '${signInId}';
        """)
        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1';
        """)

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("review_result").get("order_id").asInt() == 1001
        responseBody.get("review_result").get("review_status").asText() == "approval"
        responseBody.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody.get("review_result").get("reviewer").get("reviewer_name").asText() == reviewerName
        Objects.isNull(responseBody.get("review_result").get("rejected_reason"))
        !Objects.isNull(responseBody.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody.get("review_result").get("operated_at"))
        Objects.isNull(responseBody.get("qr_token"))
        !Objects.isNull(responseBody.get("account_force_burned_detail"))

        responseBody.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody.get("account_force_burned_detail").get("reason_code").asText() == "SASB0003"
        responseBody.get("account_force_burned_detail").get("reason_title").asText() == "不正利用"
        responseBody.get("account_force_burned_detail").get("reason_detail").asText() == "activate"
        responseBody.get("account_force_burned_detail").get("balance").asText() == "***************"
        responseBody.get("account_force_burned_detail").get("bank_account_balance").asText() == "***************"

        // DBデータ確認
        serviceUserOrder.get("order_status") == "approval"
        serviceUserOrder.get("reviewer_sign_in_id") == signInId
        !Objects.isNull(serviceUserOrder.get("reviewed_at"))
        Objects.isNull(serviceUserOrder.get("reason_code"))
        Objects.isNull(serviceUserOrder.get("reason_detail"))

        Objects.isNull(qrOperation)

        where:
        role             | signInId       | reviewerName
        "イシュア管理者" | "SID11AA-1001" | "テストサ－ビスカンリシャＡ"
        "業務承認者"     | "SID11AA-1031" | "テストギョウムショウニンシャＡ１"
    }

    def "reviewAccountBurn_1-14_Core の 11-71_FinZoneコイン強制償却前確認 が #vResponse の場合、承認待ちに戻ること"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0001",
                                 "reason_title": "端末紛失",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)

        def beforeServiceUserOrder = sql.firstRow("""
            SELECT
                *
            FROM
                service_user_order
            WHERE
                order_id = 1001
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(postCoreForceBurn_success())
        // Core 残高取得
        wiremockCore.stubFor(getCoreBalances_success())
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(postBankGwDeposit_success(requestId))
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(getBankGwFetchBalance())

        // Core の 11-71_FinZoneコイン強制償却前確認 のMock
        def scenarioName = "reviewAccountBurn_checkBurn"

        // 1回目に呼び出すMock
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .inScenario(scenarioName)
                .whenScenarioStateIs(Scenario.STARTED)
                .willSetStateTo("SecondCall")
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "${vResponse}"
                                }
                                """)
                )
        )

        // 2回目以降に呼び出すMock
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn/check"))
                .inScenario(scenarioName)
                .whenScenarioStateIs("SecondCall")

                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1"
                                }
                                """)
                )
        )

        when:
        // 1回目
        def response1 = httpClient.execute(httpPost)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def afterServiceUserOrder1 = sql.firstRow("""
            SELECT
                *
            FROM
                service_user_order
            WHERE
                order_id = 1001
        """)

        // 2回目
        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        def afterServiceUserOrder2 = sql.firstRow("""
            SELECT
                *
            FROM
                service_user_order
            WHERE
                order_id = 1001
        """)

        then:
        /* 1回目(エラー)の確認 */
        // レスポンスの確認
        response1.getStatusLine().getStatusCode() == httpStatus
        responseBody1.get("error_code").asText() == errorCode

        // DB のレコード確認
        afterServiceUserOrder1.get("service_order_type") == beforeServiceUserOrder.get("service_order_type")
        afterServiceUserOrder1.get("order_status") == beforeServiceUserOrder.get("order_status")
        afterServiceUserOrder1.get("order_sign_in_id") == beforeServiceUserOrder.get("order_sign_in_id")
        afterServiceUserOrder1.get("reviewer_sign_in_id") == beforeServiceUserOrder.get("reviewer_sign_in_id")
        afterServiceUserOrder1.get("reviewed_at") == beforeServiceUserOrder.get("reviewed_at")
        afterServiceUserOrder1.get("reason_code") == beforeServiceUserOrder.get("reason_code")
        afterServiceUserOrder1.get("reason_detail") == beforeServiceUserOrder.get("reason_detail")
        afterServiceUserOrder1.get("error_code") == beforeServiceUserOrder.get("error_code")

        /* 2回目の確認 */
        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("review_result").get("order_id").asInt() == 1001
        responseBody2.get("review_result").get("review_status").asText() == "approval"
        responseBody2.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody2.get("review_result").get("reviewer").get("reviewer_name").asText() == "テストサ－ビスカンリシャＡ"
        Objects.isNull(responseBody2.get("review_result").get("rejected_reason"))
        !Objects.isNull(responseBody2.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody2.get("review_result").get("operated_at"))
        Objects.isNull(responseBody2.get("qr_token"))
        !Objects.isNull(responseBody2.get("account_force_burned_detail"))

        responseBody2.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody2.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody2.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody2.get("account_force_burned_detail").get("reason_code").asText() == "SASB0001"
        responseBody2.get("account_force_burned_detail").get("reason_title").asText() == "端末紛失"
        responseBody2.get("account_force_burned_detail").get("reason_detail").asText() == "activate"
        responseBody2.get("account_force_burned_detail").get("balance").asText() == "0"
        responseBody2.get("account_force_burned_detail").get("bank_account_balance").asText() == "10000"

        // DBデータ確認
        afterServiceUserOrder2.get("order_status") == "approval"
        afterServiceUserOrder2.get("reviewer_sign_in_id") == "SID11AA-1001"
        !Objects.isNull(afterServiceUserOrder2.get("reviewed_at"))
        Objects.isNull(afterServiceUserOrder2.get("reason_code"))
        Objects.isNull(afterServiceUserOrder2.get("reason_detail"))

        cleanup:
        sql.execute("DELETE FROM service_user_order")

        where:
        vResponse | httpStatus | errorCode
        "E0014"   | 500        | "EEE0001"
    }

    def "reviewAccountBurn_1-15_Core の 11-72_FinZoneコイン強制償却 が #vResponse の場合、承認待ちに戻ること"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0001",
                                 "reason_title": "端末紛失",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)
        def beforeServiceUserOrder = sql.firstRow("""
            SELECT
                *
            FROM
                service_user_order
            WHERE
                order_id = 1001
        """)

        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(postCoreCheckForceBurn_success())
        // Core 残高取得
        wiremockCore.stubFor(getCoreBalances_success())
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(postBankGwDeposit_success(requestId))
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(getBankGwFetchBalance())

        // Core の 11-72_FinZoneコイン強制償却 のMock
        def scenarioName = "reviewAccountBurn_burn"

        // 1回目に呼び出すMock
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn"))
                .inScenario(scenarioName)
                .whenScenarioStateIs(Scenario.STARTED)
                .willSetStateTo("SecondCall")
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "${vResponse}"
                                }
                                """)
                )
        )

        // 2回目以降に呼び出すMock
        wiremockCore.stubFor(post(urlEqualTo("/accounts/6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi/force_burn"))
                .inScenario(scenarioName)
                .whenScenarioStateIs("SecondCall")
                .willSetStateTo(Scenario.STARTED)
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                  "account_name": "モックギンコウコウザ1",
                                  "balance": "0",
                                  "burn_amount": "10000"
                                }
                                """)
                )
        )

        when:
        // 1回目
        def response1 = httpClient.execute(httpPost)
        def responseBody1 = adhocHttpHelper.toJson(response1)

        def afterServiceUserOrder1 = sql.firstRow("""
            SELECT
                *
            FROM
                service_user_order
            WHERE
                order_id = 1001
        """)

        // 2回目
        def response2 = httpClient.execute(httpPost)
        def responseBody2 = adhocHttpHelper.toJson(response2)

        def afterServiceUserOrder2 = sql.firstRow("""
            SELECT
                *
            FROM
                service_user_order
            WHERE
                order_id = 1001
        """)

        then:
        /* 1回目(エラー)の確認 */
        // レスポンスの確認
        response1.getStatusLine().getStatusCode() == httpStatus
        responseBody1.get("error_code").asText() == errorCode

        // DB のレコード確認
        afterServiceUserOrder1.get("service_order_type") == beforeServiceUserOrder.get("service_order_type")
        afterServiceUserOrder1.get("order_status") == beforeServiceUserOrder.get("order_status")
        afterServiceUserOrder1.get("order_sign_in_id") == beforeServiceUserOrder.get("order_sign_in_id")
        afterServiceUserOrder1.get("reviewer_sign_in_id") == beforeServiceUserOrder.get("reviewer_sign_in_id")
        afterServiceUserOrder1.get("reviewed_at") == beforeServiceUserOrder.get("reviewed_at")
        afterServiceUserOrder1.get("reason_code") == beforeServiceUserOrder.get("reason_code")
        afterServiceUserOrder1.get("reason_detail") == beforeServiceUserOrder.get("reason_detail")
        afterServiceUserOrder1.get("error_code") == beforeServiceUserOrder.get("error_code")

        /* 2回目の確認 */
        response2.getStatusLine().getStatusCode() == 200
        responseBody2.get("review_result").get("order_id").asInt() == 1001
        responseBody2.get("review_result").get("review_status").asText() == "approval"
        responseBody2.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody2.get("review_result").get("reviewer").get("reviewer_name").asText() == "テストサ－ビスカンリシャＡ"
        Objects.isNull(responseBody2.get("review_result").get("rejected_reason"))
        !Objects.isNull(responseBody2.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody2.get("review_result").get("operated_at"))
        Objects.isNull(responseBody2.get("qr_token"))
        !Objects.isNull(responseBody2.get("account_force_burned_detail"))

        responseBody2.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-1234-1234-1"
        responseBody2.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ１"
        responseBody2.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody2.get("account_force_burned_detail").get("reason_code").asText() == "SASB0001"
        responseBody2.get("account_force_burned_detail").get("reason_title").asText() == "端末紛失"
        responseBody2.get("account_force_burned_detail").get("reason_detail").asText() == "activate"
        responseBody2.get("account_force_burned_detail").get("balance").asText() == "0"
        responseBody2.get("account_force_burned_detail").get("bank_account_balance").asText() == "10000"

        // DBデータ確認
        afterServiceUserOrder2.get("order_status") == "approval"
        afterServiceUserOrder2.get("reviewer_sign_in_id") == "SID11AA-1001"
        !Objects.isNull(afterServiceUserOrder2.get("reviewed_at"))
        Objects.isNull(afterServiceUserOrder2.get("reason_code"))
        Objects.isNull(afterServiceUserOrder2.get("reason_detail"))

        cleanup:
        sql.execute("DELETE FROM service_user_order")

        where:
        vResponse | httpStatus | errorCode
        "E0014"   | 500        | "EEE0001"
        "E0075"   | 500        | "EEE0001"
    }

    def "reviewAccountBurn_1-16_IB接続状態が切れている場合、実行できないこと"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-1234-1234-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AA-1001', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-1234-1234-1",
                                 "account_name": "モックギンコウコウザ1",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)

        sql.execute("UPDATE dc_account_bank_linkage SET bank_link_status = 'need_auth' WHERE dc_bank_number = 'DC001-1234-1234-1'")

        def accessToken = adhocHttpHelper.signInService("SID11AA-1001", "Password1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(postCoreCheckForceBurn_success())
        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(postCoreForceBurn_success())
        // Core 残高取得
        wiremockCore.stubFor(getCoreBalances_success())
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(postBankGwDeposit_success(requestId))
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(getBankGwFetchBalance())

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)
        def serviceUserOrder = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        """)
        def qrOperation = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1001';
        """)
        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AA-1001';
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-1234-1234-1';
        """)

        then:
        // レスポンスの確認
        response.getStatusLine().getStatusCode() == 400
        responseBody.get("error_code").asText() == "EXW0040"

        // DBデータ確認
        serviceUserOrder.get("order_status") == "rejected"
        serviceUserOrder.get("order_sign_in_id") == "SID11AA-1001"
        serviceUserOrder.get("order_detail") != null
        serviceUserOrder.get("reviewer_sign_in_id") == "SID11AA-1001"
        serviceUserOrder.get("reviewed_at") != null
        serviceUserOrder.get("reason_code") == "SSYR0001"
        serviceUserOrder.get("reason_detail") == null
        serviceUserOrder.get("error_code") == "EXW0040"

        Objects.isNull(qrOperation)

        cleanup:
        sql.execute("UPDATE dc_account_bank_linkage SET bank_link_status = 'authenticated', expires_at = CURRENT_TIMESTAMP + INTERVAL '30 minute' WHERE dc_bank_number = 'DC001-1234-1234-1'")
    }

    def "reviewAccountBurn_1-17_別テナントの #role が自身の配下の依頼を承認できること"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-3333-0103-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AB-3021', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-3333-0103-1",
                                 "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)

        def beforeDcUser = sql.firstRow("SELECT * FROM dc_user WHERE sign_in_id='SID01AC-3001'")
        def beforeBankAccount = sql.firstRow("SELECT * FROM bank_account WHERE dc_bank_number='DC001-3333-0103-1'")

        def accessToken = adhocHttpHelper.signInService(signInId, "Password1", "0d367be20aed7707d71dce4dbd8825f1")
        def requestId = UUID.randomUUID().toString()
        def httpPost = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned")
                .setServiceId("0d367be20aed7707d71dce4dbd8825f1")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        // Core FinZoneコイン強制償却前確認
        wiremockCore.stubFor(post(urlEqualTo("/accounts/601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5/force_burn/check"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3"
                                }
                                """)
                ))
        // Core FinZoneコイン強制償却
        wiremockCore.stubFor(post(urlEqualTo("/accounts/601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5/force_burn"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5",
                                  "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3",
                                  "balance": "0",
                                  "burn_amount": "10000"
                                }
                                """)
                ))
        // Core 口座名義変更
        wiremockCore.stubFor(postCorePutAccountName_success("601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5"))
        // Core 残高取得
        wiremockCore.stubFor(get(urlEqualTo("/accounts/601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5/balances"))
                .willReturn(
                        okJson("""
                                {
                                  "account_id": "601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5",
                                  "total_balance": "0",
                                  "items": [
                                    {
                                      "zone_id": "3000",
                                      "zone_name": "モックギンコウゾーン",
                                      "balance": "0",
                                      "account_name":  "モックギンコウコウザ1",
                                      "account_status": "force_burned"
                                    }, {
                                      "zone_id": "3333",
                                      "zone_name": "モックビズゾーン1",
                                      "balance": "0",
                                      "account_name": "モックビズ",
                                      "account_status": "force_burned"
                                    }
                                  ]
                                }
                        """)
                ))
        // BankGW 銀行預金口座入金
        wiremockBankGw.stubFor(post(urlEqualTo("/bank_account/0310-302-02-6634567/deposit?dc_bank_number=DC001-3333-0103-1"))
                .willReturn(
                        okJson("""
                                {
                                    "bankgw_transaction_id": "${requestId}-response",
                                    "operated_at": "${ZonedDateTime.now().format(DateTimeFormatter.ISO_ZONED_DATE_TIME)}",
                                    "bank_account": {
                                        "bank_account_id": "0310-302-02-6634567",
                                        "bank_code": "0310",
                                        "branch_code": "302",
                                        "bank_account_type": "02",
                                        "bank_account_number": "6634567",
                                        "bank_account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3",
                                        "bank_account_name_kana": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3"
                                    },
                                    "extra_info": {
                                        "extra_type": "ganb",
                                        "extra_detail": {
                                          "item1" : "value1",
                                          "item2" : "value2"
                                        }
                                    }
                                }
                                """)
                )
        )
        // BankGW 銀行預金口座残高取得
        wiremockBankGw.stubFor(get("/bank_account/0310-302-02-6634567/balance?dc_bank_number=DC001-3333-0103-1")
                .willReturn(
                        okJson("""
                          {
                            "real_balance": "10000",
                            "available_balance": "10001"
                          }
                        """)
                ))

        when:
        def response = httpClient.execute(httpPost)
        def responseBody = adhocHttpHelper.toJson(response)
        def serviceUserOrder = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AB-3021';
        """)
        def qrOperation = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = '${signInId}';
        """)
        def afterDcUser = sql.firstRow("SELECT * FROM dc_user WHERE sign_in_id='SID01AC-3001'")

        then:
        response.getStatusLine().getStatusCode() == 200
        responseBody.get("review_result").get("order_id").asInt() == 1001
        responseBody.get("review_result").get("review_status").asText() == "approval"
        responseBody.get("review_result").get("order_type").asText() == "account_force_burned"
        responseBody.get("review_result").get("reviewer").get("reviewer_name").asText() == reviewerName
        Objects.isNull(responseBody.get("review_result").get("rejected_reason"))
        !Objects.isNull(responseBody.get("review_result").get("ordered_at"))
        !Objects.isNull(responseBody.get("review_result").get("operated_at"))
        Objects.isNull(responseBody.get("qr_token"))
        !Objects.isNull(responseBody.get("account_force_burned_detail"))

        responseBody.get("account_force_burned_detail").get("dc_bank_number").asText() == "DC001-3333-0103-1"
        responseBody.get("account_force_burned_detail").get("account_name").asText() == "モックギンコウコウザ３"
        responseBody.get("account_force_burned_detail").get("operated_reason_type").asText() == "account_force_burned"
        responseBody.get("account_force_burned_detail").get("reason_code").asText() == "SASB0003"
        responseBody.get("account_force_burned_detail").get("reason_title").asText() == "不正利用"
        responseBody.get("account_force_burned_detail").get("reason_detail").asText() == "activate"
        responseBody.get("account_force_burned_detail").get("balance").asText() == "0"
        responseBody.get("account_force_burned_detail").get("bank_account_balance").asText() == "10000"

        // DBデータ確認
        serviceUserOrder.get("order_status") == "approval"
        serviceUserOrder.get("reviewer_sign_in_id") == signInId
        !Objects.isNull(serviceUserOrder.get("reviewed_at"))
        Objects.isNull(serviceUserOrder.get("reason_code"))
        Objects.isNull(serviceUserOrder.get("reason_detail"))

        // dc_userテーブルの確認
        afterDcUser.get("sign_in_id") == beforeDcUser.get("sign_in_id")
        afterDcUser.get("user_name") == "ﾃｽﾄｱｶｳﾝﾄｶﾝﾘｼｬC"
        afterDcUser.get("dc_user_type") == beforeDcUser.get("dc_user_type")
        afterDcUser.get("user_status") == beforeDcUser.get("user_status")
        afterDcUser.get("service_id") == beforeDcUser.get("service_id")
        afterDcUser.get("role_id") == beforeDcUser.get("role_id")
        afterDcUser.get("registered_at") == beforeDcUser.get("registered_at")
        afterDcUser.get("terminated_at") == beforeDcUser.get("terminated_at")

        // bank_accountテーブルの確認
        def afterBankAccount = sql.firstRow("SELECT * FROM bank_account WHERE dc_bank_number='DC001-3333-0103-1'")
        afterBankAccount.get("dc_bank_number") == beforeBankAccount.get("dc_bank_number")
        afterBankAccount.get("service_id") == beforeBankAccount.get("service_id")
        afterBankAccount.get("bank_account_id") == beforeBankAccount.get("bank_account_id")
        afterBankAccount.get("bank_code") == beforeBankAccount.get("bank_code")
        afterBankAccount.get("bank_name") == beforeBankAccount.get("bank_name")
        afterBankAccount.get("branch_code") == beforeBankAccount.get("branch_code")
        afterBankAccount.get("bank_account_type") == beforeBankAccount.get("bank_account_type")
        afterBankAccount.get("bank_account_number") == beforeBankAccount.get("bank_account_number")
        afterBankAccount.get("bank_account_name") == "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3"

        Objects.isNull(qrOperation)

        cleanup:
        sql.execute("""
        DELETE FROM service_user_order WHERE order_sign_in_id = 'SID11AB-3021';
        DELETE FROM dc_account_reason WHERE dc_bank_number = 'DC001-3333-0103-1';
        """)
        sql.execute("""
            UPDATE dc_user SET user_name='ﾃｽﾄｱｶｳﾝﾄｶﾝﾘｼｬC' WHERE sign_in_id='SID01AC-3001';
            UPDATE bank_account SET bank_account_name='ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3' WHERE dc_bank_number='DC001-3333-0103-1';
        """)

        where:
        role             | signInId       | reviewerName
        "イシュア管理者" | "SID11AB-3001" | "テストサ－ビスカンリシャＣ"
        "業務承認者"     | "SID11AB-3031" | "テストギョウムショウニンシャＣ"
    }

    def "reviewAccountBurn_1-18_別テナントの依頼を指定した場合、業務エラーになること"() {
        setup:
        //テストの連続実行に備えてシーケンスを更新する。
        sql.execute("""
             SELECT SETVAL('service_user_order_order_id_seq', 1000, TRUE)
        """)
        sql.execute(""" 
            INSERT INTO service_user_order (dc_bank_number, ordered_at, service_order_type, order_status, order_sign_in_id, order_detail)
            VALUES ('DC001-3333-0103-1', '2024-01-13 07:17:02.823', 'account_force_burned', 'pending', 'SID11AB-3021', 
                TO_JSONB( '{
                                 "dc_bank_number": "DC001-3333-0103-1",
                                 "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3",
                                 "operation_type": "ACCOUNT_FORCE_BURNED",
                                 "reason_code": "SASB0003",
                                 "reason_title": "不正利用",
                                 "reason_detail": "activate"
                          }'::jsonb)
            )       
        """)
        
        def accessToken = adhocHttpHelper.signInService("SID11AA-1031", "Password1")
        def httpGet = adhocHttpHelper.httpPost("/services/orders/1001/account_force_burned/")
                .setToken(accessToken)
                .setBody("""
                {
                    "review_status": "approval"
                }
                """)
                .build()

        when:
        def response = httpClient.execute(httpGet)
        def responseBody = adhocHttpHelper.toJson(response)

        def orderResult = sql.firstRow("""
        SELECT * FROM service_user_order WHERE order_sign_in_id = 'SID11AB-3021';
        """)

        def qrResult = sql.firstRow("""
        SELECT * FROM qr_operation WHERE sign_in_id = 'SID11AA-1031';
        """)

        then:
        response.getStatusLine().getStatusCode() == 404
        responseBody.get("error_code").asText() == "EGE0100"

        // DBデータ確認
        orderResult.get("order_id") == 1001
        orderResult.get("dc_bank_number") == "DC001-3333-0103-1"
        !Objects.isNull(orderResult.get("ordered_at"))
        orderResult.get("service_order_type") == "account_force_burned"
        orderResult.get("order_status") == "pending"
        orderResult.get("order_sign_in_Id") == "SID11AB-3021"

        def orderDetail = mapper.readTree(orderResult.get("order_detail").toString())
        orderDetail.get("dc_bank_number").asText() == "DC001-3333-0103-1"
        orderDetail.get("account_name").asText() == "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ3"
        orderDetail.get("operation_type").asText() == "ACCOUNT_FORCE_BURNED"
        orderDetail.get("reason_code").asText() == "SASB0003"
        orderDetail.get("reason_title").asText() == "不正利用"
        orderDetail.get("reason_detail").asText() == "activate"

        Objects.isNull(qrResult)
    }
}
