package com.decurret_dcp.dcjpy.core.application.token_spec

import com.decurret_dcp.dcjpy.core.adaptor.infrastructure.dynamodb.DynamodbBalanceCacheRepository
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService
import com.decurret_dcp.dcjpy.core.application.token_spec.command.MintServiceCommand
import com.decurret_dcp.dcjpy.core.domain.model.TraceId
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainTransactionRevertedException
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.CheckMint
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerHasAccount
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.CheckMintCommand
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.MintCommand
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendResponse
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId
import com.decurret_dcp.dcjpy.core.message.MessageCode
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime

class MintServiceSpec extends Specification {

    SendBlockchainContractService mockSendBlockchainContractService = Mock()
    CallBlockchainContractService mockCallBlockchainContractService = Mock()
    IssuerGetAccount mockIssuerGetAccount = Mock()
    IssuerHasAccount mockIssuerHasAccount = Mock()
    CheckMint mockCheckMint = Mock()
    DynamodbBalanceCacheRepository mockDynamodbBalanceCacheRepository = Mock()

    @Shared
    ZoneIdThreadLocalHolder zoneIdThreadLocalHolder = new ZoneIdThreadLocalHolder()

    @Shared
    TraceIdThreadLocalHolder traceIdThreadLocalHolder = new TraceIdThreadLocalHolder()

    def sut = new MintService(
            mockSendBlockchainContractService,
            mockCallBlockchainContractService,
            mockIssuerGetAccount,
            mockIssuerHasAccount,
            mockCheckMint,
            mockDynamodbBalanceCacheRepository
    )

    def "コントラクトのcheckMintを呼び出す"() {

        setup:
        def command = MintServiceCommand.builder()
                .issuerId(vIssuerId)
                .accountId(vAccountId)
                .mintAmount(vAmount)
                .build()

        zoneIdThreadLocalHolder.setZoneId(ZoneId.of(3000))
        def getAccountResult = IssuerGetAccountResult.builder().accountName("name").build()

        when:
        def response = sut.check(command)

        then:
        1 * mockIssuerGetAccount.execute(vIssuerId, vAccountId, mockCallBlockchainContractService) >> getAccountResult
        1 * mockCheckMint.execute(new CheckMintCommand(vIssuerId, vAccountId, vAmount), new EntityId(vIssuerId), mockCallBlockchainContractService)

        verifyAll(response) {
            issuerId == vIssuerId
            accountId == vAccountId
            mintAmount == vAmount
            accountName == "name"
        }

        zoneIdThreadLocalHolder.remove()

        where:
        vIssuerId         | vAccountId         | vAmount
        new IssuerId("1") | new AccountId("2") | new Amount(new BigInteger("3"))

    }

    def "コントラクトのmintをIssuerの署名で呼び出す"() {
        setup:
        def reqTransactionHash = "0x98765"
        def zoneId = ZoneId.of(3000)
        def command = MintServiceCommand.builder()
                .issuerId(vIssuerId)
                .accountId(vAccountId)
                .mintAmount(vAmount)
                .build()

        zoneIdThreadLocalHolder.setZoneId(ZoneId.of(3000))

        when:
        def response = sut.execute(command)

        then:
        1 * mockIssuerHasAccount.execute(vIssuerId, vAccountId,
                _ as IssuerHasAccount.RequestSwitch, mockCallBlockchainContractService)
        1 * mockCheckMint.execute(
                new CheckMintCommand(vIssuerId, vAccountId, vAmount),
                new EntityId(vIssuerId),
                mockCallBlockchainContractService)
        1 * mockSendBlockchainContractService.executeWithoutSignature(new MintCommand(vIssuerId, vAccountId, vAmount))
                >> new BlockchainSendResponse(true, null, reqTransactionHash)
        1 * mockDynamodbBalanceCacheRepository.addBalanceCache(_, _, _) >> BalanceCache.builder()
                .accountId(vAccountId).zoneId(zoneId).balance(vBalance).updatedAt(vUpdatedAt).build()

        verifyAll(response) {
            issuerId == vIssuerId
            accountId == vAccountId
            mintAmount == vAmount
            cacheBalance == vBalance
        }

        zoneIdThreadLocalHolder.remove()

        where:
        vIssuerId         | vAccountId         | vAmount                         | vBalance                               | vUpdatedAt
        new IssuerId("1") | new AccountId("2") | new Amount(new BigInteger("3")) | new Balance(BigInteger.valueOf(1000L)) | LocalDateTime.now()

    }

    def "「check」関数を呼び出すときに FinZone の ZoneId ではない場合にエラーがスローされる"() {
        setup:
        def command = MintServiceCommand.builder()
                .issuerId(vIssuerId)
                .accountId(vAccountId)
                .mintAmount(vAmount)
                .build()

        zoneIdThreadLocalHolder.setZoneId(ZoneId.of(3001))

        when:
        sut.check(command)

        zoneIdThreadLocalHolder.remove()
        then:

        def exception = thrown(ForbiddenException)
        if (exception instanceof ForbiddenException) {
            exception.messageCode == MessageCode.INSUFFICIENT_AUTHORITY
        }

        where:
        vIssuerId         | vAccountId         | vAmount                         | vBalance                               | vUpdatedAt
        new IssuerId("1") | new AccountId("2") | new Amount(new BigInteger("3")) | new Balance(BigInteger.valueOf(1000L)) | LocalDateTime.now()
    }

    def "「execute」関数を呼び出すときに FinZone の ZoneId ではない場合にエラーがスローされる"() {
        setup:
        def command = MintServiceCommand.builder()
                .issuerId(vIssuerId)
                .accountId(vAccountId)
                .mintAmount(vAmount)
                .build()

        zoneIdThreadLocalHolder.setZoneId(ZoneId.of(3001))

        when:
        sut.execute(command)

        zoneIdThreadLocalHolder.remove()
        then:

        def exception = thrown(ForbiddenException)
        if (exception instanceof ForbiddenException) {
            exception.messageCode == MessageCode.INSUFFICIENT_AUTHORITY
        }

        where:
        vIssuerId         | vAccountId         | vAmount                         | vBalance                               | vUpdatedAt
        new IssuerId("1") | new AccountId("2") | new Amount(new BigInteger("3")) | new Balance(BigInteger.valueOf(1000L)) | LocalDateTime.now()
    }

    @Unroll
    def "Mintのコントラクト実行でエラーコードが想定外の #vCode の場合、例外をスローする"() {
        setup:
        zoneIdThreadLocalHolder.setZoneId(ZoneId.of(3000))
        traceIdThreadLocalHolder.setTraceId(TraceId.of("012345678901234567890123456789ab"))

        when:
        mockCheckMint.execute(_, _, _) >> {}

        MintServiceCommand mintServiceCommand = MintServiceCommand.builder()
                                                .issuerId(new IssuerId("1"))
                                                .accountId(new AccountId("2"))
                                                .mintAmount(new Amount(new BigInteger("3")))
                                                .build()
        sut.execute(mintServiceCommand)

        then:
        1 * mockIssuerHasAccount.execute(new IssuerId("1"), new AccountId("2"),
                _ as IssuerHasAccount.RequestSwitch, mockCallBlockchainContractService)
        1 * mockCheckMint.execute(
                new CheckMintCommand(new IssuerId("1"), new AccountId("2"), new Amount(new BigInteger("3"))),
                new EntityId(new IssuerId("1")),
                mockCallBlockchainContractService)
        1 * mockSendBlockchainContractService.executeWithoutSignature(_) >> { throw new BlockchainTransactionRevertedException("error", "hash", vCode) }

        then:
        def e = thrown(expectedException)

        traceIdThreadLocalHolder.remove()
        zoneIdThreadLocalHolder.remove()

        where:
        vCode   || expectedException
        "09999" || BlockchainTransactionRevertedException
    }
}
