package com.decurret_dcp.dcjpy.core.application.account

import com.decurret_dcp.dcjpy.core.application.account.command.GetBalancesServiceCommand
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ResponseContractBytes32Value
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.GetBalanceList
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.result.TokenGetBalanceListResult
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId
import spock.lang.Shared
import spock.lang.Specification

class GetBalancesServiceSpec extends Specification {
    CallBlockchainContractService mockCallBlockchainContractService = Mock()
    IssuerGetAccount mockIssuerGetAccount = Mock()
    ProviderGetZone mockProviderGetZone = Mock()
    GetBalanceList mockGetBalanceList = Mock()

    @Shared
    ZoneIdThreadLocalHolder zoneIdThreadLocalHolder = new ZoneIdThreadLocalHolder()

    def sut = new GetBalancesService(
            mockCallBlockchainContractService,
            mockIssuerGetAccount,
            mockProviderGetZone,
            mockGetBalanceList
    )

    def "コントラクトのIssuer_getAccount関数とToken_getBalanceList関数を呼び出して残高リストを取得する"() {
        setup:
        def reqIssuerId = new IssuerId("1")
        def reqAccountId = new AccountId("2")

        def accountNameList = List.of("test1","test2")
        def zoneIdList = List.of(ZoneId.of(3001), ZoneId.of(3002))
        def zoneNameList = List.of("biz", "biz2")
        def balanceList = List.of(new Balance(BigInteger.valueOf(2000)), new Balance(BigInteger.valueOf(2000)))
        def accountStatusList = List.of(new ResponseContractBytes32Value(AccountStatus.ACTIVE.getValue()),
                new ResponseContractBytes32Value(AccountStatus.ACTIVE.getValue()))

        zoneIdThreadLocalHolder.setZoneId(ZoneId.of(3000))

        IssuerGetAccountResult responseIssuerGetAccount = IssuerGetAccountResult.builder()
                .accountName("test")
                .accountStatus(new ResponseContractBytes32Value(AccountStatus.ACTIVE.getValue()))
                .balance(new Balance(BigInteger.valueOf(2000)))
                .reasonCode(new ResponseContractBytes32Value("test"))
                .build()
        ProviderGetZoneResult responseProviderGetZone = ProviderGetZoneResult.builder()
                .zoneId(ZoneId.of(3000))
                .zoneName("finName")
                .build()
        TokenGetBalanceListResult responseGetBalanceList = TokenGetBalanceListResult.builder()
                .zoneIds(zoneIdList)
                .zoneNames(zoneNameList)
                .balances(balanceList)
                .accountNames(accountNameList)
                .accountStatus(accountStatusList)
                .build()

        when:
        def response = sut.execute(new GetBalancesServiceCommand(reqIssuerId, reqAccountId))

        then:
        1 * mockIssuerGetAccount.execute(reqIssuerId, reqAccountId, mockCallBlockchainContractService) >> responseIssuerGetAccount
        1 * mockProviderGetZone.execute(mockCallBlockchainContractService) >> responseProviderGetZone
        1 * mockGetBalanceList.execute(reqAccountId, mockCallBlockchainContractService) >> responseGetBalanceList

        then:
        verifyAll(response) {
            response.totalAmount == new Amount(BigInteger.valueOf(6000))
            response.balances[0].zoneId == ZoneId.of(3000)
            response.balances[0].zoneName == "finName"
            response.balances[0].balance == new Balance(BigInteger.valueOf(2000))
            response.balances[1].zoneId == ZoneId.of(3001)
            response.balances[1].balance == new Balance(BigInteger.valueOf(2000))
            response.balances[2].zoneId == ZoneId.of(3002)
            response.balances[2].balance == new Balance(BigInteger.valueOf(2000))
        }

        zoneIdThreadLocalHolder.remove()
    }

    def "コントラクトのToken_getBalanceList関数を呼び出した時のレスポンスが0件の場合"() {
        setup:
        def reqIssuerId = new IssuerId("1")
        def reqAccountId = new AccountId("2")

        def responseIssuerGetAccount = IssuerGetAccountResult.builder()
                .accountName("test")
                .accountStatus(new ResponseContractBytes32Value(AccountStatus.ACTIVE.getValue()))
                .balance(new Balance(BigInteger.valueOf(2000)))
                .reasonCode(new ResponseContractBytes32Value("test"))
                .build()
        def responseProviderGetZone = ProviderGetZoneResult.builder()
                .zoneId(ZoneId.of(3000))
                .zoneName("finName")
                .build()
        def responseGetBalanceList = TokenGetBalanceListResult.builder()
                .zoneIds(List.of())
                .zoneNames(List.of())
                .balances(List.of())
                .accountNames(List.of())
                .accountStatus(List.of())
                .build()

        zoneIdThreadLocalHolder.setZoneId(ZoneId.of(3000))

        when:
        def response = sut.execute(new GetBalancesServiceCommand(reqIssuerId, reqAccountId))

        then:
        1 * mockGetBalanceList.execute(reqAccountId, mockCallBlockchainContractService) >> responseGetBalanceList
        1 * mockProviderGetZone.execute(mockCallBlockchainContractService) >> responseProviderGetZone
        1 * mockIssuerGetAccount.execute(reqIssuerId, reqAccountId, mockCallBlockchainContractService) >> responseIssuerGetAccount

        then:
        verifyAll(response) {
            response.totalAmount == new Amount(BigInteger.valueOf(2000))
            response.balances[0].zoneId == ZoneId.of(3000)
            response.balances[0].zoneName == "finName"
            response.balances[0].balance == new Balance(BigInteger.valueOf(2000))
        }

        zoneIdThreadLocalHolder.remove()
    }
}
