package com.decurret_dcp.dcjpy.core.presentation.rest

import com.decurret_dcp.dcjpy.core.domain.model.message.Message
import com.decurret_dcp.dcjpy.core.domain.model.message.MessageBody
import com.decurret_dcp.dcjpy.core.domain.model.message.MessageCode
import com.decurret_dcp.dcjpy.core.domain.model.message.MessageRepository
import org.spockframework.spring.SpringBean
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.request
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class ApiAuthenticationValidationSpec extends ApiEndPointSpecification {
    @SpringBean
    MessageRepository mockMessageRepository = Mock()
    @SpringBean
    ExceptionHandlerControllerAdvice controllerAdvice = new ExceptionHandlerControllerAdvice(mockMessageRepository)

    def setup() {
        mockMessageRepository.selectAll() >> List.of(new Message(new MessageCode("1"), new MessageBody("1")))
    }

    def "Authorizationにトークンが設定されていない場合、401 Unauthorized が発生する"() {
        when:
        def response = mockMvc.perform(request(HttpMethod.GET, "/accounts"))

        then:
        response.andExpect(status().isUnauthorized())
                .andExpect(MockMvcResultMatchers.jsonPath("message").value("Unauthorized"))
                .andExpect(MockMvcResultMatchers.jsonPath("detail").value("E0039:Unauthorized"))
    }

    def "トークンがJWT形式として不正なとき、401 Unauthorized が発生する"() {
        when:
        def response = mockMvc.perform(request(HttpMethod.GET, "/accounts")
                .header("Authorization", "invalid token")
                .contentType(MediaType.APPLICATION_JSON_VALUE))

        then:
        response.andExpect(status().isUnauthorized())
                .andExpect(MockMvcResultMatchers.jsonPath("errorCode").value("E0039"))
                .andExpect(MockMvcResultMatchers.jsonPath("message").value("Unauthorized"))
                .andExpect(MockMvcResultMatchers.jsonPath("detail").value("E0039:Unauthorized"))
    }

    def "トークンから認証情報が取得できないとき、401 Unauthorized が発生する"() {
        when:
        def response = mockMvc.perform(request(HttpMethod.GET, "/accounts")
                .header("Authorization", "DUMMY")
                .contentType(MediaType.APPLICATION_JSON_VALUE))

        then:
        response.andExpect(status().isUnauthorized())
                .andExpect(MockMvcResultMatchers.jsonPath("errorCode").value("E0039"))
                .andExpect(MockMvcResultMatchers.jsonPath("message").value("Unauthorized"))
                .andExpect(MockMvcResultMatchers.jsonPath("detail").value("E0039:Unauthorized"))
    }
}
