package com.decurret_dcp.dcjpy.core.presentation.rest.account

import com.decurret_dcp.dcjpy.core.application.account.*
import com.decurret_dcp.dcjpy.core.application.account.result.*
import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp
import com.decurret_dcp.dcjpy.core.domain.model.account.*
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKey
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info
import com.decurret_dcp.dcjpy.core.domain.model.message.Message
import com.decurret_dcp.dcjpy.core.domain.model.message.MessageBody
import com.decurret_dcp.dcjpy.core.domain.model.message.MessageCode
import com.decurret_dcp.dcjpy.core.domain.model.message.MessageRepository
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId
import com.decurret_dcp.dcjpy.core.presentation.rest.ApiEndPointSpecification
import com.decurret_dcp.dcjpy.core.presentation.rest.ExceptionHandlerControllerAdvice
import org.spockframework.spring.SpringBean
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import spock.lang.Unroll

import java.time.Instant

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.request
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class AccountResourceSpec extends ApiEndPointSpecification {

    @SpringBean
    GetAccountListService getAccountListService = Mock()
    @SpringBean
    GetAccountService getAccountService = Mock()
    @SpringBean
    GetBalancesService getBalancesService = Mock()
    @SpringBean
    CreateAccountService createAccountService = Mock()
    @SpringBean
    UpdateAccountStatusService accountEnabledService = Mock()
    @SpringBean
    SyncAccountService syncAccountService = Mock()
    @SpringBean
    CreateOneTimeKeyService createOneTimeKeyService = Mock()
    @SpringBean
    IndustryAccountService industryAccountService = Mock()
    @SpringBean
    TokenLimitService tokenLimitService = Mock()
    @SpringBean
    CumulativeResetService cumulativeResetService = Mock()
    @SpringBean
    MessageRepository mockMessageRepository = Mock()
    @SpringBean
    ExceptionHandlerControllerAdvice controllerAdvice = new ExceptionHandlerControllerAdvice(mockMessageRepository)

    def setup() {
        def mintLimit = new Amount(new BigInteger("1"))
        def burnLimit = new Amount(new BigInteger("2"))
        def transferLimit = new Amount(new BigInteger("3"))
        def chargeLimit = new Amount(new BigInteger("4"))
        def dischargeLimit = new Amount(new BigInteger("5"))
        AccountLimit accountLimit = AccountLimit.builder()
                .mint(mintLimit)
                .burn(burnLimit)
                .transfer(transferLimit)
                .charge(chargeLimit)
                .discharge(dischargeLimit)
                .build()

        def dailyLimit = new Amount(new BigInteger("99"))
        def cumulativeMintLimit = new Amount(new BigInteger("10"))
        def cumulativeBurnLimit = new Amount(new BigInteger("20"))
        def cumulativeTransferLimit = new Amount(new BigInteger("30"))
        def cumulativeChargeLimit = new Amount(new BigInteger("40"))
        def cumulativeDischargeLimit = new Amount(new BigInteger("50"))
        AccountCumulativeLimit accountCumulativeLimit = AccountCumulativeLimit.builder()
                .total(dailyLimit)
                .mint(cumulativeMintLimit)
                .burn(cumulativeBurnLimit)
                .transfer(cumulativeTransferLimit)
                .charge(cumulativeChargeLimit)
                .discharge(cumulativeDischargeLimit)
                .build()

        AccountCumulativeAmount accountCumulativeAmount = AccountCumulativeAmount.builder()
                .total(new Amount(new BigInteger("100")))
                .mint(new Amount(new BigInteger("200")))
                .burn(new Amount(new BigInteger("300")))
                .transfer(new Amount(new BigInteger("400")))
                .charge(new Amount(new BigInteger("500")))
                .discharge(new Amount(new BigInteger("600")))
                .build()



        Account vAccount = Account.builder()
                .accountId(new AccountId("aaa"))
                .accountName("accountName")
                .accountStatus(AccountStatus.of("active"))
                .reasonCode(new ReasonCode("SAUA0006"))
                .zoneId(ZoneId.of(3000))
                .zoneName("zoneName")
                .balance(new Balance(10000))
                .cacheBalance(new Balance(10001))
                .accountLimit(accountLimit)
                .accountCumulativeLimit(accountCumulativeLimit)
                .accountCumulativeAmount(accountCumulativeAmount)
                .cumulativeDate(AppTimeStamp.of(Instant.ofEpochSecond(**********)))
                .appliedAt(AppTimeStamp.of(Instant.ofEpochSecond(**********)))
                .registeredAt(AppTimeStamp.of(Instant.ofEpochSecond(**********)))
                .terminatingAt(AppTimeStamp.of(Instant.ofEpochSecond(**********)))
                .terminatedAt(AppTimeStamp.of(Instant.ofEpochSecond(**********)))
                .build()


        def getAccountListResult = GetAccountListResult.builder()
                .accounts(Collections.EMPTY_LIST)
                .offset(new Offset(0L))
                .limit(new Limit(1L))
                .totalCount(new TotalCount(0))
                .build()


        def createAccountResult = CreateAccountServiceResult.builder()
                .accountId(new AccountId("123"))
                .accountName("ﾃｽﾄｱｶｳﾝﾄ")
                .accountLimit(accountLimit)
                .accountCumulativeLimit(accountCumulativeLimit)
                .build()

        getAccountListService.execute(_) >> getAccountListResult
        createAccountService.execute(_) >> createAccountResult
        getAccountService.execute(_) >> vAccount
        accountEnabledService.execute(_) >> new UpdateAccountStatusResult(new AccountId("111"), "accountName", AccountStatus.ACTIVE, new ReasonCode("SAUA0006"))
        getBalancesService.execute(_) >> new GetBalancesResult(new Amount(BigInteger.ZERO), Collections.emptyList())
        createOneTimeKeyService.execute(_) >> new CreateOneTimeKeyResult(new AccountId("111"), new PrivateKey("key"), new Info("123ab"))
        mockMessageRepository.selectAll() >> List.of(new Message(new MessageCode("1"), new MessageBody("1")))
    }

    @Unroll
    @WithMockUser(username = "test", authorities = ["ROLE_ADMIN", "ROLE_PROVIDER", "ROLE_ISSUER", "ROLE_VALIDATOR"])
    def "#method #url: AccountIDに半角英数以外の文字が入っているとき、404 not foundが発生する"() {
        when:
        def response = mockMvc.perform(request(method, url)
                .content(content)
                .contentType(MediaType.APPLICATION_JSON_VALUE))

        then:
        response.andExpect(status().isNotFound())
                .andExpect(MockMvcResultMatchers.jsonPath("message").value("Not Found"))
                .andExpect(MockMvcResultMatchers.jsonPath("detail").value("account_id not found"))

        where:
        method          | role        | url                           | content
        HttpMethod.GET  | "VALIDATOR" | "/accounts/-"                 | ""
        HttpMethod.GET  | "VALIDATOR" | "/accounts/¥"                 | ""
        HttpMethod.PUT  | "ISSUER"    | "/accounts/-/account_status"  | '{"request_id":"id","account_status":"active","reason_code":"SAUA0006"}'
        HttpMethod.PUT  | "ISSUER"    | "/accounts/¥/account_status"  | '{"request_id":"id","account_status":"active","reason_code":"SAUA0006"}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/-/synchronous/check" | '{"account_signature":"0x8644e4b426f3f43784ef8ef3fbff486b9c1e4dd45c493f7b5727dc6a057e1cac6c697de741b63af6549cc071e98a88c182d8d100f7b9c6154aadd93b339c7cb51b","info":"0x000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103d3567ab5db5350169d43b8cc49783cd584341d078bf950bfbb2b06f7b7c428d200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103b37291376263cd3d1e5b14d84ac9d3950b52f17b9756320f406a2d2ff80c3e9b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041c56a6381fed45fb83747ea118816a4930bf1d9ab541d437045fa4089541e8337416d21be9019b1a95fdadce8713f965e433759d12bef36571769861da9dc26371b00000000000000000000000000000000000000000000000000000000000000"}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/¥/synchronous/check" | '{"account_signature":"0x8644e4b426f3f43784ef8ef3fbff486b9c1e4dd45c493f7b5727dc6a057e1cac6c697de741b63af6549cc071e98a88c182d8d100f7b9c6154aadd93b339c7cb51b","info":"0x000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103d3567ab5db5350169d43b8cc49783cd584341d078bf950bfbb2b06f7b7c428d200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103b37291376263cd3d1e5b14d84ac9d3950b52f17b9756320f406a2d2ff80c3e9b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041c56a6381fed45fb83747ea118816a4930bf1d9ab541d437045fa4089541e8337416d21be9019b1a95fdadce8713f965e433759d12bef36571769861da9dc26371b00000000000000000000000000000000000000000000000000000000000000"}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/-/synchronous"     | '{"request_id":"id", "account_name": "name", "approval_limit":100, "account_signature":"0x8644e4b426f3f43784ef8ef3fbff486b9c1e4dd45c493f7b5727dc6a057e1cac6c697de741b63af6549cc071e98a88c182d8d100f7b9c6154aadd93b339c7cb51b","info":"0x000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103d3567ab5db5350169d43b8cc49783cd584341d078bf950bfbb2b06f7b7c428d200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103b37291376263cd3d1e5b14d84ac9d3950b52f17b9756320f406a2d2ff80c3e9b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041c56a6381fed45fb83747ea118816a4930bf1d9ab541d437045fa4089541e8337416d21be9019b1a95fdadce8713f965e433759d12bef36571769861da9dc26371b00000000000000000000000000000000000000000000000000000000000000"}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/¥/synchronous"     | '{"request_id":"id", "account_name": "name", "approval_limit":100, "account_signature":"0x8644e4b426f3f43784ef8ef3fbff486b9c1e4dd45c493f7b5727dc6a057e1cac6c697de741b63af6549cc071e98a88c182d8d100f7b9c6154aadd93b339c7cb51b","info":"0x000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103d3567ab5db5350169d43b8cc49783cd584341d078bf950bfbb2b06f7b7c428d200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103b37291376263cd3d1e5b14d84ac9d3950b52f17b9756320f406a2d2ff80c3e9b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041c56a6381fed45fb83747ea118816a4930bf1d9ab541d437045fa4089541e8337416d21be9019b1a95fdadce8713f965e433759d12bef36571769861da9dc26371b00000000000000000000000000000000000000000000000000000000000000"}'
        HttpMethod.GET  | "ISSUER"    | "/accounts/-/balances"        | ''
        HttpMethod.GET  | "ISSUER"    | "/accounts/¥/balances"        | ''
        HttpMethod.GET  | "ISSUER"    | "/accounts/-/security"        | ''
        HttpMethod.GET  | "ISSUER"    | "/accounts/¥/security"        | ''
        HttpMethod.POST | "VALIDATOR" | "/accounts/-/industry/check"  | '{"zone_id":3001}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/¥/industry/check"  | '{"zone_id":3001}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/-/industry"        | '{"request_id":"id","zone_id":3001}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/¥/industry"        | '{"request_id":"id","zone_id":3001}'
        HttpMethod.PUT  | "ISSUER"    | "/accounts/-/limit"           | '{"request_id":"1","mint_limit":1,"burn_limit":1,"transfer_limit":1,"charge_limit":1,"cumulative_limit":1}'
        HttpMethod.PUT  | "ISSUER"    | "/accounts/¥/limit"           | '{"request_id":"1","mint_limit":1,"burn_limit":1,"transfer_limit":1,"charge_limit":1,"cumulative_limit":1}'
        HttpMethod.PUT  | "ISSUER"    | "/accounts/-/cumulativereset" | '{"request_id":"id"}'
        HttpMethod.PUT  | "ISSUER"    | "/accounts/¥/cumulativereset" | '{"request_id":"id"}'
        HttpMethod.PUT  | "VALIDATOR" | "/accounts/-/biz_terminated"  | '{"request_id":"id","zone_id":3001}'
        HttpMethod.PUT  | "VALIDATOR" | "/accounts/¥/biz_terminated"  | '{"request_id":"id","zone_id":3001}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/-/biz_terminated/check" | '{"request_id":"id","zone_id":3001}'
        HttpMethod.POST | "VALIDATOR" | "/accounts/¥/biz_terminated/check" | '{"request_id":"id","zone_id":3001}'
    }
}
