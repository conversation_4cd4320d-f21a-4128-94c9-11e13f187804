package com.decurret_dcp.dcjpy.core.batch.share.domain.model.fundamental;

import java.math.BigInteger;

import org.seasar.doma.Domain;

import com.decurret_dcp.dcjpy.core.batch.share.domain.model.Assertion;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.Value;

@Domain(valueType = BigInteger.class)
@Value
@JsonSerialize(using = StateCodeJson.Serializer.class)
@JsonDeserialize(using = StateCodeJson.Deserializer.class)
public class StateCode {
    // 開設済み
    public static final StateCode OPENED = new StateCode(BigInteger.ONE);
    // 未開設
    public static final StateCode NOT_OPENED = new StateCode(BigInteger.TWO);
    BigInteger value;

    public StateCode(BigInteger value) {
        Assertion.assertNotNull(value, "value");
        this.value = value;
    }
}
