{"priority": 1, "request": {"method": "GET", "urlPathPattern": "/accounts/6078ESArL7rbWTzjuLDsGnnBNB4hDnrD"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"account_id": "{{request.path.[1]}}", "identified": true, "balance": 210000, "enabled": true, "reason_code": 10000, "terminated": false, "transfer_limit": 0, "exchange_limit": 0, "mint_limit": 0, "burn_limit": 0, "daily_limit": 10000, "cumulative_date": "{{cut (truncateDate(now timezone='Asia/Tokyo' format='yyyy-MM-ddTHH:mm:ss') 'first hour of day') '+09:00'}}", "cumulative_amount": 5001}, "transformers": ["response-template"]}}