{"priority": 12, "request": {"method": "POST", "urlPath": "/transactions/exchange", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"equalToJson": {"account_id": "${json-unit.any-string}", "to_region_id": "${json-unit.any-number}", "exchange_amount": "${json-unit.any-number}", "account_signature": "expiredSignature", "info": "${json-unit.regex}^0x([a-fA-F0-9]{896})"}, "ignoreArrayOrder": true}]}, "response": {"status": 400, "headers": {"Content-Type": "application/json"}, "jsonBody": {"message": "Bad Request", "detail": "E0063:signature expired"}, "transformers": ["response-template"]}}