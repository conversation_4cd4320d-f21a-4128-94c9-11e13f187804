{"priority": 11, "request": {"method": "POST", "urlPath": "/transactions/transfer", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"equalToJson": {"send_account_id": "${json-unit.any-string}", "from_account_id": "${json-unit.any-string}", "to_account_id": "${json-unit.any-string}", "transfer_amount": "${json-unit.any-number}", "account_signature": "${json-unit.regex}^0x([a-fA-F0-9]{130})", "info": "${json-unit.regex}^0x([a-fA-F0-9]{896})", "misc_value1": "invalidMisc", "misc_value2": "${json-unit.any-string}"}, "ignoreArrayOrder": true}]}, "response": {"status": 400, "headers": {"Content-Type": "application/json"}, "jsonBody": {"message": "Bad Request", "detail": "E0067:hexadecimal string cannot be set for misc_value"}, "transformers": ["response-template"]}}