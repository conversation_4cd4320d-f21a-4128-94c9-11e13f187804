import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { ForceBurnTokenTask } from '../tasks/ForceBurnTokenTask'
import { SetAccountStatusTask } from '../tasks/SetAccountStatusTask'
import { MintTokenTask } from '../tasks/MintTokenTask'
import { PartialForceBurnTokenTask } from '../tasks/PartialForceBurnTokenTask'
import { TransferTask } from '../tasks/TransferTask'
import { SyncAccountTask } from '../tasks/SyncAccountTask'
import { GetBizZoneAccountStatus } from '../tasks/GetBizZoneAccountStatusTask'
import { SetActiveBusinessAccountWithZoneTask } from '../tasks/SetActiveBusinessAccountWithZoneTask'
import { GetTokenInfoTask } from '../tasks/GetTokenInfoTask'
import { ERROR, showAccountsStatus, SUCCESS, extractAccountInfo, extractTotalSupply,extractBurnedAmount } from './utils'
import { RetrieveForceBurnEventTask } from '../tasks/RetrieveForceBurnEventTask'

export async function frozenForceBurn(accountId: string) {
  // 強制償却前のtotalSupplyを取得
  const tokenInfoBefore = await new GetTokenInfoTask(Network.LocalFin).execute({ providerId: networkConfig[Network.LocalFin].PROV_ID })
  const totalSupplyBefore = extractTotalSupply(tokenInfoBefore)

  await new SetAccountStatusTask(Network.LocalFin).execute({
    accountStatus: commonConfig.STATUS_FROZEN,
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId,
  })

  const frozenAccStatus = extractAccountInfo(await showAccountsStatus(accountId))
  const frozenBizZoneAccStatus = await new GetBizZoneAccountStatus(Network.LocalFin).execute({
    accountId,
  })

  if (frozenAccStatus.finZone.status !== commonConfig.STATUS_FROZEN) {
    return ERROR
  }

  if (
    !frozenBizZoneAccStatus.includes('active') ||
    !frozenBizZoneAccStatus.includes(accountId) ||
    !frozenBizZoneAccStatus.includes('3001')
  )
    return ERROR

  await new ForceBurnTokenTask(Network.LocalFin).execute({
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId,
  })
  const forceBurnAccStatus = extractAccountInfo(await showAccountsStatus(accountId))
  const forceBizZoneAccStatus = await new GetBizZoneAccountStatus(Network.LocalFin).execute({
    accountId,
  })

  // 強制償却イベントによる償却額を取得
  const forceBurnFromFinOutput = await new RetrieveForceBurnEventTask(Network.LocalFin).execute({})
  const burnedAmount = extractBurnedAmount(forceBurnFromFinOutput)

  if (
    forceBurnAccStatus.finZone.status !== commonConfig.STATUS_FORCE_BURNED ||
    forceBurnAccStatus.finZone.balance !== '0' ||
    forceBurnAccStatus.finZone.bizZoneAccountStatus !== commonConfig.STATUS_FORCE_BURNED ||
    forceBurnAccStatus.finZone.bizZoneAccountBalance !== '0' ||
    !forceBizZoneAccStatus.includes(commonConfig.STATUS_FORCE_BURNED) ||
    !forceBizZoneAccStatus.includes(accountId) ||
    !forceBizZoneAccStatus.includes(networkConfig[Network.LocalFin].BIZ_ZONE_ID)
  ) {
    return ERROR
  }

  // 強制償却後のtotalSupplyを取得
  const tokenInfoAfter = await new GetTokenInfoTask(Network.LocalFin).execute({ providerId: networkConfig[Network.LocalFin].PROV_ID })
  const totalSupplyAfter = extractTotalSupply(tokenInfoAfter)

  // 強制償却イベントによる償却額と、強制償却前後のTotalSupplyの差分が一致していることを確認
  if (totalSupplyBefore.totalSupply == null || totalSupplyAfter.totalSupply == null ||
    burnedAmount.burnedAmount == null) {
    return ERROR;
  }

  if (totalSupplyBefore.totalSupply - totalSupplyAfter.totalSupply !== burnedAmount.burnedAmount) {
    return ERROR;
  }

  await new SetAccountStatusTask(Network.LocalFin).execute({
    accountId,
    accountStatus: commonConfig.STATUS_ACTIVE,
  })

  const accountStatus = extractAccountInfo(await showAccountsStatus(accountId))
  const bizZoneAccStatus = await new GetBizZoneAccountStatus(Network.LocalFin).execute({
    accountId,
  })

  if (
    accountStatus.finZone.status !== commonConfig.STATUS_ACTIVE ||
    accountStatus.finZone.balance !== '0' ||
    accountStatus.finZone.bizZoneAccountStatus !== commonConfig.STATUS_FORCE_BURNED ||
    accountStatus.finZone.bizZoneAccountBalance !== '0' ||
    !bizZoneAccStatus.includes(commonConfig.STATUS_FORCE_BURNED) ||
    !bizZoneAccStatus.includes(accountId) ||
    !bizZoneAccStatus.includes(networkConfig[Network.LocalFin].BIZ_ZONE_ID)
  ) {
    return ERROR
  }

  return SUCCESS
}

export async function partialForceBurn() {
  const accountId = networkConfig[Network.LocalFin].ACCOUNT_ID_3
  const accountInfo = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfo.finZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfo.finZone.balance !== '0' ||
    accountInfo.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountInfo.finZone.bizZoneAccountBalance !== '0' ||
    accountInfo.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfo.bizZone.balance !== '0'
  )
    return ERROR

  await new MintTokenTask(Network.LocalFin).execute({ accountId: accountId, amount: '500' })

  const accountInfoAfterMint = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfoAfterMint.finZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterMint.finZone.balance !== '500' ||
    accountInfoAfterMint.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterMint.finZone.bizZoneAccountBalance !== '0' ||
    accountInfoAfterMint.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterMint.bizZone.balance !== '0'
  )
    return ERROR

  await new TransferTask(Network.LocalFin).execute({
    accountId: accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    amount: '100',
  })

  await new Promise((resolve) => setTimeout(resolve, 15000))

  const accountInfoAfterCharge = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfoAfterCharge.finZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterCharge.finZone.balance !== '400' ||
    accountInfoAfterCharge.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterCharge.finZone.bizZoneAccountBalance !== '100' ||
    accountInfoAfterCharge.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterCharge.bizZone.balance !== '100'
  )
    return ERROR

  await new SetAccountStatusTask(Network.LocalFin).execute({
    accountStatus: commonConfig.STATUS_FROZEN,
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId,
  })

  const accountInfoAfterFrozen = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfoAfterFrozen.finZone.status !== commonConfig.STATUS_FROZEN ||
    accountInfoAfterFrozen.finZone.balance !== '400' ||
    accountInfoAfterFrozen.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterFrozen.finZone.bizZoneAccountBalance !== '100' ||
    accountInfoAfterFrozen.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterFrozen.bizZone.balance !== '100'
  )
    return ERROR

  console.log('Start partial force burn account less than account balance: partial force burn 300 tokens')
  await new PartialForceBurnTokenTask(Network.LocalFin).execute({
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId: accountId,
    burnedAmount: '300',
    burnedBalance: '100',
  })

  const accountInfoAfterForceBurn1 = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfoAfterForceBurn1.finZone.status !== commonConfig.STATUS_FROZEN ||
    accountInfoAfterForceBurn1.finZone.balance !== '100' ||
    accountInfoAfterForceBurn1.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterForceBurn1.finZone.bizZoneAccountBalance !== '100' ||
    accountInfoAfterForceBurn1.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterForceBurn1.bizZone.balance !== '100'
  )
    return ERROR

  console.log('Start partial force burn account more than account balance: partial force burn 150 tokens')
  await new PartialForceBurnTokenTask(Network.LocalFin).execute({
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId: accountId,
    burnedAmount: '150',
    burnedBalance: '50',
  })

  const accountInfoAfterForceBurn2 = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfoAfterForceBurn2.finZone.status !== commonConfig.STATUS_FROZEN ||
    accountInfoAfterForceBurn2.finZone.balance !== '50' ||
    accountInfoAfterForceBurn2.finZone.bizZoneAccountStatus !== commonConfig.STATUS_FORCE_BURNED ||
    accountInfoAfterForceBurn2.finZone.bizZoneAccountBalance !== '0' ||
    accountInfoAfterForceBurn2.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfoAfterForceBurn2.bizZone.balance !== '100'
  )
    return ERROR

  await reActiveAccount(Network.LocalBiz, accountId)

  const activeAccStatus = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    activeAccStatus.finZone.status !== commonConfig.STATUS_ACTIVE ||
    activeAccStatus.finZone.balance !== '50' ||
    activeAccStatus.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    activeAccStatus.finZone.bizZoneAccountBalance !== '0' ||
    activeAccStatus.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    activeAccStatus.bizZone.balance !== '0'
  ) {
    return ERROR
  }

  return SUCCESS
}

async function reActiveAccount(network: Network, accountId: string) {
  await new SetAccountStatusTask(Network.LocalFin).execute({
    accountId,
    accountStatus: commonConfig.STATUS_ACTIVE,
  })

  await new SyncAccountTask(network).execute({ accountId: accountId })

  await new Promise((resolve) => setTimeout(resolve, 15000))

  await new SetActiveBusinessAccountWithZoneTask(Network.LocalFin).execute({ accountId })
}
