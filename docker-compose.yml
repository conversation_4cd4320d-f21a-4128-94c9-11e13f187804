version: "3.8"
services:
  app:
    image: amazoncorretto:17-alpine-jdk
    ports:
      - 8080:8080
    environment:
      - SPRING_PROFILES_ACTIVE=local-docker
      - DB_BASE=host.docker.internal
      - BC_CLIENT_BASE=host.docker.internal
      - AUTH_ISSUER_URL=http://host.docker.internal:8082
      - AWS_ACCESS_KEY_ID=access123
      - AWS_SECRET_ACCESS_KEY=secret123
    working_dir: /app
    volumes:
      - ./build/libs:/app
    command: java -jar dcbg-dcjpy-core-0.0.1-SNAPSHOT.jar
