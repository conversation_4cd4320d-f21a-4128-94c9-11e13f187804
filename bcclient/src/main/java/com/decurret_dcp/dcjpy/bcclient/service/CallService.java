package com.decurret_dcp.dcjpy.bcclient.service;

import com.decurret_dcp.dcjpy.bcclient.base.contract.FinancialContract;
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.service.TransactionResolver;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthCallExecutor;
import com.decurret_dcp.dcjpy.bcclient.base.transaction.EthTransactionCommand;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.MainWebSocketConnectionPool;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.SubWebSocketConnectionPool;
import com.decurret_dcp.dcjpy.bcclient.base.websocket.WebSocketConnectionPoolBase;
import com.decurret_dcp.dcjpy.bcclient.service.param.CallResult;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.Map;

@RequiredArgsConstructor
@Service
@Slf4j
public class CallService {

    private final TransactionResolver transactionResolver;

    private final MainWebSocketConnectionPool mainWebSocketConnectionPool;

    private final SubWebSocketConnectionPool subWebSocketConnectionPool;

    private final EthCallExecutor ethCallExecutor;

    /**
     * CallServiceを実行する
     *
     * @param command サービスインプット
     * @return CallResult
     */
    @NonNull
    public CallResult execute(@NonNull TransactionCommand command) {
        EthTransactionCommand ethCommand = this.transactionResolver.resolve(command);

        // 渡された引数に漏れがないか検証する
        ethCommand.verify();

        // コントラクト名より、接続先の DLT を決定する
        WebSocketConnectionPoolBase connectionPool = FinancialContract.mustFinancial(command.contractName)
                ? this.subWebSocketConnectionPool : this.mainWebSocketConnectionPool;

        // Call
        Map<String, Object> result = this.ethCallExecutor.callTransaction(connectionPool, ethCommand);
        return new CallResult(result);
   }
}