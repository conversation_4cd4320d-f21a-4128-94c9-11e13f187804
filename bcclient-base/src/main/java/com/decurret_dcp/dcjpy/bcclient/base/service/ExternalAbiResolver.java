package com.decurret_dcp.dcjpy.bcclient.base.service;

import java.io.IOException;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbi;
import com.decurret_dcp.dcjpy.bcclient.base.contract.ContractAbiConvertUtil;
import com.decurret_dcp.dcjpy.bcclient.base.exception.BadRequestException;
import com.decurret_dcp.dcjpy.bcclient.base.properties.AbiFormat;
import com.decurret_dcp.dcjpy.bcclient.base.properties.ApplicationProperty;
import com.decurret_dcp.dcjpy.bcclient.base.properties.S3Property;
import com.decurret_dcp.dcjpy.bcclient.base.adaptor.AwsS3Adaptor;
import com.decurret_dcp.dcjpy.bcclient.base.value.ZoneId;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

@RequiredArgsConstructor
@Component
@Slf4j
public class ExternalAbiResolver {

    private final ApplicationProperty property;

    private final S3Property s3Property;

    private final AwsS3Adaptor s3Adaptor;

    /**
     * 外部S3からコントラクトABIを取得する
     *
     * @param contractName コントラクト名
     * @return ContractAbi
     */
    public ContractAbi resolve(ZoneId zoneId, String contractName) {
        log.info("Finding an external contract (contractName={})", contractName);

        String key = zoneId.getValue() + "/" + contractName + ".json";
        String bucketName = this.s3Property.externalContractBucketName;

        byte[] abiContent;
        try {
            // 外部ABI用S3からJSONをダウンロードする
            abiContent = this.s3Adaptor.fetchObjectAsByte(bucketName, key);
        } catch (NoSuchKeyException noKeyExc) {
            log.warn("No abi file on the external abi S3 (key={})", key);
            throw new BadRequestException("contractName not found");
        }

        AbiFormat abiFormat = this.property.getAbiFormat();
        try {
            return ContractAbiConvertUtil.convertByteToObject(abiFormat, contractName, abiContent);
        } catch (IOException ioExc) {
            log.warn("Failed to convert an external abi (contractName={})", contractName);
            throw new RuntimeException(ioExc);
        }
    }
}
