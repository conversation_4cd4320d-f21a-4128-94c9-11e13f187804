#!/bin/bash
# 正常系テストのうち、アカウント作成〜アカウント解約を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 2 ]; then
    echo "[引数なし] ./02_normal2-fin.sh [issuer_client_id] [issuer_client_secret]"
    exit 9
fi

# ACCESS_TOKENの取得
I_CLIENT_ID=$1
I_CLIENT_SECRET=$2
I_ACCESS_TOKEN=$(curl -Ss -u $I_CLIENT_ID:$I_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $I_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 9
fi

# issuer_idとvalidator_idをidentity_authoritiesから取得
ISSUER_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select issuer_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $ISSUER_ID = null ]; then
    echo "ERROR:ISSUER_IDの取得に失敗しました"
    exit 9
fi
VALIDATOR_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:VALIDATOR_IDの取得に失敗しました"
    exit 9
fi

echo "----- 02_normal2-fin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "22-3 発行者取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "42-3 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $VALIDATOR_ID != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:issuer_idが設定されていない"
    exit 1
fi
echo ""

echo "2-1 アカウント一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "1-1 アカウント作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}')
echo $RESPONSE
ACCOUNT_ID0=$(echo $RESPONSE | jq -r ".account_id")
if [ $ACCOUNT_ID0 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "account_attributesの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select attribute from account_attributes where account_id='$ACCOUNT_ID0'")
echo ""
sleep 10

echo "3-1 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "50-1 アカウントのアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID0 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "external_signersの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'external_signers : account_id OK' from external_signers where signer_id='$ACCOUNT_ID0'")
echo ""

echo "2-2 アカウント一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "49-1 残高取得（付加領域で同期なし）"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
echo ""

echo "5-1 アカウント属性取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "4 アカウント属性更新"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID0/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"string1\":\"aaa\",\"number1\":123,\"boolean1\":true}}")
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "5-2 アカウント属性取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "7-1 アカウントの本人確認"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID0/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}")
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "3-2 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ true != $(echo $RESPONSE | jq -r ".identified") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "54 限度額更新"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID0/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1,"exchange_limit":2,"mint_limit":3,"burn_limit":4,"daily_limit":5}')
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "3-3 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ 1 != $(echo $RESPONSE | jq -r ".transfer_limit") ]; then
    echo "ERROR:失敗しました(transfer_limit)"
    exit 1
fi
if [ 2 != $(echo $RESPONSE | jq -r ".exchange_limit") ]; then
    echo "ERROR:失敗しました(exchange_limit)"
    exit 1
fi
if [ 3 != $(echo $RESPONSE | jq -r ".mint_limit") ]; then
    echo "ERROR:失敗しました(mint_limit)"
    exit 1
fi
if [ 4 != $(echo $RESPONSE | jq -r ".burn_limit") ]; then
    echo "ERROR:失敗しました(burn_limit)"
    exit 1
fi
if [ 5 != $(echo $RESPONSE | jq -r ".daily_limit") ]; then
    echo "ERROR:失敗しました(daily_limit)"
    exit 1
fi
echo ""

echo "6 アカウントの無効化"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID0/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false,\"reason_code\":10000}")
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "3-4 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ false != $(echo $RESPONSE | jq -r ".enabled") ]; then
    echo "ERROR:enabledがtrueのまま"
    exit 1
fi
if [ 10000 != $(echo $RESPONSE | jq -r ".reason_code") ]; then
    echo "ERROR:失敗しました(reason_code)"
    exit 1
fi
echo ""

echo "51 アカウント解約"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID0/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"terminated": true}')
echo $RESPONSE
if [ $ACCOUNT_ID0 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "3-5 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID0 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ true != $(echo $RESPONSE | jq -r ".terminated") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 02_normal2-fin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
