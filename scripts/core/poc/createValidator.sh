#!/bin/bash

source env.sh

if [ $# -ne 1 ]; then
echo "usage:"
echo "    bash $0 [validator name]"
exit 1
fi

VALIDATOR_ID=$(curl -Ss -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"$1\"}" | jq -r ".validator_id"); echo $VALIDATOR_ID

VALIDATOR_RES=$(curl -Ss -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}")
V_CLIENT_ID=$(echo $VALIDATOR_RES | jq -r ".client_id")
V_CLIENT_SECRET=$(echo $VALIDATOR_RES | jq -r ".client_secret")

echo "****************************************"
echo "VALIDATOR_ID=$VALIDATOR_ID"
echo "V_CLIENT_ID=$V_CLIENT_ID"
echo "V_CLIENT_SECRET=$V_CLIENT_SECRET"
echo "****************************************"
echo "DB"
psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select * from external_signers where signer_id='$VALIDATOR_ID'"
echo "****************************************"

sleep 1

echo "確認"
V_ACCESS_TOKEN=$(curl -Ss -u $V_CLIENT_ID:$V_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json"
echo ""
