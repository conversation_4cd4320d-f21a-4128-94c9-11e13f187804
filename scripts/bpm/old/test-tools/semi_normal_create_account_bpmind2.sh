#!/bin/bash
# 共通領域で付加領域用アカウント作成、チャージを行うシェル

echo "対象の共通領域にポートフォワード済、対象の共通領域のProfileに変更していることを確認し、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit
fi

# 環境変数の設定
source ./env-bpmfin.sh

# 作成した環境変数の設定
source ./env_semi_normal_01.sh

echo "----- semi_normal_create_account_bpmind2 start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

# 1つ目の付加領域用口座を作成（残高0）
echo "サインイン"
SIGN_IN_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
echo "付加領域用口座作成"
INDUSTRY_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}")
echo $INDUSTRY_RES3

# 2つ目の付加領域用口座を作成（残高あり）
echo "サインイン"
SIGN_IN_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
echo "付加領域用口座作成"
INDUSTRY_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}")
echo $INDUSTRY_RES4

sleep 4

echo "共通領域コイン発行"
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"mint_amount\":10000}"
echo ""

sleep 4

echo "共通領域コインと付加領域コインのチャージ／返還受付"
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}"
echo ""

sleep 4

echo "共通領域コインと付加領域コインのチャージ／返還"
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}"
echo ""

echo "----- semi_normal_create_account_bpmind2 end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`