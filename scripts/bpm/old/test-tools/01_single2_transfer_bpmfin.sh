#!/bin/bash
# 共通領域シングルモードでサインイン〜送金を行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 8 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME \$ADMIN_PASSWORD \$PHONE_NUMBER4 \$PASSWORD4 \$PHONE_NUMBER5 \$PASSWORD5 \$PHONE_NUMBER6 \$PASSWORD6"
  echo ""
  exit 9
fi

ADMIN_USER_NAME=$1
ADMIN_PASSWORD=$2
PHONE_NUMBER4=$3
PASSWORD4=$4
PHONE_NUMBER5=$5
PASSWORD5=$6
PHONE_NUMBER6=$7
PASSWORD6=$8

echo "----- 01_single2_transfer_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "47-2 管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .
echo ""

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "59-3 端末認証なしサインアップ"
SIGN_IN_ID4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER4\", \"password\":\"$PASSWORD4\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID4 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-6 サインイン受付"
SIGN_IN_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
echo $SIGN_IN_RES4 | jq .
if [ -z "$SIGN_IN_RES4" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
if [ $ACCESS_TOKEN4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ID_TOKEN4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "4-4 共通領域用口座開設受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234504\", \"bank_account_name\":\"accountname4\"}" | jq .
echo ""

echo "5-4 共通領域用口座作成"
USER_RES5_4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234504\", \"bank_account_name\":\"accountname4\"}")
echo "$USER_RES5_4" | jq .
DC_BANK_NUMBER4=$(echo $USER_RES5_4 | jq -r ".dc_bank_number")
ACCOUNT_ID5_4=$(echo $USER_RES5_4 | jq -r ".account_id")
if [ $DC_BANK_NUMBER4 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID5_4 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi

sleep 10

echo "6-8 共通領域用口座／付加領域用口座取得"
USER_RES6_8=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$USER_RES6_8" | jq .
DC_BANK_NUMBER6_8=$(echo $USER_RES6_8 | jq -r ".dc_bank_number")
if [ "$DC_BANK_NUMBER4" != "$DC_BANK_NUMBER6_8" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "59-4 端末認証なしサインアップ"
SIGN_IN_ID5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER5\", \"password\":\"$PASSWORD5\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID5 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-7 サインイン受付"
SIGN_IN_RES5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\"}")
echo $SIGN_IN_RES5 | jq .
if [ -z "$SIGN_IN_RES5" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")
if [ $ACCESS_TOKEN5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ID_TOKEN5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "4-5 共通領域用口座開設受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234505\", \"bank_account_name\":\"accountname5\"}" | jq .
echo ""

echo "5-5 共通領域用口座作成"
USER_RES5_5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234505\", \"bank_account_name\":\"accountname5\"}")
echo "$USER_RES5_5" | jq .
DC_BANK_NUMBER5=$(echo $USER_RES5_5 | jq -r ".dc_bank_number")
ACCOUNT_ID5_5=$(echo $USER_RES5_5 | jq -r ".account_id")
if [ $DC_BANK_NUMBER5 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID5_5 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi

sleep 10

echo "6-9 共通領域用口座／付加領域用口座取得"
USER_RES6_9=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo "$USER_RES6_9" | jq .
DC_BANK_NUMBER6_9=$(echo $USER_RES6_9 | jq -r ".dc_bank_number")
if [ "$DC_BANK_NUMBER5" != "$DC_BANK_NUMBER6_9" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "59-5 端末認証なしサインアップ"
SIGN_IN_ID6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER6\", \"password\":\"$PASSWORD6\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID6 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-8 サインイン受付"
SIGN_IN_RES6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID6\", \"password\":\"$PASSWORD6\"}")
echo $SIGN_IN_RES6 | jq .
if [ -z "$SIGN_IN_RES6" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".access_token")
ID_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".id_token")
if [ $ACCESS_TOKEN6 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ID_TOKEN6 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "4-6 共通領域用口座開設受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234506\", \"bank_account_name\":\"accountname6\"}" | jq .
echo ""

echo "5-6 共通領域用口座作成"
USER_RES5_6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234506\", \"bank_account_name\":\"accountname6\"}")
echo "$USER_RES5_6" | jq .
DC_BANK_NUMBER6=$(echo $USER_RES5_6 | jq -r ".dc_bank_number")
ACCOUNT_ID6=$(echo $USER_RES5_6 | jq -r ".account_id")
if [ $DC_BANK_NUMBER6 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID6 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi

sleep 10

echo "6-10 共通領域用口座／付加領域用口座取得"
USER_RES6_10=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json")
echo "$USER_RES6_10" | jq .
DC_BANK_NUMBER6_10=$(echo $USER_RES6_10 | jq -r ".dc_bank_number")
if [ "$DC_BANK_NUMBER6" != "$DC_BANK_NUMBER6_10" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-8 残高取得"
BALANCES_RES8_8=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$BALANCES_RES8_8" | jq .
TOTAL_BALANCE8_8=$(echo $BALANCES_RES8_8 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_8" != "0" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "8-9 残高取得"
BALANCES_RES8_9=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo "$BALANCES_RES8_9" | jq .
TOTAL_BALANCE8_9=$(echo $BALANCES_RES8_9 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_9" != "0" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "8-10 残高取得"
BALANCES_RES8_10=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json")
echo "$BALANCES_RES8_10" | jq .
TOTAL_BALANCE8_10=$(echo $BALANCES_RES8_10 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_10" != "0" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-6 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "45-4 共通領域コイン発行／償却状況照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/token/history -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "51-3 アカウントID取得"
USERS_RES51_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER4 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo $USERS_RES51_3 | jq .
ACCOUNT_ID51_3=$(echo $USERS_RES51_3 | jq -r ".account_id")
if [ "$ACCOUNT_ID5_4" != "$ACCOUNT_ID51_3" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "10-4 署名鍵取得"
SECURITY_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$SECURITY_RES4" | jq .
SK_O4=$(echo $SECURITY_RES4 | jq -r ".sk_o")
INFO4=$(echo $SECURITY_RES4 | jq -r ".info")
if [ $SK_O4 == null ]; then
  echo "署名鍵取得(SK_O4)に失敗しました"
  exit 1
fi
if [ $INFO4 == null ]; then
  echo "署名鍵取得(INFO4)に失敗しました"
  exit 1
fi

echo "11-2 共通領域コイン発行"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"mint_amount\":10000}" | jq .
echo ""

# 承認モードが有効な場合に限り実施
if [ "$TOKEN_FLOW_MODE" == "approval" ]; then
  echo "53-3 承認待ち処理一覧取得"
  ORDER_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET "$BASE_URL/management/orders?offset=0&limit=1&order_status=pending" -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json");
  echo "$ORDER_RES3" | jq .
  echo ""

  ORDER_ID3=$(echo $ORDER_RES3 | jq -r ".order_items[0].order_id")
  if [ $ORDER_ID3 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER3=$(echo $ORDER_RES3 | jq -r ".order_items[0].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER3" != "$DC_BANK_NUMBER4" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER4)と異なります"
    exit 1
  fi

  echo "54-3 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID3/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":10000}' | jq .
  echo ""
fi

sleep 8

echo "6-11 共通領域用口座／付加領域用口座取得"
USER_RES6_11=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$USER_RES6_11" | jq .
BALANCE6_11=$(echo $USER_RES6_11 | jq -r ".balance")
if [ "$BALANCE6_11" != "10000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-11 残高取得"
BALANCES_RES8_11=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$BALANCES_RES8_11" | jq .
TOTAL_BALANCE8_11=$(echo $BALANCES_RES8_11 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_11" != "10000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-7 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "45-5 共通領域コイン発行／償却状況照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/token/history -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "17-2 共通領域コイン償却"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"burn_amount\":1000}" | jq .
echo ""

# 承認モードが有効な場合に限り実施
if [ "$TOKEN_FLOW_MODE" == "approval" ]; then
  echo "53-4 承認待ち処理一覧取得"
  ORDER_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET "$BASE_URL/management/orders?offset=0&limit=1&order_status=pending" -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json");
  echo "$ORDER_RES4" | jq .
  echo ""

  ORDER_ID4=$(echo $ORDER_RES4 | jq -r ".order_items[0].order_id")
  if [ $ORDER_ID4 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER4=$(echo $ORDER_RES4 | jq -r ".order_items[0].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER4" != "$DC_BANK_NUMBER4" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER4)と異なります"
    exit 1
  fi

  echo "54-4 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID4/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":1000}' | jq .
  echo ""
fi

sleep 8

echo "6-12 共通領域用口座／付加領域用口座取得"
USER_RES6_12=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$USER_RES6_12" | jq .
BALANCE6_12=$(echo $USER_RES6_12 | jq -r ".balance")
if [ "$BALANCE6_12" != "9000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-12 残高取得"
BALANCES_RES8_12=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$BALANCES_RES8_12" | jq .
TOTAL_BALANCE8_12=$(echo $BALANCES_RES8_12 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_12" != "9000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-8 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions?offset=0\&limit=5\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3000\&region_id_sort=desc\&date_sort=desc -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "45-6 共通領域コイン発行／償却状況照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/token/history -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "10-5 署名鍵取得"
SECURITY_RES6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json")
echo $SECURITY_RES6 | jq .

# 署名鍵を変数に設定
SK_O6=$(echo $SECURITY_RES6 | jq -r ".sk_o")
INFO6=$(echo $SECURITY_RES6 | jq -r ".info")
if [ $SK_O6 == null ]; then
  echo "署名鍵取得に失敗しました"
  exit 1
fi

if [ $INFO6 == null ]; then
  echo "署名鍵取得に失敗しました"
  exit 1
fi

echo "3-9 サインイン受付"
SIGN_IN_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
echo $SIGN_IN_RES4 | jq .
if [ -z "$SIGN_IN_RES4" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
DC_BANK_NUMBER4=$(echo $SIGN_IN_RES4 | jq -r ".dc_bank_number")
ACCOUNT_ID4=$(echo $SIGN_IN_RES4 | jq -r ".account_id")
if [ $ID_TOKEN4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "3-10 サインイン受付"
SIGN_IN_RES5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\"}")
echo $SIGN_IN_RES5 | jq .
if [ -z "$SIGN_IN_RES5" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")
DC_BANK_NUMBER5=$(echo $SIGN_IN_RES5 | jq -r ".dc_bank_number")
ACCOUNT_ID5=$(echo $SIGN_IN_RES5 | jq -r ".account_id")
if [ $ID_TOKEN5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "51-4 アカウントID取得"
USERS_RES51_4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER5 -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo $USERS_RES51_4 | jq .
ACCOUNT_ID51_4=$(echo $USERS_RES51_4 | jq -r ".account_id")
if [ "$ACCOUNT_ID5" != "$ACCOUNT_ID51_4" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "10-6 署名鍵取得"
SECURITY_RES5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo "$SECURITY_RES5" | jq .
SK_O5=$(echo $SECURITY_RES5 | jq -r ".sk_o")
INFO5=$(echo $SECURITY_RES5 | jq -r ".info")
if [ $SK_O5 == null ]; then
  echo "署名鍵取得(SK_O5)に失敗しました"
  exit 1
fi
if [ $INFO5 == null ]; then
  echo "署名鍵取得(INFO5)に失敗しました"
  exit 1
fi

# 送金用アカウント署名作成
TRANSFER_SIGNATURE=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O4 $ACCOUNT_ID4 $ACCOUNT_ID4 $ACCOUNT_ID5 2000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$TRANSFER_SIGNATURE" ]; then
  echo "送金用アカウント署名作成に失敗しました"
  exit 1
fi

echo "12-2 共通領域コイン送金／付加領域コイン移転受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER5\", \"transfer_amount\":2000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO4\"}" | jq .
echo ""

echo "13-2 共通領域コイン送金／付加領域コイン移転"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER5\", \"transfer_amount\":2000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO4\"}" | jq .
echo ""

echo "6-13 共通領域用口座／付加領域用口座取得"
USER_RES6_13=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$USER_RES6_13" | jq .
BALANCE6_13=$(echo $USER_RES6_13 | jq -r ".balance")
if [ "$BALANCE6_13" != "7000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-13 残高取得"
BALANCES_RES8_13=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$BALANCES_RES8_13" | jq .
TOTAL_BALANCE8_13=$(echo $BALANCES_RES8_13 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_13" != "7000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-9 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "6-14 共通領域用口座／付加領域用口座取得"
USER_RES6_14=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo "$USER_RES6_14" | jq .
BALANCE6_14=$(echo $USER_RES6_14 | jq -r ".balance")
if [ "$BALANCE6_14" != "2000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-14 残高取得"
BALANCES_RES8_14=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo "$BALANCES_RES8_14" | jq .
TOTAL_BALANCE8_14=$(echo $BALANCES_RES8_14 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_14" != "2000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-10 デジタル通貨取引一覧照会"
TRANSACTION_RES18_10=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo "$TRANSACTION_RES18_10" | jq .

echo ""
echo "以降のスクリプト実行前に、以下をターミナル上で実行してください"
echo ""
echo "export SK_O4=$SK_O4"
echo "export INFO4=$INFO4"
echo "export ACCOUNT_ID4=$ACCOUNT_ID4"
echo "export SK_O5=$SK_O5"
echo "export INFO5=$INFO5"
echo "export ACCOUNT_ID5=$ACCOUNT_ID5"
echo "export SK_O6=$SK_O6"
echo "export INFO6=$INFO6"
echo "export ACCOUNT_ID6=$ACCOUNT_ID6"
echo "export SIGN_IN_ID4=$SIGN_IN_ID4"
echo "export SIGN_IN_ID5=$SIGN_IN_ID5"
echo "export SIGN_IN_ID6=$SIGN_IN_ID6"
echo ""

echo "----- 01_single2_transfer_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`