#!/usr/bin/env bash

usage () {
    cat <<EOF

$(basename ${0}) is a tool for stop RDS cluster.

Usage:
    $(basename ${0}) [<options>]

Options:
    --profile, -p   Set awsp profile configured in ~/.aws/config.
    --force, -f     Attempt to stop RDS cluster without prompting for confirmation.
    --help, -h      Print this.
EOF
}

. common_function.sh

# argument proccessing
while (( $# > 0))
do
    case $1 in
        -p | --profile | --profile=*)
            if [[ "$1" =~ ^--profile= ]]; then
                PROFILE="${1/--profile=/}"
            elif [[ -z "$2" ]] || [[ "$2" =~ ^-+ ]]; then
                printf "\nError: 'profile' requires an argument.\n" 1>&2
                exit 1
            else 
                PROFILE=$2
                shift
            fi
            ;;
        -f | --force)
            FORCE=1
            ;;
        -h | --help)
            usage
            exit 1
            ;;
        -*)
            printf "\nError: invalid option %s\n" "$1" 1>&2
            usage
            exit 1
            ;;
        *)
            ARGS=("${ARGS[@]}" "$1")
            ;;
    esac
    shift
done

if [[ -z "${PROFILE}" ]] && [[ -z "${AWS_PROFILE}" ]]; then
    printf "\nError: 'profile' or \$AWS_PROFILE is not set.\n"
    exit 1
    elif [[ -z "${PROFILE}" ]] && [[ -n ${AWS_PROFILE} ]]; then
    PROFILE=${AWS_PROFILE}
fi

# check tools
if ! check_tools "aws jq"; then
    exit 1
fi

# check "aws sts" and aws profile
printf "\nprofile: %s\n\n" "${PROFILE}"

if ! aws_profile_check "${PROFILE}" ; then
    exit 1
fi

##### stop RDS #####

RDS_CLUSTER_NAME=$(rds_cluster_name "${PROFILE}")
printf "\nRDS cluster name: %s\n" "${RDS_CLUSTER_NAME}"

if [[ $(rds_cluster_status "${PROFILE}") != 'available' ]]; then
    printf "\nRDS cluster status is not 'available'.\n"
    exit 1
fi

printf "\nYou are about to stop RDS cluster, in profile: %s\n\n" "${PROFILE}"

if [[ ${FORCE} -eq 0 ]]; then
    read -n1 -p "Are you sure? (y/N): " yn
    case "${yn}" in 
        [yY]*) 
            echo
            ;;
        *) 
            printf "\n\nabort.\n"
            exit 1
            ;;
    esac
fi

# stop RDS cluster
aws rds stop-db-cluster --db-cluster-identifier "${RDS_CLUSTER_NAME}" --profile "${PROFILE}" --no-cli-pager
if [[ $? -ne 0 ]]; then
    printf "\n'aws rds start-db-cluster' command failed.\n" 
    exit 1
else
    printf "\nRDS cluster is stopping.\n"
fi

# watching status
SECONDS=0
while :
do
    if [[ $(rds_cluster_status "${PROFILE}") == 'stopped' ]]; then
        printf "\nRDS clusters stopped.\n" 
        break
    fi
    sleep 10
    i=${SECONDS}
    ((sec=i%60, min=(i%3600)/60))
    TIMESTAMP=$(printf "%02d:%02d" "${min}" "${sec}")
    printf "Elapsed Time: %s  Status: %s\n" "${TIMESTAMP}" "$(rds_cluster_status "${PROFILE}")"
done

exit 0