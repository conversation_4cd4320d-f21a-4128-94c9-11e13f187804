{"address": "0xe4Ba9c2420dEa4D8Cf56C2dAdD69312D59EBd69b", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "restoreAccounts", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "accounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreBusinessZoneAccounts", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "bizAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreFinancialZoneAccounts", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "financialZoneAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple", "name": "financialZoneAccountData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "dischargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}, {"type": "tuple", "name": "cumulativeTransactionLimits", "components": [{"type": "uint256", "name": "cumulativeMintLimit"}, {"type": "uint256", "name": "cumulativeMintAmount"}, {"type": "uint256", "name": "cumulativeBurnLimit"}, {"type": "uint256", "name": "cumulativeBurnAmount"}, {"type": "uint256", "name": "cumulativeChargeLimit"}, {"type": "uint256", "name": "cumulativeChargeAmount"}, {"type": "uint256", "name": "cumulativeDischargeLimit"}, {"type": "uint256", "name": "cumulativeDischargeAmount"}, {"type": "uint256", "name": "cumulativeTransferLimit"}, {"type": "uint256", "name": "cumulativeTransferAmount"}]}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restore<PERSON><PERSON>uer<PERSON>", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "issuers", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreProviders", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "providers", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32[]", "name": "availableIssuerIds"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreToken", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreValidators", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "validators", "components": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "validatorAccountId"}, {"type": "bool", "name": "enabled"}, {"type": "bool", "name": "validatorIdExistence"}, {"type": "bool", "name": "issuerIdLinkedFlag"}, {"type": "address", "name": "validatorEoa"}, {"type": "tuple[]", "name": "validAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByValidatorId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x23b78954a705973030d871502b8ff010fd15c07fad440c65531dd6079bbfef6b", "receipt": {"to": null, "from": "0x90c37c63f3c5a07a12e16d8aD5708b5bC7F4E287", "contractAddress": "0xe4Ba9c2420dEa4D8Cf56C2dAdD69312D59EBd69b", "transactionIndex": 0, "gasUsed": "2865919", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x98cf603f41445b1210021ab2ec68c104c53e10fdbfa046a6a596057b62bce933", "blockNumber": 179, "cumulativeGasUsed": "2865919", "status": 1}}