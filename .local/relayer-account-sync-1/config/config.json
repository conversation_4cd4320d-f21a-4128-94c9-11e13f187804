{"global": {"timeout": "10s", "light-cache-size": 20, "logger": {"level": "INFO", "format": "text", "output": "stdout"}}, "chains": [{"chain": {"@type": "/relayer.chains.ethereum.config.ChainConfig", "chain_id": "********", "eth_chain_id": "5151", "rpc_addr": "http://host.docker.internal:18451", "signer": {"@type": "/relayer.chains.ethereum.signers.hd.SignerConfig", "mnemonic": "math razor capable expose worth grape metal sunset metal sudden usage scheme", "path": "m/44'/60'/0'/0/0"}, "ibc_address": "******************************************", "initial_send_checkpoint": 1, "initial_recv_checkpoint": 1, "enable_debug_trace": false, "average_block_time_msec": 2000, "max_retry_for_inclusion": 3, "gas_estimate_rate": {"numerator": 1, "denominator": 1}, "max_gas_limit": *********, "tx_type": "auto"}, "prover": {"@type": "/relayer.provers.ibft2.config.ProverConfig", "trust_level_numerator": 1, "trust_level_denominator": 3, "trusting_period": 1209600}}, {"chain": {"@type": "/relayer.chains.ethereum.config.ChainConfig", "chain_id": "********", "eth_chain_id": "5152", "rpc_addr": "http://host.docker.internal:28451", "signer": {"@type": "/relayer.chains.ethereum.signers.hd.SignerConfig", "mnemonic": "math razor capable expose worth grape metal sunset metal sudden usage scheme", "path": "m/44'/60'/0'/0/0"}, "ibc_address": "******************************************", "initial_send_checkpoint": 1, "initial_recv_checkpoint": 1, "enable_debug_trace": false, "average_block_time_msec": 2000, "max_retry_for_inclusion": 3, "max_gas_limit": *********, "gas_estimate_rate": {"numerator": 1, "denominator": 1}, "tx_type": "auto"}, "prover": {"@type": "/relayer.provers.ibft2.config.ProverConfig", "trust_level_numerator": 1, "trust_level_denominator": 3, "trusting_period": 1209600}}], "paths": {"account-sync": {"src": {"chain-id": "********", "client-id": "hb-ibft2-0", "connection-id": "connection-0", "channel-id": "channel-0", "port-id": "account-sync", "order": "unordered", "version": "account-sync-1"}, "dst": {"chain-id": "********", "client-id": "hb-ibft2-0", "connection-id": "connection-0", "channel-id": "channel-0", "port-id": "account-sync", "order": "unordered", "version": "account-sync-1"}, "strategy": {"type": "naive", "src-noack": false, "dst-noack": false}}, "balance-sync": {"src": {"chain-id": "********", "client-id": "hb-ibft2-1", "connection-id": "connection-1", "channel-id": "channel-1", "port-id": "balance-sync", "order": "unordered", "version": "balance-sync-1"}, "dst": {"chain-id": "********", "client-id": "hb-ibft2-1", "connection-id": "connection-1", "channel-id": "channel-1", "port-id": "balance-sync", "order": "unordered", "version": "balance-sync-1"}, "strategy": {"type": "naive", "src-noack": false, "dst-noack": false}}, "token-transfer": {"src": {"chain-id": "********", "client-id": "hb-ibft2-2", "connection-id": "connection-2", "channel-id": "channel-2", "port-id": "token-transfer", "order": "unordered", "version": "token-transfer-1"}, "dst": {"chain-id": "********", "client-id": "hb-ibft2-2", "connection-id": "connection-2", "channel-id": "channel-2", "port-id": "token-transfer", "order": "unordered", "version": "token-transfer-1"}, "strategy": {"type": "naive", "src-noack": false, "dst-noack": false}}}}