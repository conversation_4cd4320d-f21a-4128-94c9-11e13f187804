import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { balanceSyncBridgeFuncs } from '@test/BalanceSyncBridge/helpers/function'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlMockInstance, BalanceSyncBridgeInstance, IBCTokenMockInstance } from '@test/common/types'
import { expect } from 'chai'

describe('recoverPacket', () => {
  let accounts: SignerWithAddress[]
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcTokenMock: IBCTokenMockInstance
  let accessCtrlMock: AccessCtrlMockInstance

  const setupFixture = async () => {
    ;({ accounts, balanceSyncBridge, ibcTokenMock, accessCtrlMock } =
      await contractFixture<BalanceSyncBridgeContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      accessCtrlMock.addAdminRole(accounts[0], 0, BASE.TRACE_ID)
    })

    describe('初期状態', () => {
      it('BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウントが更新されること', async () => {
        await balanceSyncBridgeFuncs.recoverPacket({ balanceSyncBridge })

        const bizZoneAccountData = await ibcTokenMock.getBalanceByZone(BASE.BRIDGE.ACCOUNT_B, BASE.ZONE.BIZ)
        await expect(bizZoneAccountData).to.be.equal(BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT))
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('ADMIN権限ではない場合、エラーが返却されること', async () => {
        const result = balanceSyncBridgeFuncs.recoverPacket({
          balanceSyncBridge,
          options: {
            privateKeyForSig: privateKey[1],
          },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.NOT_ADMIN_ROLE)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const result = balanceSyncBridgeFuncs.recoverPacket({
          balanceSyncBridge,
          options: {
            sig: ['0x12345678', ''],
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名が無効（not signature）の場合、エラーがスローされること', async () => {
        const bad_sig =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'

        const result = balanceSyncBridgeFuncs.recoverPacket({
          balanceSyncBridge,
          options: {
            sig: [bad_sig, ''],
          },
        })
        await expect(result).to.be.reverted // OpenZeppelin revertのため文字列がDCF仕様と異なる
      })
    })
  })
})
