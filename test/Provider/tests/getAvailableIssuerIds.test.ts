import '@nomicfoundation/hardhat-chai-matchers'
import assert from 'assert'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

describe('getAvailableIssuerIds()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer } = await contractFixture<ProviderContractType>())
    })

    describe('provider, role, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider, accounts })
        await providerFuncs.addProviderRole({ provider, accounts })
        await providerFuncs.addToken({ provider, accounts })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await issuerFuncs.addIssuerRole({ issuer, accounts })
        await issuerFuncs.addIssuer({
          issuer,
          accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            name: BASE.ISSUER.ISSUER1.NAME,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        await providerFuncs.addBizZone({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })
        await issuerFuncs.addBizZoneToIssuer({
          issuer,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            issuerId: BASE.ISSUER.ISSUER0.ID,
          },
        })
        await issuerFuncs.addBizZoneToIssuer({
          issuer,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            issuerId: BASE.ISSUER.ISSUER1.ID,
          },
        })
      })

      it('イシュアIDリストが取得できること', async () => {
        const result = await providerFuncs.getAvailableIssuerIds({ provider, options: [BASE.ZONE_ID.ID1] })
        assert.strictEqual(result.length, 2, 'count')
        assert.strictEqual(result[0], BASE.ISSUER.ISSUER0.ID, 'issuerId')
        assert.strictEqual(result[1], BASE.ISSUER.ISSUER1.ID, 'issuerId')
      })

      it('0件のイシュアIDリストが取得できること', async () => {
        const result = await providerFuncs.getAvailableIssuerIds({ provider, options: [BASE.ZONE_ID.ID0] })
        assert.equal(result.length, 0)
      })
    })
  })
})
