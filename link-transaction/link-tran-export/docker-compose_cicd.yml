version: '3.8'
services:
  db:
    image: postgres:15.3-alpine
    ports:
      - "15432:5432"
    environment:
      POSTGRES_USER: bpm_user
      POSTGRES_PASSWORD: bpm_password
      POSTGRES_DB: bpm_db
  # volumes を定義すると、GitHub Actions でのテスト実行時にエラーが発生するため、
  # テストコード内で下記ディレクトリに管理されているデータ投入ファイルを呼び出すようにしている。
  #    volumes:
  #      - ./bpm-local-env/bpm-db:/docker-entrypoint-initdb.d

  bpm-idp-mock:
    build:
      context: ../link-tran-export/bpm-local-env/idp-mock
      dockerfile: ../idp-mock/Dockerfile
    ports:
      - "8280:8280"

  minio:
    image: minio/minio:RELEASE.2021-04-06T23-11-00Z.hotfix.f3cd60697
    ports:
      - 9000:9000
    environment:
      - MINIO_ACCESS_KEY=access123
      - MINIO_SECRET_KEY=secret123
    entrypoint: sh
    command: -c "/usr/bin/minio server /data;"
#    volumes:
#      - ./bpm-local-env/minio:/data

  localstack:
    image: localstack/localstack:3.0.2
    environment:
      - SERVICES=secretsmanager,events
      - DEFAULT_REGION=ap-northeast-1
    ports:
      - "14566-14599:4566-4599"

  createbuckets:
    image: minio/mc
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      until (/usr/bin/mc config host add localminio http://minio:9000 access123 secret123) do echo '...waiting...' && sleep 1; done;
      /usr/bin/mc mb localminio/localbucket;
      /usr/bin/mc policy download localminio/localbucket;
      exit 0;
      "