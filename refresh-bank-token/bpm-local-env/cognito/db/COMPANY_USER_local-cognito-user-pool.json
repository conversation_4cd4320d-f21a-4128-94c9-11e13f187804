{"Users": {"SID01AA-1001": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01001"}, {"Name": "phone_number", "Value": "+817012341001"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1001", "UserStatus": "CONFIRMED"}, "SID01AA-1011": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01011"}, {"Name": "phone_number", "Value": "+817012341011"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1011", "UserStatus": "CONFIRMED"}, "SID01AA-1012": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01012"}, {"Name": "phone_number", "Value": "+817012341012"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AB-1012", "UserStatus": "CONFIRMED"}, "SID01AA-1013": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01013"}, {"Name": "phone_number", "Value": "+817012341013"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1013", "UserStatus": "CONFIRMED"}, "SID01AA-1021": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01021"}, {"Name": "phone_number", "Value": "+817012341021"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1021", "UserStatus": "CONFIRMED"}, "SID01AA-1022": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01022"}, {"Name": "phone_number", "Value": "+817012341022"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1022", "UserStatus": "CONFIRMED"}, "SID01AA-1023": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01023"}, {"Name": "phone_number", "Value": "+817012341023"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1023", "UserStatus": "CONFIRMED"}, "SID01AA-1031": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01031"}, {"Name": "phone_number", "Value": "+817012341031"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1031", "UserStatus": "CONFIRMED"}, "SID01AA-1032": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01032"}, {"Name": "phone_number", "Value": "+817012341032"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1032", "UserStatus": "CONFIRMED"}, "SID01AA-1033": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01033"}, {"Name": "phone_number", "Value": "+817012341033"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1033", "UserStatus": "CONFIRMED"}, "SID01AA-1411": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01141"}, {"Name": "phone_number", "Value": "+817012341411"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1411", "UserStatus": "CONFIRMED"}, "SID01AA-1421": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01241"}, {"Name": "phone_number", "Value": "+817012341421"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1421", "UserStatus": "CONFIRMED"}, "SID01AA-1422": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01242"}, {"Name": "phone_number", "Value": "+817012341422"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1422", "UserStatus": "CONFIRMED"}, "SID01AA-1431": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01341"}, {"Name": "phone_number", "Value": "+817012341431"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1431", "UserStatus": "CONFIRMED"}, "SID01AA-1521": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01251"}, {"Name": "phone_number", "Value": "+817012341521"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1521", "UserStatus": "CONFIRMED"}, "SID01AA-1522": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01252"}, {"Name": "phone_number", "Value": "+817012341522"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1522", "UserStatus": "CONFIRMED"}, "SID01AA-1523": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01253"}, {"Name": "phone_number", "Value": "+817012341523"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1523", "UserStatus": "CONFIRMED"}, "SID01AA-1711": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01171"}, {"Name": "phone_number", "Value": "+817012341711"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1711", "UserStatus": "CONFIRMED"}, "SID01AA-1721": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01271"}, {"Name": "phone_number", "Value": "+817012341721"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1721", "UserStatus": "CONFIRMED"}, "SID01AA-1722": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01272"}, {"Name": "phone_number", "Value": "+817012341722"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1722", "UserStatus": "CONFIRMED"}, "SID01AA-1731": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01371"}, {"Name": "phone_number", "Value": "+817012341731"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AA-1731", "UserStatus": "CONFIRMED"}, "SID01AB-2001": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01002"}, {"Name": "phone_number", "Value": "+817012342001"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AB-2001", "UserStatus": "CONFIRMED"}, "SID01AB-2011": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01102"}, {"Name": "phone_number", "Value": "+817012342011"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AB-2011", "UserStatus": "CONFIRMED"}, "SID01AB-2021": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01202"}, {"Name": "phone_number", "Value": "+817012342021"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AB-2021", "UserStatus": "CONFIRMED"}, "SID01AB-2031": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01302"}, {"Name": "phone_number", "Value": "+817012342031"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AB-2031", "UserStatus": "CONFIRMED"}, "SID01AC-3001": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01003"}, {"Name": "phone_number", "Value": "+817012343001"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AC-3001", "UserStatus": "CONFIRMED"}, "SID01AC-3011": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01103"}, {"Name": "phone_number", "Value": "+817012343011"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AC-3011", "UserStatus": "CONFIRMED"}, "SID01AC-3021": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01203"}, {"Name": "phone_number", "Value": "+817012343021"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AC-3021", "UserStatus": "CONFIRMED"}, "SID01AC-3031": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d01303"}, {"Name": "phone_number", "Value": "+817012343031"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID01AC-3031", "UserStatus": "CONFIRMED"}, "SID0100-4001": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce952d04001"}, {"Name": "phone_number", "Value": "+817012344001"}, {"Name": "email", "Value": "<EMAIL>"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID0100-4001", "UserStatus": "CONFIRMED"}, "SID21AA-1001": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce950021001"}, {"Name": "phone_number", "Value": "+817212341001"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID21AA-1001", "UserStatus": "UNCONFIRMED"}, "SID21AA-1002": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce950021002"}, {"Name": "phone_number", "Value": "+817212341002"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID21AA-1002", "UserStatus": "RESET_REQUIRED"}, "SID21AA-1003": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce950021003"}, {"Name": "phone_number", "Value": "+817212341003"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID21AA-1003", "UserStatus": "FORCE_CHANGE_PASSWORD"}, "SID21AA-1004": {"Attributes": [{"Name": "sub", "Value": "4ac6de79-7e8c-4080-87f5-4ce950021004"}, {"Name": "phone_number", "Value": "+817212341004"}], "Enabled": true, "Password": "Password1", "RefreshTokens": [], "UserCreateDate": "2023-12-03T10:57:55.589Z", "UserLastModifiedDate": "2023-12-03T10:57:55.589Z", "Username": "SID21AA-1004", "UserStatus": "UNKNOWN"}}, "Options": {"Policies": {"PasswordPolicy": {"MinimumLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireNumbers": true, "RequireSymbols": true, "TemporaryPasswordValidityDays": 7}}, "LambdaConfig": {}, "SchemaAttributes": [{"Name": "sub", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": false, "Required": true, "StringAttributeConstraints": {"MinLength": "1", "MaxLength": "2048"}}, {"Name": "name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "given_name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "family_name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "middle_name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "nickname", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "preferred_username", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "profile", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "picture", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "website", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "email", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "email_verified", "AttributeDataType": "Boolean", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false}, {"Name": "gender", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "birthdate", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "10", "MaxLength": "10"}}, {"Name": "zoneinfo", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "locale", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "phone_number", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "phone_number_verified", "AttributeDataType": "Boolean", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false}, {"Name": "address", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "updated_at", "AttributeDataType": "Number", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "NumberAttributeConstraints": {"MinValue": "0"}}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE"}, "MfaConfiguration": "OFF", "EstimatedNumberOfUsers": 0, "EmailConfiguration": {"EmailSendingAccount": "COGNITO_DEFAULT"}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false, "UnusedAccountValidityDays": 7}, "UsernameAttributes": ["email"], "Id": "COMPANY_USER_local-cognito-user-pool"}}