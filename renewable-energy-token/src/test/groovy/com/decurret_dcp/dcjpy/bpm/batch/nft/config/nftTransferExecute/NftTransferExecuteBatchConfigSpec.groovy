package com.decurret_dcp.dcjpy.bpm.batch.nft.config.nftTransferExecute

import com.decurret_dcp.dcjpy.bpm.batch.helper.AdhocHelper
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.springframework.batch.core.Job
import org.springframework.batch.core.JobParametersBuilder
import org.springframework.batch.core.scope.context.StepSynchronizationManager
import org.springframework.batch.test.JobLauncherTestUtils
import org.springframework.batch.test.MetaDataInstanceFactory
import org.springframework.batch.test.context.SpringBatchTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.batch.JobLauncherApplicationRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import software.amazon.awssdk.services.s3.model.S3Object
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@SpringBatchTest
@SpringBootTest
@ContextConfiguration
class NftTransferExecuteBatchConfigSpec extends Specification {

    static ObjectMapper mapper = new ObjectMapper()

    static Sql sql

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockBpm

    @MockBean
    JobLauncherApplicationRunner jobLauncherApplicationRunner

    @Autowired
    Job transferNftJob

    @Autowired
    JobLauncherTestUtils jobLauncherTestUtils

    static String LOCAL_BUCKETS_NAME = "localbucket"

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        registry.add("bpm.batch.nft.user-type", () -> "service_user")
        registry.add("bpm.batch.nft.job-type", () -> "nft-transfer-exec")
        registry.add("local.server.port", () -> "8080")
    }

    def setupSpec() {
        wiremockBpm = new WireMockServer(options().port(8180))
        wiremockBpm.start()
    }

    def cleanupSpec() {
        wiremockBpm.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        // Do nothing.
    }

    def cleanup() {
        sql.execute("""
            DELETE FROM service_user_file_task
        """)
        sql.execute("""
            DELETE FROM service_user_file_task_detail
        """)
        sql.execute("""
            DELETE FROM dc_account_settlement
        """)
        sql.execute("""
            DELETE FROM batch_step_execution_context
        """)
        sql.execute("""
            DELETE FROM batch_step_execution
        """)


        wiremockBpm.resetAll()
        AdhocHelper.deleteAllS3Object()
    }

    void postCheckTransfer(String fromDcBankNumber, int amount) {
        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer/check"))
                .withRequestBody(matchingJsonPath("transfer_amount", equalTo("$amount")))
                .withRequestBody(matchingJsonPath("from_dc_bank_number", equalTo("$fromDcBankNumber")))
                .willReturn(
                        okJson("""
                          {
                            "trasfer_amount" : $amount,
                            "from_account": {
                                "dc_bank_number": "$fromDcBankNumber"
                            },
                            "to_account": {
                                "dc_bank_number": "DC001-1234-1234-1",
                                "account_name" : "accountName"
                            },
                            "memo" : "memo",
                            "extra_info": {
                              "misc_value1" : "renewable",
                              "misc_value2" : "dummy_token_id"
                            }
                          }
                        """)
                )
        )
    }

    void postCheckNftTransactionSuccess() {
        wiremockBpm.stubFor(post(urlEqualTo("/services/nft/transfer/check?nft_id=renewable_energy_token"))
                .willReturn(ok())
        )
    }

    void postTransferSuccess(String dcBankNumber, int transferAmount, String nftTokenId) {
        def extraInfo = Map.ofEntries(
                Map.entry("misc_value1", "renewable"),
                Map.entry("misc_value2", nftTokenId)
        )

        def requestBodyMap = Map.ofEntries(
                Map.entry("request_id", "\${json-unit.any-string}"),
                Map.entry("transfer_amount", transferAmount),
                Map.entry("from_dc_bank_number", dcBankNumber),
                Map.entry("to_dc_bank_number", "DC001-1234-1234-1"),
                Map.entry("extra_info", extraInfo)
        )

        def requestBodyString = mapper.writeValueAsString(requestBodyMap)
        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer"))
                .withRequestBody(equalToJson(requestBodyString, true, true))
                .willReturn(
                        okJson("""
                          {
                            "transfer_amount" : $transferAmount,
                            "from_account" : {
                              "dc_bank_number" : "$dcBankNumber",
                              "account_name" : "fromAccountName"
                            },
                            "to_account" : {
                              "dc_bank_number" : "DC001-1234-1234-1",
                              "account_name" : "toAccountName"
                            },
                            "extra_info" : {
                              "misc_value1" : "renewable",
                              "misc_value2" : "$nftTokenId"
                            }
                          }
                        """)
                )
        )
    }

    void postTransferBusinessError(String dcBankNumber, int transferAmount, String nftTokenId) {
        def extraInfo = Map.ofEntries(
                Map.entry("misc_value1", "renewable"),
                Map.entry("misc_value2", nftTokenId)
        )

        def requestBodyMap = Map.ofEntries(
                Map.entry("request_id", "\${json-unit.any-string}"),
                Map.entry("transfer_amount", transferAmount),
                Map.entry("from_dc_bank_number", dcBankNumber),
                Map.entry("to_dc_bank_number", "DC001-1234-1234-1"),
                Map.entry("extra_info", extraInfo)
        )

        def requestBodyString = mapper.writeValueAsString(requestBodyMap)
        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer"))
                .withRequestBody(equalToJson(requestBodyString, true, true))
                .willReturn(badRequest()
                        .withBody("""
                                {
                                    "message": "insufficient dc account balance.",
                                    "error_code": "EUE4400"
                                }
                        """)
                )
        )
    }

    void postTransferInternalServerError(String dcBankNumber, int transferAmount, String nftTokenId) {
        def extraInfo = Map.ofEntries(
                Map.entry("misc_value1", "renewable"),
                Map.entry("misc_value2", nftTokenId)
        )

        def requestBodyMap = Map.ofEntries(
                Map.entry("request_id", "\${json-unit.any-string}"),
                Map.entry("transfer_amount", transferAmount),
                Map.entry("from_dc_bank_number", dcBankNumber),
                Map.entry("to_dc_bank_number", "DC001-1234-1234-1"),
                Map.entry("extra_info", extraInfo)
        )

        def requestBodyString = mapper.writeValueAsString(requestBodyMap)
        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer"))
                .withRequestBody(equalToJson(requestBodyString, true, true))
                .willReturn(serverError()
                        .withBody("""
                                {
                                    "message": "Internal Server Error",
                                    "error_code": "EEE0001"
                                }
                        """)
                )
        )
    }

    static void insertServiceUserFileTask(
            String taskId,
            String taskStatus,
            String orderedAt,
            String signInId,
            String fileName,
            String taskOrderDetail
    ) {

        sql.execute("""
            INSERT INTO service_user_file_task
            (task_id, task_type, task_status, ordered_at, sign_in_id, file_name, task_order_detail, result_bucket, result_file_path)
            VALUES (
                '$taskId', 'nft_transfer', '$taskStatus', '$orderedAt', '$signInId', '$fileName', '$taskOrderDetail', NULL, NULL
            )
        """)

    }

    static void insertServiceUserFileTaskDetail(String taskId, int line, String taskStatus, String taskOrderDetail) {
        sql.execute("""
            INSERT INTO service_user_file_task_detail
            (task_id, line_number, task_detail_status, task_order_detail, task_result_detail)
            VALUES (${taskId}, ${line}, ${taskStatus}, ${taskOrderDetail}::jsonb, NULL)
        """)

    }

    static void insertServiceUserFileTaskDetailFailed(
            String taskId, int line, String taskStatus, String taskOrderDetail, String taskResultDetail
    ) {
        sql.execute("""
            INSERT INTO service_user_file_task_detail
            (task_id, line_number, task_detail_status, task_order_detail, task_result_detail)
            VALUES ($taskId, $line, $taskStatus, ${taskOrderDetail}::jsonb, ${taskResultDetail}::jsonb)
        """)
    }

    static void insertDcAccount(String dcBankNumber) {
        sql.execute("""
            DELETE FROM dc_account WHERE dc_bank_number = $dcBankNumber
        """)

        sql.execute("""
            INSERT INTO dc_account
            (dc_bank_number, service_id, account_id, core_linked)
            VALUES ($dcBankNumber, '0', '60AAAAABBBBBAAAAABBBBBAAAAABBBBB', 'true')
        """)
    }

    static void insertSettlement(String dcBankNumber, String settlementType, Short scheduledDay) {
        sql.execute("""
            DELETE FROM dc_account WHERE dc_bank_number = $dcBankNumber
        """)

        sql.execute("""
            INSERT INTO dc_account
            (dc_bank_number, service_id, account_id, core_linked)
            VALUES ($dcBankNumber, '0', '60AAAAABBBBBAAAAABBBBBAAAAABBBBB', 'true')
        """)

        sql.execute("""
            INSERT INTO dc_account_settlement
            (dc_bank_number, service_id, settlement_type, scheduled_day)
            VALUES ($dcBankNumber, '0', $settlementType, $scheduledDay)
        """)
    }


    def "nftTransferExecBatch_1_1_バッチプログラムより起動して移転が成功し、結果ファイル保存、実行結果レコード更新ができること。"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "1_1"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_1_1.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_1_1.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber2, transferAmount2)
        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 4
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "completed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_1_1.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == transferAmount2.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","1000","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_1_2_オートチャージより起動して移転が成功し、結果ファイル保存、実行結果レコード更新ができること。"() {
        setup:
        //テストデータの設定
        def s3Key = "input/service_user/auto_charge/2024/02/********-060504301_1_2.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer_execute/nftTransferExecuteBatch_1_2.csv"
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "1_2"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_1_2.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_1_2.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, 10)
        postCheckTransfer(toDcBankNumber2, 1000)
        postCheckTransfer(toDcBankNumber3, 100)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 6
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckHeaderFormatStep_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "CheckDataFormatStep_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"
        step_row.get(4).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(4).get("exit_code").toString() == "COMPLETED"
        step_row.get(5).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(5).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "completed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_1_2.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == transferAmount2.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","1000","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_1_3_バッチプログラムより起動して実行大量件数が正常に実行されること"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "1_3"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_1_1.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_1_1.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, 10)
        postCheckTransfer(toDcBankNumber3, 100)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録(101件登録)
        for (line in 1..51) {
            insertServiceUserFileTaskDetail(taskId, line, taskStatus, taskOrderDetail_1)
        }
        for (line in 52..101) {
            insertServiceUserFileTaskDetail(taskId, line, taskStatus, taskOrderDetail_3)
        }

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(101, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(101, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(101, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "completed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_1_3.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 101
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail2.get("nft_token_id").asText() == nftTokenId1
        resultDetail2.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail3.get("nft_token_id").asText() == nftTokenId1
        resultDetail3.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail3.get("settlement_type").asText() == "immediate"
        resultDetail3.get("scheduled_day") == null

        detailRows.get(99).get("task_id").toString() == taskId
        detailRows.get(99).get("line_number").toString() == "100"
        detailRows.get(99).get("task_detail_status").toString() == "completed"
        detailRows.get(99).get("task_order_detail") != null
        def resultDetail100 = mapper.readTree(detailRows.get(99).get("task_result_detail").toString())
        resultDetail100.get("task_result").asText() == "success"
        resultDetail100.get("error_message").asText() == ""
        resultDetail100.get("file_type").asText() == "nft_transfer"
        resultDetail100.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail100.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail100.get("nft_token_id").asText() == nftTokenId3
        resultDetail100.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail100.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail100.get("scheduled_day").asText() == "10"

        detailRows.get(100).get("task_id").toString() == taskId
        detailRows.get(100).get("line_number").toString() == "101"
        detailRows.get(100).get("task_detail_status").toString() == "completed"
        detailRows.get(100).get("task_order_detail") != null
        def resultDetail101 = mapper.readTree(detailRows.get(100).get("task_result_detail").toString())
        resultDetail101.get("task_result").asText() == "success"
        resultDetail101.get("error_message").asText() == ""
        resultDetail101.get("file_type").asText() == "nft_transfer"
        resultDetail101.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail101.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail101.get("nft_token_id").asText() == nftTokenId3
        resultDetail101.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail101.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail101.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 101
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(99) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'
        fileValues.get(100) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_1_４_オートチャージより起動して大量件数が正常に処理されること。"() {
        setup:
        //テストデータの設定
        def s3Key = "input/service_user/auto_charge/2024/02/********-060504301_1_4.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer_execute/nftTransferExecuteBatch_1_4.csv"
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "1_4"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_1_4.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_1_4.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録(101件登録)
        for (line in 1..51) {
            insertServiceUserFileTaskDetail(taskId, line, taskStatus, taskOrderDetail_1)
        }
        for (line in 52..101) {
            insertServiceUserFileTaskDetail(taskId, line, taskStatus, taskOrderDetail_3)
        }

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(101, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(101, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(101, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 6
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckHeaderFormatStep_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "CheckDataFormatStep_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"
        step_row.get(4).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(4).get("exit_code").toString() == "COMPLETED"
        step_row.get(5).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(5).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "completed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_1_4.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 101
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail2.get("nft_token_id").asText() == nftTokenId1
        resultDetail2.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail3.get("nft_token_id").asText() == nftTokenId1
        resultDetail3.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail3.get("settlement_type").asText() == "immediate"
        resultDetail3.get("scheduled_day") == null


        detailRows.get(99).get("task_id").toString() == taskId
        detailRows.get(99).get("line_number").toString() == "100"
        detailRows.get(99).get("task_detail_status").toString() == "completed"
        detailRows.get(99).get("task_order_detail") != null
        def resultDetail100 = mapper.readTree(detailRows.get(99).get("task_result_detail").toString())
        resultDetail100.get("task_result").asText() == "success"
        resultDetail100.get("error_message").asText() == ""
        resultDetail100.get("file_type").asText() == "nft_transfer"
        resultDetail100.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail100.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail100.get("nft_token_id").asText() == nftTokenId3
        resultDetail100.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail100.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail100.get("scheduled_day").asText() == "10"

        detailRows.get(100).get("task_id").toString() == taskId
        detailRows.get(100).get("line_number").toString() == "101"
        detailRows.get(100).get("task_detail_status").toString() == "completed"
        detailRows.get(100).get("task_order_detail") != null
        def resultDetail101 = mapper.readTree(detailRows.get(100).get("task_result_detail").toString())
        resultDetail101.get("task_result").asText() == "success"
        resultDetail101.get("error_message").asText() == ""
        resultDetail101.get("file_type").asText() == "nft_transfer"
        resultDetail101.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail101.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail101.get("nft_token_id").asText() == nftTokenId3
        resultDetail101.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail101.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail101.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 101
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(99) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'
        fileValues.get(100) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_1_5_オートチャージありとなしの明細がある場合にどちらも正常に移転が完了すること"() {
        setup:
        //テストデータの設定
        def s3Key = "input/service_user/auto_charge/2024/02/********-060504301_1_5.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer_execute/nftTransferExecuteBatch_1_5.csv"
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "1_5"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_1_5.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_1_5.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber2, transferAmount2)
        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()

        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 6
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckHeaderFormatStep_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "CheckDataFormatStep_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"
        step_row.get(4).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(4).get("exit_code").toString() == "COMPLETED"
        step_row.get(5).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(5).get("exit_code").toString() == "COMPLETED"


        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "completed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_1_5.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == transferAmount2.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","1000","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_1_6_移転準備段階でエラーとなっている明細についてはスキップされ、それ以外は実行結果レコード更新ができること。"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def toDcBankNumber4 = "DC001-4444-1104-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount4 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId4 = "31000004444400000444440000044444"

        def taskId = "1_6"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_1_6.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_1_6.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'
        def taskResultDetail_3 = '{"task_result": "EEE0001","error_message":"Internal Server Error"}'
        def taskOrderDetail_4 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000004444400000444440000044444", "transfer_amount": 100, "to_dc_bank_number": "DC001-4444-1104-1", "renewable_energy_id": "dummy_energy_id4"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-4444-1104-1\\",\\"dummy_energy_id4\\",\\"31000004444400000444440000044444\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber2, transferAmount2)
        postCheckTransfer(toDcBankNumber4, transferAmount4)
        postCheckNftTransactionSuccess()

        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber4, transferAmount4, nftTokenId4)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetailFailed(taskId, 3, "failed", taskOrderDetail_3, taskResultDetail_3)
        insertServiceUserFileTaskDetail(taskId, 4, taskStatus, taskOrderDetail_4)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))
        insertSettlement(toDcBankNumber4, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 4
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"


        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_1_6.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 4
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == transferAmount2.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "failed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "EEE0001"
        resultDetail3.get("error_message").asText() == "Internal Server Error"

        detailRows.get(3).get("task_id").toString() == taskId
        detailRows.get(3).get("line_number").toString() == "4"
        detailRows.get(3).get("task_detail_status").toString() == "completed"
        detailRows.get(3).get("task_order_detail") != null
        def resultDetail4 = mapper.readTree(detailRows.get(3).get("task_result_detail").toString())
        resultDetail4.get("task_result").asText() == "success"
        resultDetail4.get("error_message").asText() == ""
        resultDetail4.get("file_type").asText() == "nft_transfer"
        resultDetail4.get("dc_bank_number").asText() == toDcBankNumber4
        resultDetail4.get("renewable_energy_id").asText() == "dummy_energy_id4"
        resultDetail4.get("nft_token_id").asText() == nftTokenId4
        resultDetail4.get("transfer_amount").asText() == transferAmount4.toString()
        resultDetail4.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail4.get("scheduled_day").asText() == "10"


        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 4
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","1000","immediate","0"'
        fileValues.get(2) == '"EEE0001","Internal Server Error","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","0","",""'
        fileValues.get(3) == '"success","","nft_transfer:renewable_energy_token","DC001-4444-1104-1","dummy_energy_id4","31000004444400000444440000044444","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_1_7_複数トークンの移転の場合、移転が成功し、結果ファイル保存、実行結果レコード更新ができること。"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111:31000002222200000222220000022222"
        def nftTokenId2 = "31000003333300000333330000033333:31000004444400000444440000044444"
        def nftTokenId3 = "31000005555500000555550000055555:31000006666600000666660000066666"

        def taskId = "1_7"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_1_7.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_1_7.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111:31000002222200000222220000022222", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "TEST-EVT-00001:TEST-EVT-00002"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"TEST-EVT-00001:TEST-EVT-00002\\",\\"31000001111100000111110000011111:31000002222200000222220000022222\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000003333300000333330000033333:31000004444400000444440000044444", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "TEST-EVT-00003:TEST-EVT-00004"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"TEST-EVT-00003:TEST-EVT-00004\\",\\"31000003333300000333330000033333:31000004444400000444440000044444\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000005555500000555550000055555:31000006666600000666660000066666", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "TEST-EVT-00005:TEST-EVT-00006"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"TEST-EVT-00005:TEST-EVT-00006\\",\\"31000005555500000555550000055555:31000006666600000666660000066666\\",\\"100\\"", "error_code": ""}'

        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer/check"))
                .withRequestBody(matchingJsonPath("transfer_amount", equalTo("10")))
                .withRequestBody(matchingJsonPath("from_dc_bank_number", equalTo("$toDcBankNumber1")))
                .withRequestBody(matchingJsonPath("extra_info.misc_value2", equalTo("31000001111100000111110000011111,31000002222200000222220000022222")))
                .willReturn(
                        okJson("""
                          {
                            "trasfer_amount" : 10,
                            "from_account": {
                                "dc_bank_number": "$toDcBankNumber1"
                            },
                            "to_account": {
                                "dc_bank_number": "DC001-1234-1234-1",
                                "account_name" : "accountName"
                            },
                            "memo" : "memo",
                            "extra_info": {
                              "misc_value1" : "renewable",
                              "misc_value2" : "31000001111100000111110000011111,31000002222200000222220000022222"
                            }
                          }
                        """)
                )
        )
        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer/check"))
                .withRequestBody(matchingJsonPath("transfer_amount", equalTo("1000")))
                .withRequestBody(matchingJsonPath("from_dc_bank_number", equalTo("$toDcBankNumber2")))
                .withRequestBody(matchingJsonPath("extra_info.misc_value2", equalTo("31000003333300000333330000033333,31000004444400000444440000044444")))
                .willReturn(
                        okJson("""
                          {
                            "trasfer_amount" : 1000,
                            "from_account": {
                                "dc_bank_number": "$toDcBankNumber2"
                            },
                            "to_account": {
                                "dc_bank_number": "DC001-1234-1234-1",
                                "account_name" : "accountName"
                            },
                            "memo" : "memo",
                            "extra_info": {
                              "misc_value1" : "renewable",
                              "misc_value2" : "31000003333300000333330000033333,31000004444400000444440000044444"
                            }
                          }
                        """)
                )
        )
        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer/check"))
                .withRequestBody(matchingJsonPath("transfer_amount", equalTo("100")))
                .withRequestBody(matchingJsonPath("from_dc_bank_number", equalTo("$toDcBankNumber3")))
                .withRequestBody(matchingJsonPath("extra_info.misc_value2", equalTo("31000005555500000555550000055555,31000006666600000666660000066666")))
                .willReturn(
                        okJson("""
                          {
                            "trasfer_amount" : 100,
                            "from_account": {
                                "dc_bank_number": "$toDcBankNumber3"
                            },
                            "to_account": {
                                "dc_bank_number": "DC001-1234-1234-1",
                                "account_name" : "accountName"
                            },
                            "memo" : "memo",
                            "extra_info": {
                              "misc_value1" : "renewable",
                              "misc_value2" : "31000005555500000555550000055555,31000006666600000666660000066666"
                            }
                          }
                        """)
                )
        )
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1.replace(":",","))
        postTransferSuccess(toDcBankNumber2, transferAmount2, nftTokenId2.replace(":",","))
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3.replace(":",","))

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 4
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "completed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_1_7.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "TEST-EVT-00001:TEST-EVT-00002"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "TEST-EVT-00003:TEST-EVT-00004"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == transferAmount2.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "TEST-EVT-00005:TEST-EVT-00006"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","TEST-EVT-00001:TEST-EVT-00002","31000001111100000111110000011111:31000002222200000222220000022222","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","TEST-EVT-00003:TEST-EVT-00004","31000003333300000333330000033333:31000004444400000444440000044444","1000","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","TEST-EVT-00005:TEST-EVT-00006","31000005555500000555550000055555:31000006666600000666660000066666","100","monthly_scheduled","10"'
    }

    def "nftTransferExecBatch_2_1_オートチャージ結果ファイルのフォーマットが不正でも、後続処理は実行されること(ヘッダー部分)"() {
        setup:
        //テストデータの設定
        def s3Key = "input/service_user/auto_charge/2024/02/********-060504301_2_1.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer_execute/nftTransferExecuteBatch_2_1.csv"
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"

        def taskId = "2_1"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_2_1.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_2_1.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "FAILED"
        wiremockBpm.verify(0, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(0, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(0, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 2
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckHeaderFormatStep_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "FAILED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket") == null
        rows.get(0).get("result_file_path") == null
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") == null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "accept"
        detailRows.get(0).get("task_order_detail") != null
        detailRows.get(0).get("task_result_detail") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "accept"
        detailRows.get(1).get("task_order_detail") != null
        detailRows.get(1).get("task_result_detail") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "accept"
        detailRows.get(2).get("task_order_detail") != null
        detailRows.get(2).get("task_result_detail") == null

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 0
    }

    def "nftTransferExecBatch_2_2_オートチャージ結果ファイルのフォーマットが不正でも、後続処理は実行されること(データ部分)"() {
        setup:
        //テストデータの設定
        def s3Key = "input/service_user/auto_charge/2024/02/********-060504301_2_2.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer_execute/nftTransferExecuteBatch_2_2.csv"
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "2_2"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_2_2.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_2_1.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber2, transferAmount2)
        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 6
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckHeaderFormatStep_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "CheckDataFormatStep_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"
        step_row.get(4).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(4).get("exit_code").toString() == "COMPLETED"
        step_row.get(5).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(5).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "completed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_2_2.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "completed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == transferAmount1.toString()
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == transferAmount2.toString()
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","10","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","1000","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_3_1_バッチプログラムより起動して移転でエラーになった明細がある場合に適切に処理されること"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "3_1"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_3_1.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_3_1.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber2, transferAmount2)
        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()

        postTransferBusinessError(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferInternalServerError(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 4
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_3_1.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "failed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "EUE4400"
        resultDetail1.get("error_message").asText() == "insufficient dc account balance."
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == "0"
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "failed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "EEE0001"
        resultDetail2.get("error_message").asText() == "Internal Server Error"
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == "0"
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"EUE4400","insufficient dc account balance.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","0","immediate","0"'
        fileValues.get(1) == '"EEE0001","Internal Server Error","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","0","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_3_2_移転先のDC口座番号にサービスオーナー自身のアカウントを指定した場合、明細が適切に処理されること。"() {
        setup:
        //テストデータの設定
        def s3Key = "input/service_user/auto_charge/2024/02/********-060504301_3_2.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer_execute/nftTransferExecuteBatch_3_2.csv"
        def toDcBankNumber1 = "DC001-1234-1234-1"
        def toDcBankNumber2 = "DC001-1234-1234-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "3_2"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_3_2.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_3_2.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-1234-1234-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-1234-1234-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-1234-1234-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-1234-1234-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertDcAccount(toDcBankNumber1) // toDcBankNumber1 は精算条件の設定がなしで登録する
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(1, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(1, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(1, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 6
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckHeaderFormatStep_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "CheckDataFormatStep_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"
        step_row.get(4).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(4).get("exit_code").toString() == "COMPLETED"
        step_row.get(5).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(5).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_3_2.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "failed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "EUE2001"
        resultDetail1.get("error_message").asText() == "cannot send to the specified dc bank number."
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == "0"
        resultDetail1.get("settlement_type") == null
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "failed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "EUE2001"
        resultDetail1.get("error_message").asText() == "cannot send to the specified dc bank number."
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == "0"
        resultDetail2.get("settlement_type") == null
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"EUE2001","cannot send to the specified dc bank number.","nft_transfer:renewable_energy_token","DC001-1234-1234-1","dummy_energy_id1","31000001111100000111110000011111","0","",""'
        fileValues.get(1) == '"EUE2001","cannot send to the specified dc bank number.","nft_transfer:renewable_energy_token","DC001-1234-1234-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","0","",""'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'
    }

    def "nftTransferExecBatch_3_3_オートチャージが失敗した明細はSkipされること、結果ファイル保存、実行結果レコード更新ができること。"() {
        setup:
        //テストデータの設定
        def s3Key = "input/service_user/auto_charge/2024/02/********-060504301_3_3.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer_execute/nftTransferExecuteBatch_3_3.csv"
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "3_3"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_3_2.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_3_2.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(1, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(1, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(1, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 6
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckHeaderFormatStep_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "CheckDataFormatStep_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"
        step_row.get(4).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(4).get("exit_code").toString() == "COMPLETED"
        step_row.get(5).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(5).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_3_3.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "failed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "EUE4000"
        resultDetail1.get("error_message").asText() == "amount is exceeded mint limit."
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == "0"
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "failed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "EUE4000"
        resultDetail2.get("error_message").asText() == "amount is exceeded mint limit."
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == "0"
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"EUE4000","amount is exceeded mint limit.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","0","immediate","0"'
        fileValues.get(1) == '"EUE4000","amount is exceeded mint limit.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","0","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'
    }

    def "nftTransferExecBatch_3_4_ファイルタスクのレコードがテーブルに存在しない場合、ジョブが異常終了すること"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "3_4"

        postCheckNftTransactionSuccess()

        postTransferSuccess(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "FAILED"
        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 1
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "FAILED"

        wiremockBpm.verify(0, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 0

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 0

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 0
    }

    def "nftTransferExecBatch_3_5_バッチプログラムより起動して移転 確認でエラーになった明細がある場合に適切に処理されること"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "3_5"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_3_5.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_3_5.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer/check"))
                .withRequestBody(matchingJsonPath("transfer_amount", equalTo("$transferAmount1")))
                .withRequestBody(matchingJsonPath("from_dc_bank_number", equalTo("$toDcBankNumber1")))
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": "sending account status is invalid.",
                                    "error_code": "EUE2000"
                                }
                                """)
                )
        )
        wiremockBpm.stubFor(post(urlEqualTo("/batch/transactions/transfer/check"))
                .withRequestBody(matchingJsonPath("transfer_amount", equalTo("$transferAmount2")))
                .withRequestBody(matchingJsonPath("from_dc_bank_number", equalTo("$toDcBankNumber2")))
                .willReturn(serverError()
                        .withBody("""
                                {
                                    "message": "Internal Server Error",
                                    "error_code": "EEE0001"
                                }
                        """)
                )
        )
        postCheckTransfer(toDcBankNumber3, transferAmount3)
        postCheckNftTransactionSuccess()
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(1, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(1, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 4
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_3_5.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "failed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "EUE2000"
        resultDetail1.get("error_message").asText() == "sending account status is invalid."
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == "0"
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "failed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "EEE0001"
        resultDetail2.get("error_message").asText() == "Internal Server Error"
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == "0"
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"EUE2000","sending account status is invalid.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","0","immediate","0"'
        fileValues.get(1) == '"EEE0001","Internal Server Error","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","0","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_3_6_バッチプログラムより起動してNFT 移転受付でエラーになった明細がある場合に適切に処理されること"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount3 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"

        def taskId = "3_6"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_3_6.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_3_6.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id1"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id1\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "dummy_energy_id2"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"dummy_energy_id2\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "dummy_energy_id3"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"dummy_energy_id3\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber2, transferAmount2)
        postCheckTransfer(toDcBankNumber3, transferAmount3)
        wiremockBpm.stubFor(post(urlEqualTo("/services/nft/transfer/check?nft_id=renewable_energy_token"))
                .withRequestBody(matchingJsonPath("to_dc_bank_number", equalTo("$toDcBankNumber1")))
                .withRequestBody(matchingJsonPath("nft_token_id", equalTo("$nftTokenId1")))
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": "resource is not found.",
                                    "error_code": "EGE0100"
                                }
                                """)
                )
        )
        wiremockBpm.stubFor(post(urlEqualTo("/services/nft/transfer/check?nft_id=renewable_energy_token"))
                .withRequestBody(matchingJsonPath("to_dc_bank_number", equalTo("$toDcBankNumber2")))
                .withRequestBody(matchingJsonPath("nft_token_id", equalTo("$nftTokenId2")))
                .willReturn(serverError()
                        .withBody("""
                                {
                                    "message": "Internal Server Error",
                                    "error_code": "EEE0001"
                                }
                        """)
                )
        )
        wiremockBpm.stubFor(post(urlEqualTo("/services/nft/transfer/check?nft_id=renewable_energy_token"))
                .withRequestBody(matchingJsonPath("to_dc_bank_number", equalTo("$toDcBankNumber3")))
                .withRequestBody(matchingJsonPath("nft_token_id", equalTo("$nftTokenId3")))
                .willReturn(ok())
        )
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetail(taskId, 3, taskStatus, taskOrderDetail_3)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(1, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 4
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_3_6.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 3
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "failed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "EGE0100"
        resultDetail1.get("error_message").asText() == "resource is not found."
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "dummy_energy_id1"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == "0"
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "failed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "EEE0001"
        resultDetail2.get("error_message").asText() == "Internal Server Error"
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "dummy_energy_id2"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == "0"
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "success"
        resultDetail3.get("error_message").asText() == ""
        resultDetail3.get("file_type").asText() == "nft_transfer"
        resultDetail3.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail3.get("renewable_energy_id").asText() == "dummy_energy_id3"
        resultDetail3.get("nft_token_id").asText() == nftTokenId3
        resultDetail3.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail3.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail3.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 3
        fileValues.get(0) == '"EGE0100","resource is not found.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id1","31000001111100000111110000011111","0","immediate","0"'
        fileValues.get(1) == '"EEE0001","Internal Server Error","nft_transfer:renewable_energy_token","DC001-2222-1102-1","dummy_energy_id2","3100000abcdefghijklmnopqrstuvwxy","0","immediate","0"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","dummy_energy_id3","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'

    }

    def "nftTransferExecBatch_3_7_移転準備段階でエラーとなっている明細があるかつ、移転でエラーになったレコードがある場合、それ以外は実行結果レコード更新ができること。"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber2 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def toDcBankNumber4 = "DC001-4444-1104-1"
        def transferAmount1 = 10
        def transferAmount2 = 1000
        def transferAmount4 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId2 = "3100000abcdefghijklmnopqrstuvwxy"
        def nftTokenId4 = "31000004444400000444440000044444"

        def taskId = "3_7"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_3_7.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_3_7.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "TEST-EVT-00001"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"TEST-EVT-00001\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_2 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnopqrstuvwxy", "transfer_amount": 1000, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "TEST-EVT-00002"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"TEST-EVT-00002\\",\\"3100000abcdefghijklmnopqrstuvwxy\\",\\"1000\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "TEST-EVT-00003"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"TEST-EVT-00003\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'
        def taskResultDetail_3 = '{"task_result": "EEE0001","error_message":"Internal Server Error"}'
        def taskOrderDetail_4 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000004444400000444440000044444", "transfer_amount": 100, "to_dc_bank_number": "DC001-4444-1104-1", "renewable_energy_id": "TEST-EVT-00004"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-4444-1104-1\\",\\"TEST-EVT-00004\\",\\"31000004444400000444440000044444\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, transferAmount1)
        postCheckTransfer(toDcBankNumber2, transferAmount2)
        postCheckTransfer(toDcBankNumber4, transferAmount4)
        postCheckNftTransactionSuccess()

        postTransferBusinessError(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferBusinessError(toDcBankNumber2, transferAmount2, nftTokenId2)
        postTransferSuccess(toDcBankNumber4, transferAmount4, nftTokenId4)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        insertServiceUserFileTaskDetail(taskId, 2, taskStatus, taskOrderDetail_2)
        insertServiceUserFileTaskDetailFailed(taskId, 3, "failed", taskOrderDetail_3, taskResultDetail_3)
        insertServiceUserFileTaskDetail(taskId, 4, taskStatus, taskOrderDetail_4)

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))
        insertSettlement(toDcBankNumber4, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(3, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(3, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 4
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferExecute"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "ExecuteNftTransfer_NftTransferExecute"
        step_row.get(1).get("exit_code").toString() == "COMPLETED"
        step_row.get(2).get("step_name").toString() == "ExportResultFileToS3_NftTransferExecute"
        step_row.get(2).get("exit_code").toString() == "COMPLETED"
        step_row.get(3).get("step_name").toString() == "CompleteFileTask_NftTransferExecute"
        step_row.get(3).get("exit_code").toString() == "COMPLETED"


        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_3_7.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 4
        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "failed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "EUE4400"
        resultDetail1.get("error_message").asText() == "insufficient dc account balance."
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail1.get("renewable_energy_id").asText() == "TEST-EVT-00001"
        resultDetail1.get("nft_token_id").asText() == nftTokenId1
        resultDetail1.get("transfer_amount").asText() == "0"
        resultDetail1.get("settlement_type").asText() == "immediate"
        resultDetail1.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "failed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "EUE4400"
        resultDetail2.get("error_message").asText() == "insufficient dc account balance."
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber2
        resultDetail2.get("renewable_energy_id").asText() == "TEST-EVT-00002"
        resultDetail2.get("nft_token_id").asText() == nftTokenId2
        resultDetail2.get("transfer_amount").asText() == "0"
        resultDetail2.get("settlement_type").asText() == "immediate"
        resultDetail2.get("scheduled_day") == null

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "failed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail3 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail3.get("task_result").asText() == "EEE0001"
        resultDetail3.get("error_message").asText() == "Internal Server Error"

        detailRows.get(3).get("task_id").toString() == taskId
        detailRows.get(3).get("line_number").toString() == "4"
        detailRows.get(3).get("task_detail_status").toString() == "completed"
        detailRows.get(3).get("task_order_detail") != null
        def resultDetail4 = mapper.readTree(detailRows.get(3).get("task_result_detail").toString())
        resultDetail4.get("task_result").asText() == "success"
        resultDetail4.get("error_message").asText() == ""
        resultDetail4.get("file_type").asText() == "nft_transfer"
        resultDetail4.get("dc_bank_number").asText() == toDcBankNumber4
        resultDetail4.get("renewable_energy_id").asText() == "TEST-EVT-00004"
        resultDetail4.get("nft_token_id").asText() == nftTokenId4
        resultDetail4.get("transfer_amount").asText() == transferAmount4.toString()
        resultDetail4.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail4.get("scheduled_day").asText() == "10"


        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 4
        fileValues.get(0) == '"EUE4400","insufficient dc account balance.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","TEST-EVT-00001","31000001111100000111110000011111","0","immediate","0"'
        fileValues.get(1) == '"EUE4400","insufficient dc account balance.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","TEST-EVT-00002","3100000abcdefghijklmnopqrstuvwxy","0","immediate","0"'
        fileValues.get(2) == '"EEE0001","Internal Server Error","nft_transfer:renewable_energy_token","DC001-3333-1103-1","TEST-EVT-00003","3100000abcdefghijklmnoPQRSTUVWXY","0","",""'
        fileValues.get(3) == '"success","","nft_transfer:renewable_energy_token","DC001-4444-1104-1","TEST-EVT-00004","31000004444400000444440000044444","100","monthly_scheduled","10"'
    }

    def "nftTransferExecBatch_3_8_大量件数の中に失敗したレコードが存在する場合、適切に処理がされていること"() {
        setup:
        //テストデータの設定
        def toDcBankNumber1 = "DC001-2222-1102-1"
        def toDcBankNumber3 = "DC001-3333-1103-1"
        def toDcBankNumber4 = "DC001-4444-1104-1"
        def transferAmount1 = 10
        def transferAmount3 = 100
        def transferAmount4 = 100
        def nftTokenId1 = "31000001111100000111110000011111"
        def nftTokenId3 = "3100000abcdefghijklmnoPQRSTUVWXY"
        def nftTokenId4 = "31000004444400000444440000044444"

        def taskId = "3_8"
        def taskStatus = "accept"
        def orderedAt = "2024-02-01T06:05:04.301+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "********-120000900_3_8.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/********-120000900_3_8.csv"}'

        def taskOrderDetail_1 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000001111100000111110000011111", "transfer_amount": 10, "to_dc_bank_number": "DC001-2222-1102-1", "renewable_energy_id": "TEST-EVT-00001"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-2222-1102-1\\",\\"TEST-EVT-00001\\",\\"31000001111100000111110000011111\\",\\"10\\"", "error_code": ""}'
        def taskOrderDetail_3 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "3100000abcdefghijklmnoPQRSTUVWXY", "transfer_amount": 100, "to_dc_bank_number": "DC001-3333-1103-1", "renewable_energy_id": "TEST-EVT-00003"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-3333-1103-1\\",\\"TEST-EVT-00003\\",\\"3100000abcdefghijklmnoPQRSTUVWXY\\",\\"100\\"", "error_code": ""}'
        def taskOrderDetail_4 = '{"record": {"file_type": "nft_transfer", "nft_token_id": "31000004444400000444440000044444", "transfer_amount": 100, "to_dc_bank_number": "DC001-4444-1104-1", "renewable_energy_id": "TEST-EVT-00004"}, "source": "\\"nft_transfer:renewable_energy_token\\",\\"DC001-4444-1104-1\\",\\"TEST-EVT-00004\\",\\"31000004444400000444440000044444\\",\\"100\\"", "error_code": ""}'

        postCheckTransfer(toDcBankNumber1, 10)
        postCheckTransfer(toDcBankNumber3, 100)
        postCheckTransfer(toDcBankNumber4, 100)
        postCheckNftTransactionSuccess()
        postTransferBusinessError(toDcBankNumber1, transferAmount1, nftTokenId1)
        postTransferSuccess(toDcBankNumber3, transferAmount3, nftTokenId3)
        postTransferSuccess(toDcBankNumber4, transferAmount4, nftTokenId4)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        //ServiceUserFileTaskDetailテーブルへの登録(102件登録)
        insertServiceUserFileTaskDetail(taskId, 1, taskStatus, taskOrderDetail_1)
        for (line in 2..52) {
            insertServiceUserFileTaskDetail(taskId, line, taskStatus, taskOrderDetail_3)
        }
        for (line in 53..102) {
            insertServiceUserFileTaskDetail(taskId, line, taskStatus, taskOrderDetail_4)
        }

        //DC口座精算設定テーブルへの登録
        insertSettlement(toDcBankNumber1, "immediate", null)
        insertSettlement(toDcBankNumber3, "monthly_scheduled", Short.valueOf("10"))
        insertSettlement(toDcBankNumber4, "monthly_scheduled", Short.valueOf("10"))

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("taskId", taskId)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "COMPLETED"
        wiremockBpm.verify(102, postRequestedFor(urlEqualTo("/batch/transactions/transfer/check")))
        wiremockBpm.verify(102, postRequestedFor(urlPathEqualTo("/services/nft/transfer/check"))
                .withQueryParam("nft_id", equalTo("renewable_energy_token")))
        wiremockBpm.verify(102, postRequestedFor(urlEqualTo("/batch/transactions/transfer")))

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("sign_in_id").toString() == signInId
        rows.get(0).get("file_name").toString() == fileName
        rows.get(0).get("result_bucket").toString() == "localbucket"
        rows.get(0).get("result_file_path").toString() == "output/service_user/file_task/nft_transfer/2024/02/********-060504301_3_8.csv"
        rows.get(0).get("task_result_detail") == null
        rows.get(0).get("completed_at") != null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 102

        detailRows.get(0).get("task_id").toString() == taskId
        detailRows.get(0).get("line_number").toString() == "1"
        detailRows.get(0).get("task_detail_status").toString() == "failed"
        detailRows.get(0).get("task_order_detail") != null
        def resultDetail0 = mapper.readTree(detailRows.get(0).get("task_result_detail").toString())
        resultDetail0.get("task_result").asText() == "EUE4400"
        resultDetail0.get("error_message").asText() == "insufficient dc account balance."
        resultDetail0.get("file_type").asText() == "nft_transfer"
        resultDetail0.get("dc_bank_number").asText() == toDcBankNumber1
        resultDetail0.get("renewable_energy_id").asText() == "TEST-EVT-00001"
        resultDetail0.get("nft_token_id").asText() == nftTokenId1
        resultDetail0.get("transfer_amount").asText() == "0"
        resultDetail0.get("settlement_type").asText() == "immediate"
        resultDetail0.get("scheduled_day") == null

        detailRows.get(1).get("task_id").toString() == taskId
        detailRows.get(1).get("line_number").toString() == "2"
        detailRows.get(1).get("task_detail_status").toString() == "completed"
        detailRows.get(1).get("task_order_detail") != null
        def resultDetail1 = mapper.readTree(detailRows.get(1).get("task_result_detail").toString())
        resultDetail1.get("task_result").asText() == "success"
        resultDetail1.get("error_message").asText() == ""
        resultDetail1.get("file_type").asText() == "nft_transfer"
        resultDetail1.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail1.get("renewable_energy_id").asText() == "TEST-EVT-00003"
        resultDetail1.get("nft_token_id").asText() == nftTokenId3
        resultDetail1.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail1.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail1.get("scheduled_day").asText() == "10"

        detailRows.get(2).get("task_id").toString() == taskId
        detailRows.get(2).get("line_number").toString() == "3"
        detailRows.get(2).get("task_detail_status").toString() == "completed"
        detailRows.get(2).get("task_order_detail") != null
        def resultDetail2 = mapper.readTree(detailRows.get(2).get("task_result_detail").toString())
        resultDetail2.get("task_result").asText() == "success"
        resultDetail2.get("error_message").asText() == ""
        resultDetail2.get("file_type").asText() == "nft_transfer"
        resultDetail2.get("dc_bank_number").asText() == toDcBankNumber3
        resultDetail2.get("renewable_energy_id").asText() == "TEST-EVT-00003"
        resultDetail2.get("nft_token_id").asText() == nftTokenId3
        resultDetail2.get("transfer_amount").asText() == transferAmount3.toString()
        resultDetail2.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail2.get("scheduled_day").asText() == "10"

        detailRows.get(100).get("task_id").toString() == taskId
        detailRows.get(100).get("line_number").toString() == "101"
        detailRows.get(100).get("task_detail_status").toString() == "completed"
        detailRows.get(100).get("task_order_detail") != null
        def resultDetail100 = mapper.readTree(detailRows.get(100).get("task_result_detail").toString())
        resultDetail100.get("task_result").asText() == "success"
        resultDetail100.get("error_message").asText() == ""
        resultDetail100.get("file_type").asText() == "nft_transfer"
        resultDetail100.get("dc_bank_number").asText() == toDcBankNumber4
        resultDetail100.get("renewable_energy_id").asText() == "TEST-EVT-00004"
        resultDetail100.get("nft_token_id").asText() == nftTokenId4
        resultDetail100.get("transfer_amount").asText() == transferAmount4.toString()
        resultDetail100.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail100.get("scheduled_day").asText() == "10"

        detailRows.get(101).get("task_id").toString() == taskId
        detailRows.get(101).get("line_number").toString() == "102"
        detailRows.get(101).get("task_detail_status").toString() == "completed"
        detailRows.get(101).get("task_order_detail") != null
        def resultDetail101 = mapper.readTree(detailRows.get(101).get("task_result_detail").toString())
        resultDetail101.get("task_result").asText() == "success"
        resultDetail101.get("error_message").asText() == ""
        resultDetail101.get("file_type").asText() == "nft_transfer"
        resultDetail101.get("dc_bank_number").asText() == toDcBankNumber4
        resultDetail101.get("renewable_energy_id").asText() == "TEST-EVT-00004"
        resultDetail101.get("nft_token_id").asText() == nftTokenId4
        resultDetail101.get("transfer_amount").asText() == transferAmount4.toString()
        resultDetail101.get("settlement_type").asText() == "monthly_scheduled"
        resultDetail101.get("scheduled_day").asText() == "10"

        //オートチャージファイルの確認
        List<S3Object> s3ObjectList = AdhocHelper.getListS3Object("output/service_user/file_task/nft_transfer/")
        s3ObjectList.size() == 1
        String keyName = s3ObjectList.get(0).key()
        List<String> fileValues = AdhocHelper.getS3ObjectString(keyName)

        fileValues.size() == 102
        fileValues.get(0) == '"EUE4400","insufficient dc account balance.","nft_transfer:renewable_energy_token","DC001-2222-1102-1","TEST-EVT-00001","31000001111100000111110000011111","0","immediate","0"'
        fileValues.get(1) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","TEST-EVT-00003","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'
        fileValues.get(2) == '"success","","nft_transfer:renewable_energy_token","DC001-3333-1103-1","TEST-EVT-00003","3100000abcdefghijklmnoPQRSTUVWXY","100","monthly_scheduled","10"'
        fileValues.get(100) == '"success","","nft_transfer:renewable_energy_token","DC001-4444-1104-1","TEST-EVT-00004","31000004444400000444440000044444","100","monthly_scheduled","10"'
        fileValues.get(101) == '"success","","nft_transfer:renewable_energy_token","DC001-4444-1104-1","TEST-EVT-00004","31000004444400000444440000044444","100","monthly_scheduled","10"'

    }
}
