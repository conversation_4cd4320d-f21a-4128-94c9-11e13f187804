package com.decurret_dcp.dcjpy.bpm.batch.nft.config.nftTransferFileCheck

import com.decurret_dcp.dcjpy.bpm.batch.helper.AdhocHelper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.springframework.batch.core.Job
import org.springframework.batch.core.JobParametersBuilder
import org.springframework.batch.core.scope.context.StepSynchronizationManager
import org.springframework.batch.test.JobLauncherTestUtils
import org.springframework.batch.test.MetaDataInstanceFactory
import org.springframework.batch.test.context.SpringBatchTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.batch.JobLauncherApplicationRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper
import software.amazon.awssdk.services.s3.model.S3Object
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@SpringBatchTest
@SpringBootTest
@ContextConfiguration
class NftTransferFileCheckBatchConfigS3DisConnectionsSpec extends Specification {

    static Sql sql

    @LocalServerPort
    int applicationPort

    @Shared
    WireMockServer wiremockBpm

    @MockBean
    JobLauncherApplicationRunner jobLauncherApplicationRunner

    @Autowired
    Job transferNftJob

    @Autowired
    JobLauncherTestUtils jobLauncherTestUtils

    static String LOCAL_BUCKETS_NAME = "localbucket"

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        registry.add("bpm.batch.nft.user-type", () -> "service_user")
        registry.add("bpm.batch.nft.job-type", () -> "nft-transfer")
        registry.add("local.server.port", () -> "8080")

        // S3 に接続できないようにする
        registry.add("bpm.batch.nft.s3.local-endpoint", () -> "http://localhost:9999")

    }

    def setupSpec() {
        wiremockBpm = new WireMockServer(options().port(8180))
        wiremockBpm.start()
    }

    def cleanupSpec() {
        wiremockBpm.stop()
        AdhocHelper.cleanupSpec()
    }

    def setup() {
        // Do nothing.
    }

    def cleanup() {
        sql.execute("""
            DELETE FROM service_user_file_task
        """)
        sql.execute("""
            DELETE FROM service_user_file_task_detail
        """)
        sql.execute("""
            DELETE FROM batch_step_execution_context
        """)
        sql.execute("""
            DELETE FROM batch_step_execution
        """)

        wiremockBpm.resetRequests()
        AdhocHelper.deleteAllS3Object()
    }

    static void insertServiceUserFileTask(
            String taskId,
            String taskStatus,
            String orderedAt,
            String signInId,
            String fileName,
            String taskOrderDetail
    ) {

        sql.execute("""
            INSERT INTO service_user_file_task
            (task_id, task_type, task_status, ordered_at, sign_in_id, file_name, task_order_detail, result_bucket, result_file_path)
            VALUES (
                '$taskId', 'nft_transfer', '$taskStatus', '$orderedAt', '$signInId', '$fileName', '$taskOrderDetail', NULL, NULL
            )
        """)

    }

    def "NftTransferFileCheck_9_10_S3に接続できない場合、NFT発行せず処理を終了すること。"() {
        setup:
        //テストデータの設定
        def s3Key = "/input/service_user/file_task/nft_transfer/2024/02/20240201-120000900_9_10.csv"
        def filePath = "src/test/resources/file_task/service_user/nft_transfer/nftTransferBatch_1_1.csv"

        def taskId = "9_10"
        def taskStatus = "initialized"
        def orderedAt = "2024-02-01T05:06:07.801+09:00"
        def signInId = "SID11AA-1011"
        def fileName = "20240201-120000900_9_10.csv"
        def taskOrderDetail = '{"bucket_name" : "localbucket", "file_path" : "/input/service_user/file_task/nft_transfer/2024/02/20240201-120000900_9_10.csv"}'

        //バッチ処理で使用するファイルをPut
        AdhocHelper.putS3Object(s3Key, filePath)

        //ServiceUserFileTaskテーブルへの登録
        insertServiceUserFileTask(taskId, taskStatus, orderedAt, signInId, fileName, taskOrderDetail)

        def stepExecution = MetaDataInstanceFactory.createStepExecution()
        StepSynchronizationManager.close()
        StepSynchronizationManager.register(stepExecution)

        def jobParameters = new JobParametersBuilder()
                .addString("s3bucket", LOCAL_BUCKETS_NAME)
                .addString("s3key", s3Key)
                .toJobParameters()

        jobLauncherTestUtils.setJob(transferNftJob)

        when:
        def jobExecution = jobLauncherTestUtils.launchJob(jobParameters)

        then:
        jobExecution.getExitStatus().getExitCode() == "FAILED"
        def step_row = sql.rows("""
            SELECT
                *
            FROM
                batch_step_execution
            ORDER BY
                start_time
        """)
        step_row.size() == 2
        step_row.get(0).get("step_name").toString() == "StartFileTask_NftTransferFileCheck"
        step_row.get(0).get("exit_code").toString() == "COMPLETED"
        step_row.get(1).get("step_name").toString() == "CheckFileFormatStep_NftTransferFileCheck"
        step_row.get(1).get("exit_code").toString() == "FAILED"

        def rows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task
        """)
        rows.size() == 1
        rows.get(0).get("task_id").toString() == taskId
        rows.get(0).get("task_type").toString() == "nft_transfer"
        rows.get(0).get("task_status").toString() == "failed"
        rows.get(0).get("result_bucket") == null
        rows.get(0).get("result_file_path") == null
        rows.get(0).get("task_result_detail") == null

        def detailRows = sql.rows("""
            SELECT
                *
            FROM
                service_user_file_task_detail
            ORDER BY
                line_number
        """)
        detailRows.size() == 0
    }

}
