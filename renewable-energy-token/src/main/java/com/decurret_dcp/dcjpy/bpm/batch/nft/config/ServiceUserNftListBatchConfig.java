package com.decurret_dcp.dcjpy.bpm.batch.nft.config;

import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.listener.NftListListener;
import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.processor.NftListResultProcessor;
import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.reader.ServiceUserNftListReader;
import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.service.ServiceUserNftListSupport;
import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.tasklet.CompleteServiceUserFileTaskTasklet;
import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.tasklet.StartServiceUserFileTaskTasklet;
import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.value.RenewableEnergyTokenDetail;
import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.writer.ServiceUserS3UploadWriter;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.RefreshParametersIncrementer;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.ServiceUserFileTaskManager;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.bpmapi.BpmServerApi;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.repository.ServiceUserFileTaskRepository;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.repository.TransactionSupport;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskId;

import lombok.RequiredArgsConstructor;
import software.amazon.awssdk.services.s3.S3Client;

/**
 * サービスユーザ用 NFT 照会バッチ処理の構成定義。
 */
@RequiredArgsConstructor
@Conditional({ BatchJobCondition.NftListMode.class, UserTypeCondition.ServiceUserBatchMode.class })
@Configuration
public class ServiceUserNftListBatchConfig {

    private final JobBuilderFactory jobFactory;

    private final StepBuilderFactory stepFactory;

    /**
     * サービスユーザ用の NFT 照会を行う job 定義。
     *
     * @param startTasklet {@link ServiceUserNftListBatchConfig#startFileTaskTasklet(ServiceUserFileTaskManager, String)}
     * @param nftListReader {@link ServiceUserNftListBatchConfig#serviceUserNftListReader(ServiceUserNftListSupport)}
     * @param s3UploadWriter {@link ServiceUserNftListBatchConfig#s3UploadWriter(RenewableEnergyTokenApplicationProperty, S3Client, TransactionSupport, ServiceUserFileTaskRepository, String)}
     * @param completeTaskTasklet {@link ServiceUserNftListBatchConfig#completeFileTaskTasklet(RenewableEnergyTokenApplicationProperty, ServiceUserFileTaskManager, String)}
     *
     * @return job
     */
    @Bean
    public Job fetchNftListJob(
            StartServiceUserFileTaskTasklet startTasklet,
            ServiceUserNftListReader nftListReader,
            ServiceUserS3UploadWriter s3UploadWriter,
            CompleteServiceUserFileTaskTasklet completeTaskTasklet
    ) {
        // service_user_file_task テーブルの該当レコードを処理中に更新する step 定義
        Step startFileTaskStep = this.stepFactory.get("StartFileTask_ServiceUserNftList").tasklet(startTasklet).build();

        // NFT照会を行う step 定義
        Step nftListStep = this.stepFactory.get("ExportResultFileToS3_ServiceUserNftList")
                .<RenewableEnergyTokenDetail, String>chunk(100)
                .reader(nftListReader)
                .processor(new NftListResultProcessor())
                .writer(s3UploadWriter)
                .listener(new NftListListener())
                .build();

        // service_user_file_task テーブルの該当レコードの状態を更新する step 定義
        Step completeFileTaskStep = this.stepFactory.get("CompleteFileTask_ServiceUserNftList").tasklet(
                completeTaskTasklet).build();

        /*
            1. startFileTaskStep
                -> 成功時は 2,
                -> 失敗時は処理終了
            2. nftListStep
                -> 成功時は 3,
                -> 失敗時は処理終了
            3. completeFileTaskStep
                -> 終了
         */
        return this.jobFactory.get("Batch_ServiceUserNftList")
                .incrementer(new RefreshParametersIncrementer()) // TODO 同一パラメータでの実行について整理する必要あり。
                // FileTask のロック成功、レコードのフォーマットチェックを実施
                // FileTask のロックが失敗、もしくは例外エラーが発生した場合は処理終了
                .start(startFileTaskStep).on(ExitStatus.COMPLETED.getExitCode()).to(nftListStep)
                .start(startFileTaskStep).on(ExitStatus.FAILED.getExitCode()).fail()

                // 全体処理結果の DB 反映を実施
                .from(nftListStep).on(ExitStatus.COMPLETED.getExitCode()).to(completeFileTaskStep)
                .from(nftListStep).on(ExitStatus.FAILED.getExitCode()).fail()
                .end()
                .build();
    }

    @Bean
    public ServiceUserNftListReader serviceUserNftListReader(ServiceUserNftListSupport support) {
        return new ServiceUserNftListReader(support);
    }

    @Bean
    public ServiceUserNftListSupport serviceUserNftListSupport(
            BpmServerApi bpmServerApi, ServiceUserFileTaskRepository serviceUserFileTaskRepository
    ) {
        return new ServiceUserNftListSupport(bpmServerApi, serviceUserFileTaskRepository);
    }

    @Bean
    @StepScope
    public StartServiceUserFileTaskTasklet startFileTaskTasklet(
            ServiceUserFileTaskManager taskManager,
            @Value("#{jobParameters[taskId]}") String taskId
    ) {
        FileTaskId fileTaskId = FileTaskId.of(taskId);
        return new StartServiceUserFileTaskTasklet(taskManager, fileTaskId);
    }

    @Bean
    @StepScope
    public ServiceUserS3UploadWriter s3UploadWriter(
            RenewableEnergyTokenApplicationProperty property,
            S3Client s3Client,
            TransactionSupport transactionSupport,
            ServiceUserFileTaskRepository repository,
            @Value("#{jobParameters[taskId]}") String taskId
    ) {
        FileTaskId fileTaskId = FileTaskId.of(taskId);
        return new ServiceUserS3UploadWriter(property, s3Client, transactionSupport, repository, fileTaskId);
    }

    @Bean
    @StepScope
    public CompleteServiceUserFileTaskTasklet completeFileTaskTasklet(
            RenewableEnergyTokenApplicationProperty property,
            ServiceUserFileTaskManager taskManager,
            @Value("#{jobParameters[taskId]}") String taskId
    ) {
        FileTaskId fileTaskId = FileTaskId.of(taskId);
        return new CompleteServiceUserFileTaskTasklet(property, taskManager, fileTaskId);
    }
}
