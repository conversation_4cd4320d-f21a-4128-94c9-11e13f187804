package com.decurret_dcp.dcjpy.bpm.batch.nft.domain.value.output;

import com.decurret_dcp.dcjpy.bpm.batch.nft.domain.value.input.NftTransferInputValue;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.bpmapi.command.BpmApiCheckNftTransactionCommand;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.bpmapi.command.BpmApiCheckTransferCommand;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.MiscValue;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class NftCheckTransferInputValueWithServiceAccount {

    public final FileTaskId taskId;

    public final Integer lineNumber;

    public final NftTransferInputValue inputValue;

    public final DcBankNumber serviceAccountDcBankNumber;

    public BpmApiCheckTransferCommand initCheckTransferCommand() {
        MiscValue miscValue = this.inputValue.miscValue();

        return BpmApiCheckTransferCommand.builder()
                .transferAmount(this.inputValue.transferAmount)
                .fromDcBankNumber(this.inputValue.toDcBankNumber)
                .toDcBankNumber(this.serviceAccountDcBankNumber)
                .extraInfo(miscValue)
                .build();
    }

    public BpmApiCheckNftTransactionCommand initCheckNftCommand() {
        return BpmApiCheckNftTransactionCommand.builder()
                .nftId("renewable_energy_token")
                .toDcBankNumber(this.inputValue.toDcBankNumber)
                .nftTokenId(this.inputValue.nftTokenId.toBpmFormat())
                .build();
    }
}
