package com.decurret_dcp.dcjpy.bpm.batch.nft.domain.value.input;

import java.util.Map;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.NftFileType;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.DcBankNumber;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonDeserialize(builder = NftMintInputValue.Builder.class)
public class NftMintInputValue {

    /** ファイル種類。 */
    @JsonProperty("file_type")
    public final NftFileType fileType;

    /** 環境価値トークンID. */
    @JsonProperty("renewable_energy_id")
    public final String renewableEnergyId;

    /** NFT所有者のDC口座番号 */
    @JsonProperty("nft_owner")
    public final DcBankNumber nftOwner;

    /** 環境価値ID. */
    @JsonProperty("energy_id")
    public final String energyId;

    /** 環境価値種類。 */
    @JsonProperty("energy_type")
    public final String energyType;

    /** 環境価値割当量。 */
    @JsonProperty("energy_value")
    public final String energyValue;

    /** 割当量単位。 */
    @JsonProperty("value_unit")
    public final String valueUnit;

    /** 有効期限。 */
    @JsonProperty("expires_at")
    public final String expiresAt;

    public Map<String, Object> toMetadataDetail() {
        return Map.ofEntries(
                Map.entry("renewable_energy_id", this.renewableEnergyId),
                Map.entry("energy_id", this.energyId),
                Map.entry("energy_type", this.energyType),
                Map.entry("energy_value", this.energyValue),
                Map.entry("value_unit", this.valueUnit),
                Map.entry("expires_at", this.expiresAt)
        );
    }
}
