package com.decurret_dcp.dcjpy.bpm.batch.nft.domain.tasklet;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.ServiceUserFileTaskManager;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskId;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * フォーマット不備があった場合に、受付済みだったレコードを未処理に更新する。
 */
@RequiredArgsConstructor
@Slf4j
public class ServiceUserUpdateFormatFailureTasklet implements Tasklet {

    private final ServiceUserFileTaskManager taskManager;

    private final FileTaskId taskId;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        try {
            this.taskManager.toFormatFailure(this.taskId);
        } catch (RuntimeException exc) {
            log.error(
                    "[DO MUST RECOVERY ACTION] Failed to update service_user_file_task to not_processed. taskId = {}",
                    this.taskId.getValue(), exc);
            throw exc;
        }

        return RepeatStatus.FINISHED;
    }
}
