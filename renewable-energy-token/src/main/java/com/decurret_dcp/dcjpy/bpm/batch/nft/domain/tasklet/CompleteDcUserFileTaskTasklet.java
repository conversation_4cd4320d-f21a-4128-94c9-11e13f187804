package com.decurret_dcp.dcjpy.bpm.batch.nft.domain.tasklet;

import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.repeat.RepeatStatus;

import com.decurret_dcp.dcjpy.bpm.batch.nft.config.RenewableEnergyTokenApplicationProperty;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.DcUserFileTaskManager;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.ExecutionContextKey;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskId;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskStatus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
public class CompleteDcUserFileTaskTasklet implements Tasklet {

    private final RenewableEnergyTokenApplicationProperty property;

    private final DcUserFileTaskManager taskManager;

    private final FileTaskId taskId;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        StepContext stepContext = chunkContext.getStepContext();
        StepExecution stepExecution = stepContext.getStepExecution();
        JobExecution jobExecution = stepExecution.getJobExecution();
        ExecutionContext executionContext = jobExecution.getExecutionContext();

        FileTaskStatus beforeStatus = (FileTaskStatus) executionContext.get(ExecutionContextKey.FILE_TASK_STATUS);
        FileTaskStatus toStatus = (beforeStatus == FileTaskStatus.ACCEPT) ? FileTaskStatus.COMPLETED : beforeStatus;

        try {
            this.taskManager.complete(this.taskId, toStatus, property.s3.outputBucketName);
        } catch (RuntimeException exc) {
            log.error("Failed to update service_user_file_task to failed. taskId =" + this.taskId.getValue(), exc);
            throw exc;
        }

        return RepeatStatus.FINISHED;
    }
}
