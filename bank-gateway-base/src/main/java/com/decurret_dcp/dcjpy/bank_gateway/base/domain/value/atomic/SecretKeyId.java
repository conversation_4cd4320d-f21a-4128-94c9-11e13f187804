package com.decurret_dcp.dcjpy.bank_gateway.base.domain.value.atomic;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class SecretKeyId {

    private static final String PREFIX = "dcjpy/bank_gw/ib_token/";

    private final String value;

    private SecretKeyId(String value) {
        this.value = PREFIX + value;
    }

    public static SecretKeyId of(DcBankNumber dcBankNumber) {
        return new SecretKeyId(dcBankNumber.getValue());
    }

    public String getValue() {
        return this.value;
    }
}
