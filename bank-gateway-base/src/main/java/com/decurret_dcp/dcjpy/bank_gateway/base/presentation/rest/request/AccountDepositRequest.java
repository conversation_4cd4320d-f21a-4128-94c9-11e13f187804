package com.decurret_dcp.dcjpy.bank_gateway.base.presentation.rest.request;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString()
@EqualsAndHashCode
public class AccountDepositRequest {

    @com.decurret_dcp.dcjpy.bank_gateway.base.presentation.annotation.validation.RequestId
    public String requestId;

    @com.decurret_dcp.dcjpy.bank_gateway.base.presentation.annotation.validation.Amount
    public Long amount;

}
