package com.decurret_dcp.dcjpy.bank_gateway.base.domain.value.atomic;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 残高を表すクラス。
 */
@ToString
@EqualsAndHashCode
@JsonSerialize(using = BalanceJson.Serializer.class)
@JsonDeserialize(using = BalanceJson.Deserializer.class)
public class Balance {

    private final Long value;

    private Balance(Long value) {
        this.value = value;
    }

    public static Balance of(Long value) {
        if (value.longValue() < 0L) {
            throw new IllegalArgumentException("value must be positive. value = " + value);
        }

        return new Balance(value);
    }

    public Long getValue() {
        return this.value;
    }
}
