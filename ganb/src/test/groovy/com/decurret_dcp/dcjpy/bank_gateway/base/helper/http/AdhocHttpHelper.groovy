package com.decurret_dcp.dcjpy.bank_gateway.base.helper.http


import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.http.NameValuePair
import org.apache.http.client.HttpClient
import org.apache.http.client.methods.*
import org.apache.http.client.utils.URIBuilder
import org.apache.http.entity.ContentType
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.message.BasicNameValuePair
import org.apache.http.util.EntityUtils

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class AdhocHttpHelper {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    static ObjectMapper mapper = new ObjectMapper()

    private final int port

    AdhocHttpHelper(int port) {
        this.port = port
    }

    public HttpGetBuilder httpGet(String path) {
        return new HttpGetBuilder(path)
    }

    public HttpPostBuilder httpPost(String path) {
        return new HttpPostBuilder(path)
    }

    public HttpPutBuilder httpPut(String path) {
        return new HttpPutBuilder(path)
    }

    protected abstract class HttpBuilder<BUILDER extends HttpBuilder<BUILDER, HTTP_BASE>, HTTP_BASE extends HttpRequestBase> {

        private final String path

        private String authorization

        private String serviceId

        private final List<NameValuePair> valuePairs

        protected HttpBuilder(String path) {
            this.path = path
            this.authorization = null
            this.serviceId = null
            this.valuePairs = new ArrayList<>()
        }

        public BUILDER addQuery(String key, Object value) {
            def pair = new BasicNameValuePair(key, value.toString())
            return (BUILDER) this.addQuery(pair)
        }

        public BUILDER addQuery(BasicNameValuePair... pairs) {
            for (def pair : pairs) {
                this.valuePairs.add(pair)
            }

            return (BUILDER) this
        }

        public BUILDER setToken(String authorization) {
            this.authorization = "Bearer ${authorization}"
            return (BUILDER) this
        }

        public BUILDER setServiceId(String serviceId) {
            this.serviceId = serviceId
            return (BUILDER) this
        }

        public abstract HTTP_BASE initHttpBase(URIBuilder builder)

        public HTTP_BASE build() {
            URIBuilder builder = initURIBuilder(this.path, this.valuePairs)
            def httpBase = this.initHttpBase(builder)
            httpBase.setHeader("Content-Type", "application/json")
            if (this.authorization != null) {
                httpBase.setHeader("Authorization", this.authorization)
            }
            if (this.serviceId != null) {
                httpBase.setHeader("X-DCJPY-SERVICE-ID", this.serviceId)
            }

            return (HTTP_BASE) httpBase
        }
    }

    private URIBuilder initURIBuilder(String path, List<NameValuePair> valuePairs) {
        def builder = new URIBuilder()
                .setPath(path)
                .setScheme("http")
                .setHost("localhost")
                .setPort(this.port)

        if (valuePairs.size() > 0) {
            builder.addParameters(valuePairs)
        }

        return builder
    }

    private class HttpGetBuilder extends HttpBuilder<HttpGetBuilder, HttpGet> {

        private HttpGetBuilder(String path) {
            super(path)
        }

        @Override
        HttpGet initHttpBase(URIBuilder builder) {
            return new HttpGet(builder.build())
        }
    }

    private class HttpPostBuilder extends HttpBuilder<HttpPostBuilder, HttpPost> {

        private String requestBody

        private HttpPostBuilder(String path) {
            super(path)
        }

        public HttpPostBuilder setBody(String requestBody) {
            this.requestBody = requestBody
            return this
        }

        @Override
        HttpPost initHttpBase(URIBuilder builder) {
            return new HttpPost(builder.build())
        }

        public HttpPost build() {
            HttpPost httpPost = (HttpPost) super.build()
            if (this.requestBody != null) {
                httpPost.setEntity(new StringEntity(this.requestBody, ContentType.APPLICATION_JSON))
            }

            return httpPost
        }
    }

    private class HttpPutBuilder extends HttpBuilder<HttpPutBuilder, HttpPut> {

        private String requestBody

        private HttpPutBuilder(String path) {
            super(path)
        }

        public HttpPutBuilder setBody(String requestBody) {
            this.requestBody = requestBody
            return this
        }

        @Override
        HttpPut initHttpBase(URIBuilder builder) {
            return new HttpPut(builder.build())
        }

        public HttpPut build() {
            def httpPut = (HttpPut) super.build()
            if (this.requestBody != null) {
                httpPut.setEntity(new StringEntity(this.requestBody, ContentType.APPLICATION_JSON))
            }

            return httpPut
        }
    }

    static JsonNode toJson(CloseableHttpResponse response) {
        String str = EntityUtils.toString(response.getEntity())
        return mapper.readTree(str)
    }

    static LocalDateTime toUtcDateTime(String jstTime) {
        // whereで使用している変数を使用して、DBインサートとレスポンスの値の比較するためにJSTからUTCへ変換を行う
        // DBに登録される時間：UTC
        // レスポンスで返却される時間：JST
        return LocalDateTime.parse(jstTime, DateTimeFormatter.ISO_DATE_TIME).minusHours(9)
    }
}
