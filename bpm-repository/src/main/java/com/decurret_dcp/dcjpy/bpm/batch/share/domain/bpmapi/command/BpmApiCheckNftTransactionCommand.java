package com.decurret_dcp.dcjpy.bpm.batch.share.domain.bpmapi.command;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.DcBankNumber;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class BpmApiCheckNftTransactionCommand {

    public final String nftId;

    public final DcBankNumber toDcBankNumber;

    public final String nftTokenId;
}
