package com.decurret_dcp.dcjpy.bpm.batch.share.domain.bpmapi.result;

import java.util.Map;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class NftDetail {

    public final String nftTokenId;

    public final BpmApiNftDcAccountResult ownerAccount;

    public final BpmApiNftDcAccountResult mintAccount;

    public final BpmApiNftDcAccountResult previousAccount;

    public final String tokenStatus;

    public final Map<String, Object> metadataDetail;

    public final String metadataHash;
}
