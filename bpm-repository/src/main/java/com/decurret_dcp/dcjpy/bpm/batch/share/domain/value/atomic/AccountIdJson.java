package com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

class AccountIdJson {

    static class Serializer extends JsonSerializer<AccountId> {

        @Override
        public void serialize(AccountId value, JsonGenerator generator, SerializerProvider provider)
                throws IOException {
            if (value == null) {
                return;
            }

            generator.writeString(value.getValue());
        }
    }

    static class Deserializer extends JsonDeserializer<AccountId> {

        @Override
        public AccountId deserialize(<PERSON>son<PERSON>ars<PERSON> parser, DeserializationContext context) throws IOException {
            String value = parser.getValueAsString();
            return AccountId.of(value);
        }
    }
}
