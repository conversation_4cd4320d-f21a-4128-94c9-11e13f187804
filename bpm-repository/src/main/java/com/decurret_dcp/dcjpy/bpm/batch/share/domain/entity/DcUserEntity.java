/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bpm.batch.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.DcUserType;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.RoleId;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.UserStatus;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ : 個人 / 法人ユーザを扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_user")
public class DcUserEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** ユーザ名. */
    @Column(name = "user_name")
    public final String userName;

    /** DCユーザ種別 : individual : 個人ユーザ
company_owner : 法人ユーザ (アカウント管理者)
company : 法人ユーザ (アカウント管理者以外). */
    @Column(name = "dc_user_type")
    public final DcUserType dcUserType;

    /** ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。. */
    @Column(name = "user_status")
    public final UserStatus userStatus;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** ロールID. */
    @Column(name = "role_id")
    public final RoleId roleId;

    /** 登録日時. */
    @Column(name = "registered_at")
    public final AppTimeStamp registeredAt;

    /** 無効日時. */
    @Column(name = "terminated_at")
    public final AppTimeStamp terminatedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param userName ユーザ名
     * @param dcUserType DCユーザ種別 : individual : 個人ユーザ
company_owner : 法人ユーザ (アカウント管理者)
company : 法人ユーザ (アカウント管理者以外)
     * @param userStatus ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param roleId ロールID
     * @param registeredAt 登録日時
     * @param terminatedAt 無効日時
     */
    DcUserEntity(
        SignInId signInId,
        String userName,
        DcUserType dcUserType,
        UserStatus userStatus,
        ServiceId serviceId,
        RoleId roleId,
        AppTimeStamp registeredAt,
        AppTimeStamp terminatedAt
    ) {
        this.signInId = signInId;
        this.userName = userName;
        this.dcUserType = dcUserType;
        this.userStatus = userStatus;
        this.serviceId = serviceId;
        this.roleId = roleId;
        this.registeredAt = registeredAt;
        this.terminatedAt = terminatedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserEntity オブジェクト
     */
    protected DcUserEntity(DcUserEntity org) {
        this.signInId = org.signInId;
        this.userName = org.userName;
        this.dcUserType = org.dcUserType;
        this.userStatus = org.userStatus;
        this.serviceId = org.serviceId;
        this.roleId = org.roleId;
        this.registeredAt = org.registeredAt;
        this.terminatedAt = org.terminatedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("userName=").append(this.userName).append(", ")
                .append("dcUserType=").append(this.dcUserType).append(", ")
                .append("userStatus=").append(this.userStatus).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("roleId=").append(this.roleId).append(", ")
                .append("registeredAt=").append(this.registeredAt).append(", ")
                .append("terminatedAt=").append(this.terminatedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.userName, 
                this.dcUserType, 
                this.userStatus, 
                this.serviceId, 
                this.roleId, 
                this.registeredAt, 
                this.terminatedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserEntity other = (DcUserEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.userName, other.userName)
                && Objects.equals(this.dcUserType, other.dcUserType)
                && Objects.equals(this.userStatus, other.userStatus)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.roleId, other.roleId)
                && Objects.equals(this.registeredAt, other.registeredAt)
                && Objects.equals(this.terminatedAt, other.terminatedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** ユーザ名. */
        private String userName;

        /** DCユーザ種別 : individual : 個人ユーザ
company_owner : 法人ユーザ (アカウント管理者)
company : 法人ユーザ (アカウント管理者以外). */
        private DcUserType dcUserType;

        /** ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。. */
        private UserStatus userStatus;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** ロールID. */
        private RoleId roleId;

        /** 登録日時. */
        private AppTimeStamp registeredAt;

        /** 無効日時. */
        private AppTimeStamp terminatedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserEntity object
         */
        public DcUserEntity build() {
            return new DcUserEntity(
                    this.signInId, 
                    this.userName, 
                    this.dcUserType, 
                    this.userStatus, 
                    this.serviceId, 
                    this.roleId, 
                    this.registeredAt, 
                    this.terminatedAt
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set userName.
         *
         * @param userName ユーザ名
         * @return this builder
         */
        public Builder userName(String userName) {
            this.userName = userName;
            return this;
        }

        /**
         * Set dcUserType.
         *
         * @param dcUserType DCユーザ種別 : individual : 個人ユーザ
company_owner : 法人ユーザ (アカウント管理者)
company : 法人ユーザ (アカウント管理者以外)
         * @return this builder
         */
        public Builder dcUserType(DcUserType dcUserType) {
            this.dcUserType = dcUserType;
            return this;
        }

        /**
         * Set userStatus.
         *
         * @param userStatus ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。
         * @return this builder
         */
        public Builder userStatus(UserStatus userStatus) {
            this.userStatus = userStatus;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set roleId.
         *
         * @param roleId ロールID
         * @return this builder
         */
        public Builder roleId(RoleId roleId) {
            this.roleId = roleId;
            return this;
        }

        /**
         * Set registeredAt.
         *
         * @param registeredAt 登録日時
         * @return this builder
         */
        public Builder registeredAt(AppTimeStamp registeredAt) {
            this.registeredAt = registeredAt;
            return this;
        }

        /**
         * Set terminatedAt.
         *
         * @param terminatedAt 無効日時
         * @return this builder
         */
        public Builder terminatedAt(AppTimeStamp terminatedAt) {
            this.terminatedAt = terminatedAt;
            return this;
        }
    }
}
// @formatter:on
