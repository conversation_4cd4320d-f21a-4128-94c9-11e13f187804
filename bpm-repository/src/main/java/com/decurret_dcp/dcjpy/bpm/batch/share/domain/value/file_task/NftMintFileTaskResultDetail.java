package com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.file_task;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.NftFileType;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskResultDetail;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskResultDetailContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonPropertyOrder({ "task_result", "file_type", "nft_minter", "energy_id", "energy_type", "energy_value",
        "value_unit", "expires_at", "nft_owner", "nft_token_id", "minted_at" })
@JsonDeserialize(builder = NftMintFileTaskResultDetail.Builder.class)
public class NftMintFileTaskResultDetail implements FileTaskResultDetailContent {

    private static final TypeReference<NftMintFileTaskResultDetail> TYPE_REFERENCE = new TypeReference<>() {
    };

    /** 処理結果。 */
    @JsonProperty("task_result")
    public final String taskResult;

    /** ファイル種類。 */
    @JsonProperty("file_type")
    public final NftFileType fileType;

    /** NFT発行者のDC口座番号 */
    @JsonProperty("nft_minter")
    public final DcBankNumber nftMinter;

    /** 環境価値ID。 */
    @JsonProperty("energy_id")
    public final String energyId;

    /** 環境価値種類。 */
    @JsonProperty("energy_type")
    public final String energyType;

    /** 環境価値割当量。 */
    @JsonProperty("energy_value")
    public final String energyValue;

    /** 割当量単位。 */
    @JsonProperty("value_unit")
    public final String valueUnit;

    /** 有効期限。 */
    @JsonProperty("expires_at")
    public final String expiresAt;

    /** NFT所有者のDC口座番号。 */
    @JsonProperty("nft_owner")
    public final DcBankNumber nftOwner;

    /** NFTTokenID。 */
    @JsonProperty("nft_token_id")
    public final String nftTokenId;

    /** NFT発行日時。 */
    @JsonProperty("minted_at")
    public final AppTimeStamp mintedAt;

    public static NftMintFileTaskResultDetail of(FileTaskResultDetail taskResultDetail) {
        return taskResultDetail.getContent(TYPE_REFERENCE);
    }

    @Override
    public FileTaskResultDetail toTaskResultDetail() {
        return FileTaskResultDetail.of(this);
    }
}
