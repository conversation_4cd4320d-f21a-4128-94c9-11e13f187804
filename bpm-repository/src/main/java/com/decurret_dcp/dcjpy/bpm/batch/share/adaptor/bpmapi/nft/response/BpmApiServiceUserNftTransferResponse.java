package com.decurret_dcp.dcjpy.bpm.batch.share.adaptor.bpmapi.nft.response;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = BpmApiServiceUserNftTransferResponse.Builder.class)
public class BpmApiServiceUserNftTransferResponse {

    /** NFT トークン ID */
    @JsonProperty("nft_token_id")
    public final String nftTokenId;

    /** NFT移転元アカウント */
    @JsonProperty("from_account")
    public final DcAccountInfoResponse fromAccount;

    /** NFT移転先アカウント */
    @JsonProperty("to_account")
    public final DcAccountInfoResponse toAccount;

    /** メタデータ詳細 */
    @JsonProperty("metadata_detail")
    public final Map<String, Object> metadataDetail;

    /** メタデータハッシュ値 */
    @JsonProperty("metadata_hash")
    public final String metadataHash;

    @ToString
    @EqualsAndHashCode
    @lombok.Builder
    @JsonDeserialize(builder = DcAccountInfoResponse.Builder.class)
    public static class DcAccountInfoResponse {

        @JsonProperty("dc_bank_number")
        public final String dcBankNumber;

        @JsonProperty("account_name")
        public final String accountName;
    }
}
