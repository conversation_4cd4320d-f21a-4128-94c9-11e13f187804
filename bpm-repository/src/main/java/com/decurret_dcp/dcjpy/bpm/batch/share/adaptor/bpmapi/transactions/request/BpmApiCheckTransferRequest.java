package com.decurret_dcp.dcjpy.bpm.batch.share.adaptor.bpmapi.transactions.request;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.bpmapi.command.BpmApiCheckTransferCommand;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BpmApiCheckTransferRequest {

    public final BigInteger transferAmount;

    public final String fromDcBankNumber;

    public final String toDcBankNumber;

    public final String memo;

    public final ExtraInfo extraInfo;

    @ToString
    @EqualsAndHashCode
    @lombok.Builder(access = AccessLevel.PRIVATE)
    public static class ExtraInfo {

        public final String miscValue1;

        public final String miscValue2;
    }

    public static BpmApiCheckTransferRequest createBody(BpmApiCheckTransferCommand command) {
        BpmApiCheckTransferRequest.ExtraInfo extraInfoValue = null;
        if (command.extraInfo != null) {
            extraInfoValue = BpmApiCheckTransferRequest.ExtraInfo.builder()
                    .miscValue1(command.extraInfo.miscValue1)
                    .miscValue2(command.extraInfo.miscValue2)
                    .build();
        }

        return BpmApiCheckTransferRequest.builder()
                .transferAmount(command.transferAmount.getValue())
                .fromDcBankNumber(command.fromDcBankNumber.getValue())
                .toDcBankNumber(command.toDcBankNumber.getValue())
                .memo(command.memo)
                .extraInfo(extraInfoValue)
                .build();
    }
}
