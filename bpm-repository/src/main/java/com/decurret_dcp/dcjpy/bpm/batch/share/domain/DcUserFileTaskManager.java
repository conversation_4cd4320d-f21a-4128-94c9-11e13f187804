package com.decurret_dcp.dcjpy.bpm.batch.share.domain;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bpm.batch.share.config.BpmBatchApplicationProperty;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.entity.DcUserFileTaskEntity;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.repository.DcUserFileTaskRepository;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskId;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.FileTaskStatus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Component
@Transactional
@Slf4j
public class DcUserFileTaskManager {

    private final DcUserFileTaskRepository dcUserFileTaskRepository;

    /**
     * file_task を処理状態に更新する。既に処理中以降の場合は何もしない。
     *
     * @param taskId タスクID
     *
     * @return 処理中に更新できれば true
     */
    public boolean lock(FileTaskId taskId) {
        DcUserFileTaskEntity current = this.dcUserFileTaskRepository.lockTask(taskId);
        if (current == null) {
            log.error("Specified task is not found. taskId = {}", taskId.getValue());
            return false;
        }

        if (current.taskStatus != FileTaskStatus.INITIALIZED) {
            log.error("Specified task is after batched. {}", current);
            return false;
        }

        DcUserFileTaskEntity onProcessing = this.dcUserFileTaskRepository.onProcessing(taskId);
        log.info("Start to processing service user file task. taskId = {}, detail = {}", taskId, onProcessing);

        return true;
    }

    /**
     * 指定されたタスクを toStatus の状態に更新する。
     *
     * @param taskId タスクID
     * @param toStatus 更新後の状態
     */
    public void complete(FileTaskId taskId, FileTaskStatus toStatus, String outputBucketName) {
        DcUserFileTaskEntity fileTask = this.dcUserFileTaskRepository.findFileTask(taskId);

        String filePath = S3FilePathBuilder.initOutputPathForDcUser(fileTask);

        DcUserFileTaskEntity updating = DcUserFileTaskEntity.builder()
                .taskId(taskId)
                .taskStatus(toStatus)
                .resultBucket(outputBucketName)
                .resultFilePath(filePath)
                .completedAt(AppTimeStamp.now())
                .build();

        this.dcUserFileTaskRepository.updateProcessResult(updating);
        log.info("Complete serviceUserFileTask. taskId = {}", taskId.getValue());
    }
}
