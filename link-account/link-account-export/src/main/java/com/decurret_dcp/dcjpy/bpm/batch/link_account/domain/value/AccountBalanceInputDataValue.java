package com.decurret_dcp.dcjpy.bpm.batch.link_account.domain.value;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.AccountStatus;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.ZoneId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonDeserialize(builder = AccountBalanceInputDataValue.Builder.class)
public class AccountBalanceInputDataValue {

    /** 基準日 */
    @JsonProperty("target_date")
    public final String targetDate;

    /** バリデータID. */
    @JsonProperty("validator_id")
    public final ValidatorId validatorId;

    /** アカウントID. */
    @JsonProperty("account_id")
    public final AccountId accountId;

    /** アカウント名. */
    @JsonProperty("account_name")
    public final String accountName;

    /** アカウント状態. */
    @JsonProperty("account_status")
    public final AccountStatus accountStatus;

    /** ゾーンID. */
    @JsonProperty("zone_id")
    public final ZoneId zoneId;

    /** ゾーン名. */
    @JsonProperty("zone_name")
    public final String zoneName;

    /** 基準日アカウント残高. */
    @JsonProperty("balance")
    public final Balance balance;

    /** 利用申込日時. */
    @JsonProperty("applied_at")
    public final AppTimeStamp appliedAt;

    /** 利用確定日時. */
    @JsonProperty("registered_at")
    public final AppTimeStamp registeredAt;

    /** 解約申込日時. */
    @JsonProperty("terminating_at")
    public final AppTimeStamp terminatingAt;

    /** 解約確定日時. */
    @JsonProperty("terminated_at")
    public final AppTimeStamp terminatedAt;


}
