package com.decurret_dcp.dcjpy.bpm.batch.link_account.domain;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.bpm.batch.share.domain.value.atomic.AppTimeStamp;
import com.orangesignal.csv.CsvConfig;

public class AccountExportSupport {

    private AccountExportSupport() {
        // Do nothing.
    }

    // Jackson の CsvMapper では下記設定をマニュアル通りに実施しても、適切に動作しなかったため OrangeSignalCSV を利用
    // - 各カラムをダブルクォーテーションで囲む -> 設定しても、囲んだり、囲まないカラムが存在する
    // - null 値を別の値に置き換え -> 設定しても、カラム自体が存在しなくなる
    public static CsvConfig initCsvConfig() {
        CsvConfig csvConfig =
                new CsvConfig(CsvConfig.DEFAULT_SEPARATOR, CsvConfig.DEFAULT_QUOTE, CsvConfig.DEFAULT_ESCAPE);
        csvConfig.setNullString("");
        csvConfig.setLineSeparator("");

        return csvConfig;
    }

    public static AppTimeStamp targetDate(String targetDate) {
        if(!StringUtils.hasLength(targetDate)) {
            ZonedDateTime zonedDateTimeYesterday = ZonedDateTime.now(ZoneOffset.ofHours(9))
                    .minusDays(1L)
                    .withHour(0)
                    .withMinute(0)
                    .withSecond(0)
                    .withNano(0);
            return AppTimeStamp.of(zonedDateTimeYesterday);
        }

        return convertAppTimeStamp(targetDate);
    }

    public static AppTimeStamp convertAppTimeStamp(String targetDate) {
        LocalDate localDate = LocalDate.parse(targetDate, DateTimeFormatter.ISO_LOCAL_DATE);
        return AppTimeStamp.of(localDate.atStartOfDay(ZoneOffset.ofHours(9)));
    }

    public static String initLinkAccountExportPath(AppTimeStamp targetDateAt) {
        String year = targetDateAt.format(DateTimeFormatter.ofPattern("yyyy"));
        String month = targetDateAt.format(DateTimeFormatter.ofPattern("MM"));
        String fileNameDate = targetDateAt.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        return "link_account/export/"
                + year + "/"
                + month + "/"
                + fileNameDate + "_current_balance.csv";
    }
}
