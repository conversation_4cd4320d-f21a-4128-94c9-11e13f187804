package com.decurret_dcp.dcjpy.bpm.batch.link_account.domain.reader;

import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.file.LineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.batch.item.file.transform.IncorrectTokenCountException;
import org.springframework.batch.item.file.transform.LineTokenizer;
import org.springframework.beans.NotReadablePropertyException;
import org.springframework.core.io.Resource;
import org.springframework.validation.BindException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@StepScope
public class LineMapperWithLineNumber<RECORD> implements LineMapper<RECORD> {

    private final Resource resource;

    private final RecordMapper<RECORD> mapper;

    private final LineTokenizer tokenizer;

    public LineMapperWithLineNumber(Resource resource, RecordMapper<RECORD> mapper) {
        this.resource = resource;
        this.mapper = mapper;
        this.tokenizer = initTokenizer(mapper);
    }

    private static LineTokenizer initTokenizer(RecordMapper<?> mapper) {
        DelimitedLineTokenizer tokenizer = new DelimitedLineTokenizer();
        tokenizer.setNames(mapper.fieldNames());

        return tokenizer;
    }

    @Override
    public RECORD mapLine(String line, int lineNumber) {
        try {
            FieldSet fieldSet = this.tokenizer.tokenize(line);
            return this.mapper.mapFieldSet(fieldSet);
        } catch (IncorrectTokenCountException | NotReadablePropertyException | BindException exc) {
            throw new IllegalArgumentException(
                    String.format("Failed to parse line %d in %s", lineNumber, this.resource.getDescription()), exc);
        }
    }
}
