FROM bash:5.2.26

RUN apk --no-cache update && \
    apk add --no-cache aws-cli openssh coreutils tzdata

RUN apk add --no-cache bash jq

# 作業ディレクトリを指定
WORKDIR /app

COPY ./link-account/link-account-send/scripts/upload_account_list.sh .
COPY ./link-account/link-account-send/scripts/askpass.sh .
COPY ./link-account/gmo-sftp-credentials .

# アドホックテスト用のファイルをコピー
COPY ./link-account/bpm-local-env/sftp/.ssh/test_sftp .

ENTRYPOINT ["/bin/bash", "upload_account_list.sh"]