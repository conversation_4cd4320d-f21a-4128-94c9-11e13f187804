AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  transaction-recorder

Parameters:
  Env:
    Type: String
    AllowedValues:
      - local
      - prod
    Description: |
      Execution environment
  SourceStream:
    Type: String
    Default: __DUMMY__
    Description: DynamoDB Streams ARN
  SnsTopicArn:
    Type: String
    Description: Topic ARN for SNS
  SnsEndpoint:
    Type: String
    Default: ""
    Description: Endpoint for SNS

Conditions:
  IsProd: !Equals [ !Ref Env, "prod" ]

Resources:
  BcMonitoringStreamFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: bcmonitoring-stream
      Handler: dist/index.handler
      CodeUri: .
      Runtime: nodejs20.x
      Timeout: 60
      Environment:
        Variables:
          ENV: !Ref Env
          SOURCE_STREAM: !Ref SourceStream
          SNS_TOPIC_ARN: !Ref SnsTopicArn
          SNS_ENDPOINT: !Ref SnsEndpoint
      Tracing: Active
      Policies:
        - AWSLambdaBasicExecutionRole
        - AWSLambdaDynamoDBExecutionRole
        - Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - sns:Publish
              Resource: !Ref SnsTopicArn
            - Effect: Allow
              Action:
                - kms:Encrypt
                - kms:Decrypt
                - kms:ListGrants
                - kms:DescribeKey
                - kms:GenerateDataKey*
              Resource: "*"
      Events:
        Stream:
          Type: DynamoDB
          Properties:
            Stream: !Ref SourceStream
            Enabled: true
            BatchSize: 100
            StartingPosition: TRIM_HORIZON
